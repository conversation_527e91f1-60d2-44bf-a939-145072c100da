"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const dotenv = require("dotenv");
const path = require("path");
const helmet = require("helmet");
const cookieParser = require("cookie-parser");
const session = require("express-session");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const security_config_1 = require("./security/security.config");
dotenv.config();
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.useLogger(new common_1.Logger());
    app.use(helmet(security_config_1.SecurityConfig.getHelmetConfig()));
    app.use(cookieParser());
    app.use(session(security_config_1.SecurityConfig.getSessionConfig()));
    app.useStaticAssets(path.join(__dirname, '../../resource'));
    app.useStaticAssets(path.join(__dirname, '../../library'));
    app.useGlobalPipes(new common_1.ValidationPipe(security_config_1.SecurityConfig.getValidationConfig()));
    const config = new swagger_1.DocumentBuilder()
        .setTitle('Hassana APIs Document')
        .setVersion('1.0')
        .build();
    const documentFactory = () => swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('/v1/docs', app, documentFactory);
    app.enableCors(security_config_1.SecurityConfig.getCorsConfig());
    await app.listen(process.env.SERVER_PORT, () => {
        common_1.Logger.log("Server is running on port: " + process.env.SERVER_PORT);
    });
}
bootstrap();
//# sourceMappingURL=main.js.map