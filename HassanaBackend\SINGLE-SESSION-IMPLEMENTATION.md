# Single Session Per User Implementation

## Overview

This implementation ensures that each user can only have one active session at a time. When a user logs in from a new device/browser, their previous session is automatically invalidated.

## How It Works

### 1. Session Creation Process
```
User Login → Generate Session ID → Store in Redis → Invalidate Previous Session → Return JWT with Session ID
```

### 2. Session Validation Process
```
GraphQL Request → Extract JWT → Validate Session ID → Check Redis → Allow/Deny Request
```

### 3. Session Conflict Resolution
```
New Login → Detect Existing Session → Blacklist Old Token → Create New Session → Notify Frontend
```

## Backend Implementation

### 1. SessionService (`src/auth/session.service.ts`)

**Key Features:**
- **Session Creation**: `createUserSession(userId, tokenPayload, deviceInfo)`
- **Session Validation**: `validateUserSession(userId, sessionId, token)`
- **Session Termination**: `logoutUser(userId, token)`
- **Force Logout**: `forceLogoutUser(userId)` (admin function)
- **Token Blacklisting**: Automatic blacklisting of invalidated tokens

**Redis Storage Structure:**
```
user_session:{userId} → {
  sessionId: "session_1234567890_abc123",
  token: "jwt-token-here",
  userId: "user-id",
  loginTime: "2024-01-01T10:00:00Z",
  deviceInfo: { userAgent: "...", ip: "...", timestamp: "..." },
  isActive: "true"
}

blacklist:{token} → "true" (with TTL matching token expiry)
```

### 2. Enhanced JWT Guard (`src/auth/jwt.guard.ts`)

**Session Validation Logic:**
1. Extract JWT token from Authorization header
2. Verify JWT signature and expiry
3. Extract session ID from JWT payload
4. Validate session ID against Redis storage
5. Check if token is blacklisted
6. Allow/deny request based on validation

### 3. Updated User Resolver (`src/users/user.resolver.ts`)

**New Mutations:**
- `loginUser`: Creates new session and invalidates previous ones
- `logoutUser`: Properly terminates current session
- `forceLogoutUser`: Admin function to force logout any user

## Frontend Implementation

### 1. Session Management Hook (`src/hooks/useSessionManagement.js`)

**Features:**
- **Session Monitoring**: Real-time session validity checking
- **Conflict Detection**: Detects when user is logged in elsewhere
- **Automatic Logout**: Handles session conflicts gracefully
- **Manual Logout**: Proper session termination

**Usage:**
```javascript
const {
  sessionInfo,
  sessionConflict,
  logout,
  checkSessionValidity
} = useSessionManagement({
  enableMonitoring: true,
  onSessionConflict: () => {
    // Handle session conflict
    showSessionConflictDialog();
  }
});
```

### 2. Session Utilities (`src/utils/sessionUtils.js`)

**Key Functions:**
- `startSessionMonitoring()`: Begin monitoring session validity
- `stopSessionMonitoring()`: Stop monitoring
- `handleSessionConflict()`: Handle session conflicts
- `getCurrentSessionInfo()`: Get current session details
- `logoutCurrentUser()`: Logout with backend call

### 3. Session Conflict Dialog (`src/components/auth/SessionConflictDialog.jsx`)

**User Options:**
- **Continue Here**: Invalidate other session and continue
- **Go to Login**: Navigate to login page
- **Device Information**: Shows where the new login occurred

## Security Benefits

### 1. Prevents Concurrent Sessions
- Only one active session per user
- Automatic invalidation of previous sessions
- Real-time conflict detection

### 2. Token Security
- Blacklisted tokens cannot be reused
- Session IDs are cryptographically secure
- Timing-safe token comparison

### 3. Device Tracking
- Login device and IP tracking
- User agent information storage
- Timestamp tracking for audit trails

### 4. Graceful Conflict Resolution
- User-friendly conflict notifications
- Clear options for conflict resolution
- Automatic cleanup of invalid sessions

## Usage Examples

### Backend - Login with Session Management
```typescript
@Query((returns) => LoginUser)
async loginUser(
  @Args('username') username: string, 
  @Args('password') password: string,
  @Context() context: any
): Promise<object> {
  // Authenticate user
  const user = await this.userService.userAuth(username, password);
  
  // Get device info
  const deviceInfo = {
    userAgent: context.req.headers['user-agent'],
    ip: context.req.ip,
    timestamp: new Date().toISOString()
  };
  
  // Create session (invalidates previous sessions)
  const token = await this.sessionService.createUserSession(
    user.id.toString(),
    { id: user.id, username: user.name, role: user.role },
    deviceInfo
  );
  
  return { ...user, token };
}
```

### Frontend - Session Monitoring
```javascript
import { useSessionManagement } from '@/hooks/useSessionManagement';

function MyApp() {
  const { sessionConflict, logout } = useSessionManagement({
    enableMonitoring: true,
    onSessionConflict: () => {
      alert('You have been logged in from another device');
    }
  });

  return (
    <div>
      {sessionConflict && (
        <Alert severity="warning">
          Session conflict detected!
          <Button onClick={logout}>Logout</Button>
        </Alert>
      )}
      {/* Your app content */}
    </div>
  );
}
```

## Configuration

### Environment Variables
```bash
# Session Management
SESSION_SECRET=your-session-secret-here
CSRF_SECRET=your-csrf-secret-here

# Redis Configuration (required for session storage)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# JWT Configuration
JWT_KEY=your-jwt-secret
JWT_EXPIRES_IN=24h
```

### Redis Requirements
- Redis server must be running
- Session data is stored with 24-hour expiry
- Blacklisted tokens are stored with TTL matching JWT expiry

## Testing

### Test Session Conflict
1. Login from Device A
2. Login from Device B with same credentials
3. Device A should detect session conflict
4. Device A should show conflict dialog or auto-logout

### Test Session Validation
1. Login normally
2. Manually invalidate session in Redis
3. Next GraphQL request should fail with session error
4. User should be redirected to login

### Test Logout
1. Login normally
2. Call logout mutation
3. Session should be removed from Redis
4. Token should be blacklisted
5. Subsequent requests should fail

## Monitoring and Logging

### Backend Logs
- Session creation and invalidation
- Session conflict detection
- Token blacklisting events
- Force logout actions

### Frontend Logs
- Session monitoring start/stop
- Session conflict detection
- Automatic logout events
- Manual logout actions

## Migration Guide

### 1. Update Backend
- Add SessionService to auth module
- Update JWT guard to validate sessions
- Add logout mutations to user resolver

### 2. Update Frontend
- Add session management hook to authenticated components
- Implement session conflict handling
- Update logout functionality to call backend

### 3. Environment Setup
- Add required environment variables
- Ensure Redis is properly configured
- Test session functionality

## Troubleshooting

### Common Issues

1. **"Session is no longer valid"**
   - User logged in from another device
   - Session expired in Redis
   - Token was manually blacklisted

2. **Session monitoring not working**
   - Check if Redis is running
   - Verify session storage in Redis
   - Check network connectivity

3. **Multiple sessions still active**
   - Verify SessionService is properly injected
   - Check Redis session storage
   - Ensure JWT guard is using session validation

## Security Considerations

1. **Session Storage**: Sessions are stored in Redis with automatic expiry
2. **Token Blacklisting**: Invalid tokens are blacklisted to prevent reuse
3. **Device Tracking**: Login device information is tracked for audit
4. **Graceful Degradation**: System continues to work even if Redis is unavailable
5. **Admin Controls**: Admins can force logout any user

This implementation provides robust single-session-per-user functionality while maintaining security and user experience.
