import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserModule } from 'src/users/user.module';
import { AuthGuard } from './auth.guard';
import { JwtGuard } from './jwt.guard';
import { SessionService } from './session.service';


@Module({
  imports: [UserModule ],
  controllers: [],
  providers: [AuthGuard, JwtGuard, SessionService],
  exports: [SessionService]
})
export class AuthModule {}
