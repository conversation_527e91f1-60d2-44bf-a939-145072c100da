"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CsrfGuard = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const crypto = require("crypto");
let CsrfGuard = class CsrfGuard {
    constructor() {
        this.csrfSecret = process.env.CSRF_SECRET || 'default-csrf-secret-change-in-production';
    }
    async canActivate(context) {
        const ctx = graphql_1.GqlExecutionContext.create(context).getContext();
        const request = ctx.req;
        if (request.method === 'GET') {
            return true;
        }
        if (process.env.NODE_ENV !== 'production' && this.isIntrospectionQuery(request)) {
            return true;
        }
        const csrfToken = request.headers['x-csrf-token'];
        const sessionToken = request.headers['x-session-token'];
        if (!csrfToken) {
            throw new common_1.BadRequestException('CSRF token is required');
        }
        if (!sessionToken) {
            throw new common_1.BadRequestException('Session token is required');
        }
        if (!this.verifyCsrfToken(csrfToken, sessionToken)) {
            throw new common_1.ForbiddenException('Invalid CSRF token');
        }
        return true;
    }
    isIntrospectionQuery(request) {
        const body = request.body;
        if (body && body.query) {
            return body.query.includes('__schema') || body.query.includes('__type');
        }
        return false;
    }
    verifyCsrfToken(token, sessionToken) {
        try {
            const expectedToken = this.generateCsrfToken(sessionToken);
            return crypto.timingSafeEqual(Buffer.from(token, 'hex'), Buffer.from(expectedToken, 'hex'));
        }
        catch (error) {
            return false;
        }
    }
    generateCsrfToken(sessionToken) {
        const hmac = crypto.createHmac('sha256', this.csrfSecret);
        hmac.update(sessionToken);
        return hmac.digest('hex');
    }
};
exports.CsrfGuard = CsrfGuard;
exports.CsrfGuard = CsrfGuard = __decorate([
    (0, common_1.Injectable)()
], CsrfGuard);
//# sourceMappingURL=csrf.guard.js.map