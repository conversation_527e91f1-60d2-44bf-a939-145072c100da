"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OffersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const offers_entity_1 = require("./entities/offers.entity");
const typeorm_2 = require("typeorm");
const offers_entity_2 = require("./entities/offers.entity");
let OffersService = class OffersService {
    constructor(offersRepository, offersViewRepository) {
        this.offersRepository = offersRepository;
        this.offersViewRepository = offersViewRepository;
    }
    async create(createOffersInput) {
        const create = this.offersRepository.create(createOffersInput);
        const saved = await this.offersRepository.save(create);
        return saved;
    }
    ;
    async findAll(userId, filter) {
        const offers = await this.offersRepository.createQueryBuilder("offers")
            .leftJoinAndSelect("offers_view", "view", "view.offer_id = offers.id AND view.user_id = :userId", { userId })
            .select([
            "offers.id AS id",
            "offers.name AS name",
            "offers.contact_information AS contact_information",
            "offers.code AS code",
            "offers.expiry_date AS expiry_date",
            "offers.description AS description",
            "offers.status AS status",
            "offers.createdAt AS created_at",
            "CASE WHEN view.id IS NOT NULL THEN true ELSE false END AS is_read"
        ])
            .where(filter)
            .orderBy("offers.createdAt", "DESC")
            .getRawMany();
        return offers;
    }
    ;
    async findOne(filter) {
        return await this.offersRepository.findOne({ where: filter });
    }
    ;
    async createOfferView(offerId, userId) {
        return await this.offersViewRepository.upsert({ offer_id: offerId, user_id: userId }, { conflictPaths: ["user_id", "offer_id"] });
    }
    ;
    async update(id, updateOffersInput) {
        const existingOffer = await this.offersRepository.findOne({ where: { id } });
        if (!existingOffer)
            throw Error("offer not found");
        this.offersRepository.merge(existingOffer, updateOffersInput);
        return this.offersRepository.save(existingOffer);
    }
    ;
    async remove(id) {
        const offer = await this.offersRepository.findOne({ where: { id } });
        if (!offer)
            throw Error("offer not found");
        await this.offersRepository.remove(offer);
        return offer;
    }
    ;
};
exports.OffersService = OffersService;
exports.OffersService = OffersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(offers_entity_1.Offers)),
    __param(1, (0, typeorm_1.InjectRepository)(offers_entity_2.OffersViewEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository, typeorm_2.Repository])
], OffersService);
//# sourceMappingURL=offers.service.js.map