
import { ApolloClient, InMemoryCache, createHttpLink, ApolloLink } from "@apollo/client";
import { baseUrl } from "./ApolloClientLocal";


 //export const baseUrl = "http://*********:3001";
//export const baseUrl = "https://portal.hassana.com.sa/v1";
// export const baseUrl = "http://*************:3001";

// 1. Create a logging link
const loggingLink = new ApolloLink((operation, forward) => {
  // This code runs before each request
  console.log(
    `[Request] Starting: ${operation.operationName}`,
    {
      variables: operation.variables,
    }
  );

//export const baseurl = "http://localhost:3500";


  // Call the next link in the chain
  return forward(operation).map((response) => {
    // This code runs after a successful response is received
    console.log(
      `[Response] Completed: ${operation.operationName}`,
      {
        data: response.data,
      }
    );
    return response;
  });
});
const httpLink = createHttpLink({
  uri: baseUrl + "/graphql",
});

const authclient = new ApolloClient({
  link: ApolloLink.from([
    loggingLink,
    httpLink
  ]),
  cache: new InMemoryCache(),

  onError: ({ operation, networkError, response }) => {
    console.log(
      `[GraphQL Error]: Operation: ${operation.operationName}, Message: ${networkError?.message}, Response: `,
      response
    );
  },
});
authclient.resetCache = async () => {
  await authclient.cache.reset();
  console.log("Apollo Client cache has been reset.");
};
export default authclient;
