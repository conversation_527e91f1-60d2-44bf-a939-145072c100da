# GraphQL Authentication Guide

This guide explains how to handle GraphQL guards on the frontend for the Hassana project.

## Overview

The backend uses several guards to protect GraphQL operations:
- **JwtGuard**: Validates JWT tokens in the `Authorization` header
- **RoleGuard**: Checks user roles (ADMIN, USER)
- **AuthGuard**: Basic authentication for login operations

## Frontend Authentication System

### 1. Enhanced Apollo Client

The main Apollo Client (`src/Data/ApolloClient.js`) now includes:
- **Auth Link**: Automatically adds JWT tokens to requests
- **Error Link**: Handles authentication and authorization errors
- **Automatic token management**: Uses session tokens or localStorage fallback

### 2. Authentication Utilities

#### `src/utils/graphqlAuth.js`
- `createAuthOptions(session, options)`: Creates Apollo options with auth headers
- `createGraphQLErrorHandler()`: Handles different types of GraphQL errors
- `isAuthError(error)`: Checks if error is authentication-related
- `isForbiddenError(error)`: Checks if error is authorization-related
- `getErrorMessage(error)`: Extracts user-friendly error messages

#### `src/hooks/useGraphQLAuth.js`
- `useGraphQLAuth(error, onAuthError, onForbiddenError)`: React hook for handling auth errors
- `getAuthHeaders(session)`: Helper to get authorization headers
- `hasRequiredRole(session, role)`: Check if user has required role
- `createAuthContext(session)`: Create context with auth headers

### 3. Higher-Order Components

#### `src/components/auth/withGraphQLAuth.js`
- `withGraphQLAuth(Component, options)`: HOC for GraphQL authentication
- `withGraphQLAdminAuth(Component)`: HOC for admin-only operations
- `withGraphQLUserAuth(Component)`: HOC for user-level operations

## Usage Examples

### 1. Using Queries with Authentication

```javascript
import { useQuery } from "@apollo/client";
import { useSession } from "next-auth/react";
import { createAuthOptions, createGraphQLErrorHandler, getErrorMessage } from "@/utils/graphqlAuth";
import { useGraphQLAuth } from "@/hooks/useGraphQLAuth";

function MyComponent() {
  const { data: session } = useSession();
  const [errorMessage, setErrorMessage] = useState(null);

  // Use GraphQL authentication hook
  useGraphQLAuth(null, 
    () => setErrorMessage("Authentication failed. Please log in again."),
    () => setErrorMessage("Access denied.")
  );

  // Query with authentication
  const { data, loading, error } = useQuery(MY_QUERY, 
    createAuthOptions(session, {
      variables: { userId: session?.user?.id },
      skip: !session?.user?.id,
      onError: createGraphQLErrorHandler(
        () => setErrorMessage("Authentication failed. Please log in again."),
        () => setErrorMessage("Access denied."),
        (err) => setErrorMessage(getErrorMessage({ graphQLErrors: [err] }))
      ),
    })
  );

  return (
    <div>
      {errorMessage && <Alert severity="error">{errorMessage}</Alert>}
      {/* Your component content */}
    </div>
  );
}
```

### 2. Using Mutations with Authentication

```javascript
import { useMutation } from "@apollo/client";
import { useSession } from "next-auth/react";
import { createAuthOptions, createGraphQLErrorHandler, getErrorMessage } from "@/utils/graphqlAuth";

function MyComponent() {
  const { data: session } = useSession();
  const [error, setError] = useState(null);

  const [createItem, { loading }] = useMutation(CREATE_ITEM, 
    createAuthOptions(session, {
      onCompleted: () => {
        console.log("Item created successfully!");
      },
      onError: createGraphQLErrorHandler(
        () => setError("Authentication failed. Please log in again."),
        () => setError("Access denied. You don't have permission to create items."),
        (err) => setError(getErrorMessage({ graphQLErrors: [err] }))
      ),
    })
  );

  return (
    <div>
      {error && <Alert severity="error">{error}</Alert>}
      <button onClick={() => createItem({ variables: { input: data } })}>
        Create Item
      </button>
    </div>
  );
}
```

### 3. Using HOCs for Route Protection

```javascript
import withGraphQLAuth, { withGraphQLAdminAuth } from "@/components/auth/withGraphQLAuth";

// For admin-only pages
function AdminPage() {
  return <div>Admin content</div>;
}

export default withGraphQLAdminAuth(AdminPage);

// For general authenticated pages
function UserPage() {
  return <div>User content</div>;
}

export default withGraphQLAuth(UserPage, { requiredRole: 'USER' });
```

## Error Handling

### Authentication Errors (401/UNAUTHENTICATED)
- Token expired or invalid
- Missing authorization header
- **Action**: Redirect to login page, clear invalid tokens

### Authorization Errors (403/FORBIDDEN)
- Insufficient permissions
- Wrong user role
- **Action**: Show error message, optionally redirect to unauthorized page

### Network Errors
- Connection issues
- Server errors
- **Action**: Show appropriate error messages

## Best Practices

1. **Always use `createAuthOptions()`** for GraphQL operations that require authentication
2. **Use error handlers** to provide user-friendly error messages
3. **Clear invalid tokens** when authentication fails
4. **Check user roles** before showing UI elements for restricted actions
5. **Use HOCs** for page-level authentication requirements
6. **Handle loading states** appropriately during authentication checks

## Migration Guide

To migrate existing GraphQL operations:

1. Import the authentication utilities
2. Replace manual header creation with `createAuthOptions()`
3. Replace manual error handling with `createGraphQLErrorHandler()`
4. Add authentication hooks where needed
5. Use HOCs for page-level protection

## Troubleshooting

### Common Issues

1. **"Authorization header not found"**
   - Check if session token is available
   - Verify localStorage fallback is working

2. **"Token has expired"**
   - Implement token refresh logic
   - Clear expired tokens and redirect to login

3. **"Access denied"**
   - Check user role in session
   - Verify backend role requirements

4. **Network errors**
   - Check backend server status
   - Verify GraphQL endpoint URL
