import { UserService } from './user.service';
import { User } from './entities/user.entity';
import { UUID } from 'crypto';
import { SessionService } from '../auth/session.service';
export declare class UserResolver {
    private readonly userService;
    private readonly sessionService;
    constructor(userService: UserService, sessionService: SessionService);
    getAllUsers(page: number, pageSize: number): Promise<any>;
    getNewUsers(days: number): Promise<User[]>;
    getCulturalAmbassadors(): Promise<User[]>;
    logoutUser(userId: string, context: any): Promise<string>;
    forceLogoutUser(userId: string): Promise<string>;
    loginUser(username: string, password: string, context: any): Promise<object>;
    findUserById(id: UUID): Promise<User>;
}
