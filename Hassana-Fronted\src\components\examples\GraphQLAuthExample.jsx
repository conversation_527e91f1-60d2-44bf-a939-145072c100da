import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import { useSession } from 'next-auth/react';
import {
  Box,
  But<PERSON>,
  Card,
  CardContent,
  Typography,
  Alert,
  CircularProgress,
  Chip,
  Stack
} from '@mui/material';
import { gql } from '@apollo/client';
import { createAuthOptions, createGraphQLErrorHandler, getErrorMessage, isAuthError, isForbiddenError } from '@/utils/graphqlAuth';
import { useGraphQLAuth, hasRequiredRole } from '@/hooks/useGraphQLAuth';

// Example GraphQL operations
const GET_SECURED_DATA_FOR_USER = gql`
  query GetSecuredDataForUser {
    securedDataForUser
  }
`;

const GET_SECURED_DATA_FOR_ADMIN = gql`
  query GetSecuredDataForAdmin {
    securedDataForAdmin
  }
`;

const CREATE_QUOTE = gql`
  mutation CreateQuote($createQuoteInput: CreateQuoteInput!) {
    createQuote(createQuoteInput: $createQuoteInput) {
      id
      quote
      author
    }
  }
`;

/**
 * Example component demonstrating GraphQL authentication handling
 */
function GraphQLAuthExample() {
  const { data: session, status } = useSession();
  const [errorMessage, setErrorMessage] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);

  // Clear messages after 5 seconds
  React.useEffect(() => {
    if (errorMessage || successMessage) {
      const timer = setTimeout(() => {
        setErrorMessage(null);
        setSuccessMessage(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage, successMessage]);

  // Use GraphQL authentication hook for global error handling
  useGraphQLAuth(null, 
    () => setErrorMessage("Authentication failed. Please log in again."),
    () => setErrorMessage("Access denied. You don't have the required permissions.")
  );

  // Query for user-level data
  const { 
    data: userData, 
    loading: userLoading, 
    error: userError,
    refetch: refetchUserData 
  } = useQuery(GET_SECURED_DATA_FOR_USER, 
    createAuthOptions(session, {
      skip: status !== 'authenticated',
      onError: createGraphQLErrorHandler(
        () => setErrorMessage("Failed to authenticate for user data"),
        () => setErrorMessage("Access denied for user data"),
        (err) => setErrorMessage(`User data error: ${getErrorMessage({ graphQLErrors: [err] })}`)
      ),
    })
  );

  // Query for admin-level data
  const { 
    data: adminData, 
    loading: adminLoading, 
    error: adminError,
    refetch: refetchAdminData 
  } = useQuery(GET_SECURED_DATA_FOR_ADMIN, 
    createAuthOptions(session, {
      skip: status !== 'authenticated' || !hasRequiredRole(session, 'ADMIN'),
      onError: createGraphQLErrorHandler(
        () => setErrorMessage("Failed to authenticate for admin data"),
        () => setErrorMessage("Access denied for admin data"),
        (err) => setErrorMessage(`Admin data error: ${getErrorMessage({ graphQLErrors: [err] })}`)
      ),
    })
  );

  // Mutation example
  const [createQuote, { loading: createLoading }] = useMutation(CREATE_QUOTE, 
    createAuthOptions(session, {
      onCompleted: (data) => {
        setSuccessMessage(`Quote created successfully: "${data.createQuote.quote}"`);
      },
      onError: createGraphQLErrorHandler(
        () => setErrorMessage("Authentication failed while creating quote"),
        () => setErrorMessage("Access denied. You don't have permission to create quotes."),
        (err) => setErrorMessage(`Create quote error: ${getErrorMessage({ graphQLErrors: [err] })}`)
      ),
    })
  );

  const handleCreateQuote = () => {
    createQuote({
      variables: {
        createQuoteInput: {
          quote: "This is a test quote from the frontend",
          author: session?.user?.username || "Anonymous",
          visibility: true
        }
      }
    });
  };

  if (status === 'loading') {
    return (
      <Box display="flex" justifyContent="center" p={3}>
        <CircularProgress />
      </Box>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <Card>
        <CardContent>
          <Alert severity="warning">
            Please log in to view this example.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        GraphQL Authentication Example
      </Typography>

      {/* User Info */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Current User
          </Typography>
          <Stack direction="row" spacing={1} alignItems="center">
            <Typography>Username: {session?.user?.username}</Typography>
            <Chip 
              label={session?.user?.role} 
              color={session?.user?.role === 'ADMIN' ? 'primary' : 'secondary'}
              size="small"
            />
          </Stack>
        </CardContent>
      </Card>

      {/* Messages */}
      {errorMessage && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setErrorMessage(null)}>
          {errorMessage}
        </Alert>
      )}
      
      {successMessage && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccessMessage(null)}>
          {successMessage}
        </Alert>
      )}

      {/* User Data Section */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            User-Level Data (Requires USER or ADMIN role)
          </Typography>
          
          {userLoading && <CircularProgress size={20} />}
          
          {userData && (
            <Alert severity="success">
              {userData.securedDataForUser}
            </Alert>
          )}
          
          {userError && isAuthError(userError) && (
            <Alert severity="error">
              Authentication Error: {getErrorMessage(userError)}
            </Alert>
          )}
          
          {userError && isForbiddenError(userError) && (
            <Alert severity="warning">
              Authorization Error: {getErrorMessage(userError)}
            </Alert>
          )}
          
          <Button 
            variant="outlined" 
            onClick={() => refetchUserData()} 
            disabled={userLoading}
            sx={{ mt: 1 }}
          >
            Refresh User Data
          </Button>
        </CardContent>
      </Card>

      {/* Admin Data Section */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Admin-Level Data (Requires ADMIN role)
          </Typography>
          
          {!hasRequiredRole(session, 'ADMIN') && (
            <Alert severity="info">
              You need ADMIN role to access this data.
            </Alert>
          )}
          
          {hasRequiredRole(session, 'ADMIN') && (
            <>
              {adminLoading && <CircularProgress size={20} />}
              
              {adminData && (
                <Alert severity="success">
                  {adminData.securedDataForAdmin}
                </Alert>
              )}
              
              {adminError && (
                <Alert severity="error">
                  {getErrorMessage(adminError)}
                </Alert>
              )}
              
              <Button 
                variant="outlined" 
                onClick={() => refetchAdminData()} 
                disabled={adminLoading}
                sx={{ mt: 1 }}
              >
                Refresh Admin Data
              </Button>
            </>
          )}
        </CardContent>
      </Card>

      {/* Mutation Example */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Mutation Example (Create Quote)
          </Typography>
          
          <Typography variant="body2" color="text.secondary" gutterBottom>
            This demonstrates how to handle authentication in mutations.
          </Typography>
          
          <Button 
            variant="contained" 
            onClick={handleCreateQuote}
            disabled={createLoading}
            startIcon={createLoading && <CircularProgress size={16} />}
          >
            {createLoading ? 'Creating...' : 'Create Test Quote'}
          </Button>
        </CardContent>
      </Card>
    </Box>
  );
}

export default GraphQLAuthExample;
