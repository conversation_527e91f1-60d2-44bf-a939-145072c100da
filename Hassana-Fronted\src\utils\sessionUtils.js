import { gql } from '@apollo/client';
import { signOut } from 'next-auth/react';

// GraphQL mutations for session management
export const LOGOUT_USER = gql`
  mutation LogoutUser($userId: String!) {
    logoutUser(userId: $userId)
  }
`;

export const FORCE_LOGOUT_USER = gql`
  mutation ForceLogoutUser($userId: String!) {
    forceLogoutUser(userId: $userId)
  }
`;

/**
 * Session Management Utility
 * Handles single session per user functionality
 */
class SessionManager {
  constructor() {
    this.currentSessionId = null;
    this.sessionCheckInterval = null;
  }

  /**
   * Start session monitoring
   * Checks if current session is still valid
   * @param {string} userId - User ID
   * @param {string} sessionId - Current session ID
   * @param {Function} onSessionInvalid - Callback when session becomes invalid
   */
  startSessionMonitoring(userId, sessionId, onSessionInvalid) {
    this.currentSessionId = sessionId;
    
    // Check session validity every 30 seconds
    this.sessionCheckInterval = setInterval(async () => {
      try {
        const isValid = await this.checkSessionValidity(userId, sessionId);
        if (!isValid && onSessionInvalid) {
          this.stopSessionMonitoring();
          onSessionInvalid();
        }
      } catch (error) {
        console.error('Session check error:', error);
      }
    }, 30000); // 30 seconds

    console.log(`Session monitoring started for user ${userId}`);
  }

  /**
   * Stop session monitoring
   */
  stopSessionMonitoring() {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
      console.log('Session monitoring stopped');
    }
  }

  /**
   * Check if current session is still valid
   * @param {string} userId - User ID
   * @param {string} sessionId - Session ID to check
   * @returns {Promise<boolean>} Whether session is valid
   */
  async checkSessionValidity(userId, sessionId) {
    try {
      // This would typically make a request to backend to check session
      // For now, we'll check if the token is still valid by making a simple authenticated request
      const token = localStorage.getItem('jwtToken');
      if (!token) return false;

      // Decode token to check session ID
      const payload = this.decodeJWT(token);
      if (!payload || payload.sessionId !== sessionId) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('Session validity check failed:', error);
      return false;
    }
  }

  /**
   * Handle session conflict (user logged in elsewhere)
   * @param {Function} onConflict - Callback for session conflict
   */
  handleSessionConflict(onConflict) {
    // Clear local storage
    localStorage.removeItem('jwtToken');
    localStorage.removeItem('csrf-token');
    localStorage.removeItem('session-token');
    
    // Stop monitoring
    this.stopSessionMonitoring();
    
    // Call conflict handler
    if (onConflict) {
      onConflict();
    } else {
      // Default behavior: sign out and redirect
      this.defaultSessionConflictHandler();
    }
  }

  /**
   * Default session conflict handler
   */
  async defaultSessionConflictHandler() {
    try {
      // Sign out from NextAuth
      await signOut({ 
        redirect: true, 
        callbackUrl: '/login?reason=session_conflict' 
      });
    } catch (error) {
      console.error('Error during session conflict logout:', error);
      // Fallback: redirect manually
      window.location.href = '/login?reason=session_conflict';
    }
  }

  /**
   * Logout current user
   * @param {string} userId - User ID
   * @param {Function} apolloClient - Apollo client instance for mutation
   */
  async logoutCurrentUser(userId, apolloClient) {
    try {
      // Call logout mutation
      await apolloClient.mutate({
        mutation: LOGOUT_USER,
        variables: { userId }
      });

      // Clear local storage
      localStorage.removeItem('jwtToken');
      localStorage.removeItem('csrf-token');
      localStorage.removeItem('session-token');
      
      // Stop monitoring
      this.stopSessionMonitoring();

      console.log('User logged out successfully');
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  /**
   * Decode JWT token (client-side only for session ID)
   * @param {string} token - JWT token
   * @returns {Object|null} Decoded payload
   */
  decodeJWT(token) {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));

      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('JWT decode error:', error);
      return null;
    }
  }

  /**
   * Get current session info from token
   * @returns {Object|null} Session information
   */
  getCurrentSessionInfo() {
    const token = localStorage.getItem('jwtToken');
    if (!token) return null;

    const payload = this.decodeJWT(token);
    if (!payload) return null;

    return {
      userId: payload.id,
      sessionId: payload.sessionId,
      username: payload.username,
      role: payload.role,
      loginTime: payload.loginTime,
      deviceInfo: payload.deviceInfo
    };
  }

  /**
   * Check if user is logged in elsewhere
   * @param {string} expectedSessionId - Expected session ID
   * @returns {boolean} Whether user is logged in elsewhere
   */
  isLoggedInElsewhere(expectedSessionId) {
    const currentSession = this.getCurrentSessionInfo();
    return currentSession && currentSession.sessionId !== expectedSessionId;
  }
}

// Create singleton instance
const sessionManager = new SessionManager();

// Export utility functions
export const startSessionMonitoring = (userId, sessionId, onSessionInvalid) => 
  sessionManager.startSessionMonitoring(userId, sessionId, onSessionInvalid);

export const stopSessionMonitoring = () => 
  sessionManager.stopSessionMonitoring();

export const handleSessionConflict = (onConflict) => 
  sessionManager.handleSessionConflict(onConflict);

export const logoutCurrentUser = (userId, apolloClient) => 
  sessionManager.logoutCurrentUser(userId, apolloClient);

export const getCurrentSessionInfo = () => 
  sessionManager.getCurrentSessionInfo();

export const isLoggedInElsewhere = (expectedSessionId) => 
  sessionManager.isLoggedInElsewhere(expectedSessionId);

export default sessionManager;
