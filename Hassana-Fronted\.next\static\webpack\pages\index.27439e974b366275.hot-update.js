"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/csrfUtils.js":
/*!********************************!*\
  !*** ./src/utils/csrfUtils.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearCsrfTokens: function() { return /* binding */ clearCsrfTokens; },\n/* harmony export */   getCsrfToken: function() { return /* binding */ getCsrfToken; },\n/* harmony export */   getSecureHeaders: function() { return /* binding */ getSecureHeaders; },\n/* harmony export */   getSessionToken: function() { return /* binding */ getSessionToken; },\n/* harmony export */   initializeCsrfTokens: function() { return /* binding */ initializeCsrfTokens; },\n/* harmony export */   verifyCsrfToken: function() { return /* binding */ verifyCsrfToken; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var _Data_ApolloClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/Data/ApolloClient */ \"./src/Data/ApolloClient.js\");\n\n\n/**\n * CSRF Token Management Utility\n * Handles fetching, storing, and using CSRF tokens for secure requests\n */ class CsrfManager {\n    /**\n   * Fetch CSRF token from backend\n   * @returns {Promise<{csrfToken: string, sessionToken: string}>}\n   */ async fetchCsrfToken() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"\".concat(_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_0__.baseUrl, \"/security/csrf-token\"), {\n                withCredentials: true\n            });\n            const { csrfToken, sessionToken } = response.data;\n            // Store tokens\n            this.csrfToken = csrfToken;\n            this.sessionToken = sessionToken;\n            this.tokenExpiry = Date.now() + 23 * 60 * 60 * 1000; // 23 hours\n            // Store in localStorage for persistence\n            localStorage.setItem(\"csrf-token\", csrfToken);\n            localStorage.setItem(\"session-token\", sessionToken);\n            localStorage.setItem(\"csrf-expiry\", this.tokenExpiry.toString());\n            console.log(\"CSRF token fetched successfully\");\n            return {\n                csrfToken,\n                sessionToken\n            };\n        } catch (error) {\n            console.error(\"Failed to fetch CSRF token:\", error);\n            throw new Error(\"Failed to fetch CSRF token\");\n        }\n    }\n    /**\n   * Get current CSRF token, fetch new one if expired\n   * @returns {Promise<string>}\n   */ async getCsrfToken() {\n        // Check if we have a valid token\n        if (this.isTokenValid()) {\n            return this.csrfToken;\n        }\n        // Try to load from localStorage\n        const storedToken = localStorage.getItem(\"csrf-token\");\n        const storedSession = localStorage.getItem(\"session-token\");\n        const storedExpiry = localStorage.getItem(\"csrf-expiry\");\n        if (storedToken && storedSession && storedExpiry) {\n            const expiry = parseInt(storedExpiry);\n            if (Date.now() < expiry) {\n                this.csrfToken = storedToken;\n                this.sessionToken = storedSession;\n                this.tokenExpiry = expiry;\n                return this.csrfToken;\n            }\n        }\n        // Fetch new token\n        const { csrfToken } = await this.fetchCsrfToken();\n        return csrfToken;\n    }\n    /**\n   * Get current session token\n   * @returns {Promise<string>}\n   */ async getSessionToken() {\n        if (!this.isTokenValid()) {\n            await this.getCsrfToken(); // This will fetch both tokens\n        }\n        return this.sessionToken;\n    }\n    /**\n   * Check if current token is valid\n   * @returns {boolean}\n   */ isTokenValid() {\n        return this.csrfToken && this.sessionToken && this.tokenExpiry && Date.now() < this.tokenExpiry;\n    }\n    /**\n   * Clear stored tokens\n   */ clearTokens() {\n        this.csrfToken = null;\n        this.sessionToken = null;\n        this.tokenExpiry = null;\n        localStorage.removeItem(\"csrf-token\");\n        localStorage.removeItem(\"session-token\");\n        localStorage.removeItem(\"csrf-expiry\");\n    }\n    /**\n   * Get headers for secure requests\n   * @returns {Promise<Object>}\n   */ async getSecureHeaders() {\n        const csrfToken = await this.getCsrfToken();\n        const sessionToken = await this.getSessionToken();\n        return {\n            \"X-CSRF-Token\": csrfToken,\n            \"X-Session-Token\": sessionToken\n        };\n    }\n    /**\n   * Verify CSRF token with backend (for testing)\n   * @returns {Promise<boolean>}\n   */ async verifyCsrfToken() {\n        try {\n            const headers = await this.getSecureHeaders();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(\"\".concat(_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_0__.baseUrl, \"/security/verify-csrf\"), {}, {\n                headers,\n                withCredentials: true\n            });\n            return response.data.valid;\n        } catch (error) {\n            console.error(\"CSRF token verification failed:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.csrfToken = null;\n        this.sessionToken = null;\n        this.tokenExpiry = null;\n    }\n}\n// Create singleton instance\nconst csrfManager = new CsrfManager();\n/**\n * Get CSRF token for use in requests\n * @returns {Promise<string>}\n */ const getCsrfToken = ()=>csrfManager.getCsrfToken();\n/**\n * Get session token for use in requests\n * @returns {Promise<string>}\n */ const getSessionToken = ()=>csrfManager.getSessionToken();\n/**\n * Get secure headers for GraphQL requests\n * @returns {Promise<Object>}\n */ const getSecureHeaders = ()=>csrfManager.getSecureHeaders();\n/**\n * Clear CSRF tokens (on logout)\n */ const clearCsrfTokens = ()=>csrfManager.clearTokens();\n/**\n * Initialize CSRF tokens (call on app startup)\n * @returns {Promise<void>}\n */ const initializeCsrfTokens = async ()=>{\n    try {\n        await csrfManager.getCsrfToken();\n        console.log(\"CSRF tokens initialized\");\n    } catch (error) {\n        console.warn(\"Failed to initialize CSRF tokens:\", error);\n    }\n};\n/**\n * Verify CSRF token (for testing)\n * @returns {Promise<boolean>}\n */ const verifyCsrfToken = ()=>csrfManager.verifyCsrfToken();\n/* harmony default export */ __webpack_exports__[\"default\"] = (csrfManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/csrfUtils.js\n"));

/***/ }),

/***/ "./src/utils/graphqlAuth.js":
/*!**********************************!*\
  !*** ./src/utils/graphqlAuth.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAuthOptions: function() { return /* binding */ createAuthOptions; },\n/* harmony export */   createGraphQLErrorHandler: function() { return /* binding */ createGraphQLErrorHandler; },\n/* harmony export */   getErrorMessage: function() { return /* binding */ getErrorMessage; },\n/* harmony export */   isAuthError: function() { return /* binding */ isAuthError; },\n/* harmony export */   isForbiddenError: function() { return /* binding */ isForbiddenError; }\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _csrfUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./csrfUtils */ \"./src/utils/csrfUtils.js\");\n\n\n/**\n * Creates GraphQL query/mutation options with authentication headers\n * @param {Object} session - NextAuth session object\n * @param {Object} additionalOptions - Additional Apollo options\n * @returns {Object} Apollo query/mutation options with auth headers\n */ const createAuthOptions = function(session) {\n    let additionalOptions = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _additionalOptions_context;\n    const token = (session === null || session === void 0 ? void 0 : session.accessToken) || ( true ? localStorage.getItem(\"jwtToken\") : 0);\n    return {\n        ...additionalOptions,\n        context: {\n            ...additionalOptions.context,\n            headers: {\n                ...(_additionalOptions_context = additionalOptions.context) === null || _additionalOptions_context === void 0 ? void 0 : _additionalOptions_context.headers,\n                ...token && {\n                    authorization: \"Bearer \".concat(token)\n                }\n            }\n        },\n        errorPolicy: \"all\"\n    };\n};\n/**\n * Creates error handler for GraphQL operations\n * @param {Function} onAuthError - Callback for authentication errors\n * @param {Function} onForbiddenError - Callback for authorization errors\n * @param {Function} onOtherError - Callback for other errors\n * @returns {Function} Error handler function\n */ const createGraphQLErrorHandler = (onAuthError, onForbiddenError, onOtherError)=>{\n    return (error)=>{\n        console.error(\"GraphQL Error:\", error);\n        // Handle GraphQL errors\n        if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n            error.graphQLErrors.forEach((gqlError)=>{\n                const { message, extensions } = gqlError;\n                // Authentication errors\n                if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\") || message.includes(\"Token has expired\") || message.includes(\"Invalid token\")) {\n                    if (onAuthError) {\n                        onAuthError(gqlError);\n                    } else {\n                        console.warn(\"Authentication error:\", message);\n                        // Clear invalid token\n                        if (true) {\n                            localStorage.removeItem(\"jwtToken\");\n                            window.location.href = \"/login?login=false&reason=auth_error\";\n                        }\n                    }\n                } else if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\") || message.includes(\"Required role\")) {\n                    if (onForbiddenError) {\n                        onForbiddenError(gqlError);\n                    } else {\n                        console.warn(\"Authorization error:\", message);\n                    }\n                } else {\n                    if (onOtherError) {\n                        onOtherError(gqlError);\n                    } else {\n                        console.error(\"GraphQL error:\", message);\n                    }\n                }\n            });\n        }\n        // Handle network errors\n        if (error.networkError) {\n            const { statusCode } = error.networkError;\n            if (statusCode === 401) {\n                if (onAuthError) {\n                    onAuthError(error.networkError);\n                } else {\n                    console.warn(\"Network authentication error\");\n                    if (true) {\n                        localStorage.removeItem(\"jwtToken\");\n                        window.location.href = \"/login?login=false&reason=network_auth\";\n                    }\n                }\n            } else if (statusCode === 403) {\n                if (onForbiddenError) {\n                    onForbiddenError(error.networkError);\n                } else {\n                    console.warn(\"Network authorization error\");\n                }\n            } else {\n                if (onOtherError) {\n                    onOtherError(error.networkError);\n                } else {\n                    console.error(\"Network error:\", error.networkError);\n                }\n            }\n        }\n    };\n};\n/**\n * Utility function to check if an error is an authentication error\n * @param {Object} error - Apollo error object\n * @returns {boolean} Whether the error is an authentication error\n */ const isAuthError = (error)=>{\n    if (!error) return false;\n    // Check GraphQL errors\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        return error.graphQLErrors.some((gqlError)=>{\n            const { message, extensions } = gqlError;\n            return (extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\") || message.includes(\"Token has expired\") || message.includes(\"Invalid token\");\n        });\n    }\n    // Check network errors\n    if (error.networkError && error.networkError.statusCode === 401) {\n        return true;\n    }\n    return false;\n};\n/**\n * Utility function to check if an error is an authorization error\n * @param {Object} error - Apollo error object\n * @returns {boolean} Whether the error is an authorization error\n */ const isForbiddenError = (error)=>{\n    if (!error) return false;\n    // Check GraphQL errors\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        return error.graphQLErrors.some((gqlError)=>{\n            const { message, extensions } = gqlError;\n            return (extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\") || message.includes(\"Required role\");\n        });\n    }\n    // Check network errors\n    if (error.networkError && error.networkError.statusCode === 403) {\n        return true;\n    }\n    return false;\n};\n/**\n * Extracts user-friendly error message from GraphQL error\n * @param {Object} error - Apollo error object\n * @returns {string} User-friendly error message\n */ const getErrorMessage = (error)=>{\n    if (!error) return \"An unknown error occurred\";\n    // Check for GraphQL errors first\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        const gqlError = error.graphQLErrors[0];\n        // Return custom messages for common errors\n        if (isAuthError(error)) {\n            return \"Authentication required. Please log in again.\";\n        }\n        if (isForbiddenError(error)) {\n            return \"Access denied. You do not have permission to perform this action.\";\n        }\n        return gqlError.message || \"A GraphQL error occurred\";\n    }\n    // Check for network errors\n    if (error.networkError) {\n        if (error.networkError.statusCode === 401) {\n            return \"Authentication required. Please log in again.\";\n        }\n        if (error.networkError.statusCode === 403) {\n            return \"Access denied. You do not have permission to perform this action.\";\n        }\n        return error.networkError.message || \"A network error occurred\";\n    }\n    return error.message || \"An error occurred\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/graphqlAuth.js\n"));

/***/ })

});