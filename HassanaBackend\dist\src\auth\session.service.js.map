{"version": 3, "file": "session.service.js", "sourceRoot": "", "sources": ["../../../src/auth/session.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2DAAuD;AACvD,qCAA4B;AAC5B,oCAAoC;AAG7B,IAAM,cAAc,sBAApB,MAAM,cAAc;IAGzB,YAA2B,KAA6B;QAAZ,UAAK,GAAL,KAAK,CAAO;QAFvC,WAAM,GAAG,IAAI,eAAM,CAAC,gBAAc,CAAC,IAAI,CAAC,CAAC;IAEC,CAAC;IAS5D,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,YAAiB,EAAE,UAAgB;QACzE,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;QACpC,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK,CAAC;QAE3D,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAGD,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAG3C,MAAM,eAAe,GAAG;YACtB,GAAG,YAAY;YACf,SAAS;YACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,UAAU,EAAE,UAAU,IAAI,SAAS;SACpC,CAAC;QAGF,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC;QAGhF,MAAM,UAAU,GAAG,gBAAgB,MAAM,EAAE,CAAC;QAC5C,MAAM,WAAW,GAAG;YAClB,SAAS;YACT,KAAK;YACL,MAAM;YACN,SAAS,EAAE,eAAe,CAAC,SAAS;YACpC,UAAU,EAAE,eAAe,CAAC,UAAU;YACtC,QAAQ,EAAE,MAAM;SACjB,CAAC;QAGF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC7D,IAAI,eAAe,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;YACjD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,gEAAgE,CAAC,CAAC;YAGjG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC;QAGD,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QAGhD,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAElD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,MAAM,qBAAqB,SAAS,EAAE,CAAC,CAAC;QAExF,OAAO,KAAK,CAAC;IACf,CAAC;IASD,KAAK,CAAC,mBAAmB,CAAC,MAAc,EAAE,SAAiB,EAAE,KAAa;QACxE,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC3D,IAAI,aAAa,EAAE,CAAC;gBAClB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,MAAM,EAAE,CAAC,CAAC;gBAC9D,OAAO,KAAK,CAAC;YACf,CAAC;YAGD,MAAM,UAAU,GAAG,gBAAgB,MAAM,EAAE,CAAC;YAC5C,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAE5D,IAAI,CAAC,cAAc,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;gBACjD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,MAAM,EAAE,CAAC,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACf,CAAC;YAGD,IAAI,cAAc,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gCAAgC,MAAM,cAAc,cAAc,CAAC,SAAS,eAAe,SAAS,EAAE,CAAC,CAAC;gBAGzH,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;gBACjC,OAAO,KAAK,CAAC;YACf,CAAC;YAGD,IAAI,cAAc,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,EAAE,CAAC,CAAC;gBACxD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YACzE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAOD,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,KAAa;QAC5C,IAAI,CAAC;YAEH,MAAM,UAAU,GAAG,gBAAgB,MAAM,EAAE,CAAC;YAC5C,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAGjC,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAEjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,MAAM,0BAA0B,CAAC,CAAC;QAC5D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YACpE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAOD,KAAK,CAAC,cAAc,CAAC,MAAc;QACjC,MAAM,UAAU,GAAG,gBAAgB,MAAM,EAAE,CAAC;QAC5C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAErD,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ,KAAK,MAAM;SACtC,CAAC;IACJ,CAAC;IAMD,KAAK,CAAC,eAAe,CAAC,MAAc;QAClC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,gBAAgB,MAAM,EAAE,CAAC;YAC5C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAErD,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBAC7B,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC3C,CAAC;YAED,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAEjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,MAAM,EAAE,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAMO,KAAK,CAAC,cAAc,CAAC,KAAa;QACxC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAQ,CAAC;YACzC,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;gBAC3B,MAAM,YAAY,GAAG,aAAa,KAAK,EAAE,CAAC;gBAC1C,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;gBAExD,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;oBACZ,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC;oBAClD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,GAAG,UAAU,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAOO,KAAK,CAAC,kBAAkB,CAAC,KAAa;QAC5C,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,aAAa,KAAK,EAAE,CAAC;YAC1C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAClD,OAAO,MAAM,KAAK,MAAM,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAMO,iBAAiB;QACvB,OAAO,WAAW,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC5E,CAAC;IAMD,KAAK,CAAC,oBAAoB;QACxB,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACrD,MAAM,QAAQ,GAAG,EAAE,CAAC;YAEpB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBAC9C,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;oBAC3C,QAAQ,CAAC,IAAI,CAAC;wBACZ,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;wBACxC,GAAG,OAAO;qBACX,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF,CAAA;AAtPY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;IAIE,WAAA,IAAA,0BAAW,GAAE,CAAA;qCAAyB,iBAAK;GAH7C,cAAc,CAsP1B"}