import React from "react";
import Button from "@mui/material/Button";
import Image from 'next/image';
import ButtonGroup from "@mui/material/ButtonGroup";
import Link from "next/link";
import Grid from "@mui/material/Grid";
import Hidden from "@mui/material/Hidden";
import LoginSlideForm from "@/components/login";

function AppBar() {
  return (
    <Grid
      container
      direction="row"
      justifyContent="space-between"
      alignItems="center"
      sx={{
        // position: "fixed",
        top: "0px",
        left: "0px",
        width: "100%",
        backgroundColor: "#063F53",
        borderTop: "4px solid #A665E1",
        borderBottom: "4px solid #A665E1",
        padding: "10px",
        zIndex: 9999,
      }}
    >
      <Grid item xs={6}>
        <Image src="/HassanaLogoD.png" loading="lazy" width={200} alt="Logo" />
      </Grid>
      <Hidden smDown>
        <ButtonGroup
          disableElevation
          variant="contained"
          aria-label="Disabled elevation buttons"
        >
          <Button
            sx={{
              backgroundColor: "#063F53",
              "&:hover": {
                backgroundColor: "grey",
              },
            }}
          >
            About
          </Button>
            <Button onClick={<LoginSlideForm/>}
              sx={{
                backgroundColor: "#063F53",
                "&:hover": {
                  backgroundColor: "grey",
                },
              }}
            >
              Login
            </Button>
        </ButtonGroup>{" "}
        <Grid item xs={6}></Grid>
      </Hidden>
    </Grid>
  );
}

export default AppBar;
