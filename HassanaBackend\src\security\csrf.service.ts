import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';

@Injectable()
export class CsrfService {
  private readonly csrfSecret = process.env.CSRF_SECRET || 'default-csrf-secret-change-in-production';

  /**
   * Generate a CSRF token based on session token
   * @param sessionToken - The session token (usually JWT)
   * @returns CSRF token
   */
  generateCsrfToken(sessionToken: string): string {
    const hmac = crypto.createHmac('sha256', this.csrfSecret);
    hmac.update(sessionToken);
    return hmac.digest('hex');
  }

  /**
   * Verify CSRF token against session token
   * @param csrfToken - The CSRF token to verify
   * @param sessionToken - The session token
   * @returns boolean indicating if token is valid
   */
  verifyCsrfToken(csrfToken: string, sessionToken: string): boolean {
    try {
      const expectedToken = this.generateCsrfToken(sessionToken);
      return crypto.timingSafeEqual(
        Buffer.from(csrfToken, 'hex'),
        Buffer.from(expectedToken, 'hex')
      );
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate a random session token
   * @returns Random session token
   */
  generateSessionToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Create CSRF token response for client
   * @param sessionToken - The session token
   * @returns Object with CSRF token and session token
   */
  createCsrfResponse(sessionToken?: string): { csrfToken: string; sessionToken: string } {
    const session = sessionToken || this.generateSessionToken();
    const csrf = this.generateCsrfToken(session);
    
    return {
      csrfToken: csrf,
      sessionToken: session
    };
  }
}
