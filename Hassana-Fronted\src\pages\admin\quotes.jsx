import Dashboard from "@/components/Dashboard";
import { Box, Button, CircularProgress, MenuItem, TextField, Typography, useTheme } from "@mui/material";
import DataTable from "./component/DataTable";
import { useMutation, useQuery } from "@apollo/client";
import { useEffect, useState } from "react";
import BasicModal, { UpdateModal } from "./component/DialogBox";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import { getQuotes, mutationCreateQuote, mutationRemoveQuote, mutationUpdateQuote } from "@/Data/Quote";
import { getDate } from "@/components/HelperFunctions";
import withAdminAuth from "@/components/auth/withAdminAuth";
import SnackbarComponent from "@/components/SnackBar";

const columns = [
  { id: "quote", label: "Quotes", minWidth: 170 },
  { id: "author", label: "Author", minWidth: 170, align: "center" },
  { id: "status", label: "Status", minWidth: 170, align: "center" },
  { id: "visibilityStart", label: "Visibility Start", minWidth: 170, align: "center" },
  { id: "visibilityEnd", label: "Visibility End", minWidth: 170, align: "center" },
];

const AddQuote = (props) => {
  const { data, setOpenUpdate, opt, quotes, setQuotes, setOpen, setSnackbarMessage, setSnackbarSeverity, setSnackbarOpen, refetch } = props;
  const [id] = useState(data?.id || "");
  const [author, setAuthor] = useState(data?.author || "");
  const [quote, setQuote] = useState(data?.quote || "");
  const [status, setStatus] = useState(data?.status?.toString() || "true");
  const [visibilityStart, setVisibilityStart] = useState(data?.visibilityStart ? dayjs(data.visibilityStart) : dayjs());
  const [visibilityEnd, setVisibilityEnd] = useState(
    data?.visibilityEnd ? dayjs(data.visibilityEnd) : dayjs(visibilityStart).add(6, "day")
  );
  const [errors, setErrors] = useState({});
  const [createQuote, { loading: createLoading }] = useMutation(mutationCreateQuote, {
    onCompleted: () => refetch(),
  });
  const [updateQuote, { loading: updateLoading }] = useMutation(mutationUpdateQuote, {
    onCompleted: () => refetch(),
  });
  const theme = useTheme();

  useEffect(() => {
    const newVisibilityEnd = dayjs(visibilityStart).add(6, "day");
    setVisibilityEnd(newVisibilityEnd);
  }, [visibilityStart]);

  const validateForm = () => {
    let tempErrors = {};
    if (!author?.trim()) tempErrors.author = "Author is required.";
    if (!quote?.trim()) tempErrors.quote = "Quote is required.";
    if (!status) tempErrors.status = "Status is required.";
    if (!visibilityStart || !dayjs(visibilityStart).isValid()) tempErrors.visibilityStart = "Valid start date is required.";
    if (!visibilityEnd || !dayjs(visibilityEnd).isValid()) tempErrors.visibilityEnd = "Valid end date is required.";
    setErrors(tempErrors);
    return Object.keys(tempErrors).length === 0;
  };

  const resetForm = () => {
    setAuthor("");
    setQuote("");
    setStatus("true");
    setVisibilityStart(dayjs());
    setVisibilityEnd(dayjs().add(6, "day"));
    setErrors({});
  };

  const submitHandler = async () => {
    try {
      if (!validateForm()) return;

      const quoteData = {
        quote: String(quote.trim()),
        author: String(author.trim()),
        status: Boolean(status === "true"),
        visibilityStart: getDate(visibilityStart), // ISO string, e.g., "2025-05-26T00:00:00Z"
        visibilityEnd: getDate(visibilityEnd), // ISO string, e.g., "2025-06-01T23:59:59Z"
      };

      // Check for existing quote with same visibilityStart
      const formattedVisibilityStart = dayjs(quoteData.visibilityStart).format("YYYY-MM-DD");
      const checkExisting = quotes.some(
        (q) => q.id !== id && dayjs(q.visibilityStart).format("YYYY-MM-DD") === formattedVisibilityStart
      );
      if (checkExisting) {
        setErrors((prev) => ({
          ...prev,
          visibilityStart: "A quote already exists for this visibility start date.",
        }));
        setSnackbarMessage("Quote for this visibility start date already exists!");
        setSnackbarSeverity("warning");
        setSnackbarOpen(true);
        return;
      }

      let response;
      if (opt === "update") {
        const mutationData = {
          variables: {
            id: String(id),
            updateQuoteInput: quoteData,
          },
        };
        console.log("Update mutation data:", JSON.stringify(mutationData, null, 2));
        response = await updateQuote(mutationData);
        const updatedQuote = response.data.updateQuote;
        const itemIndex = quotes.findIndex((item) => item.id === id);

        if (itemIndex !== -1) {
          const updatedQuotes = [...quotes];
          updatedQuotes[itemIndex] = {
            ...updatedQuote,
            status: updatedQuote.status.toString(),
          };
          setQuotes(updatedQuotes);
        }

        setOpenUpdate(false);
        setSnackbarMessage("Quote updated successfully");
      } else {
        const mutationData = {
          variables: {
            createQuoteInput: quoteData,
          },
        };
        console.log("Create mutation data:", JSON.stringify(mutationData, null, 2));
        response = await createQuote(mutationData);
        const newQuote = response.data.createQuote;
        setQuotes([
          {
            ...newQuote,
            status: newQuote.status.toString(),
          },
          ...quotes,
        ]);

        setOpen(false);
        setSnackbarMessage("Quote created successfully");
        resetForm();
      }

      setSnackbarSeverity("success");
      setSnackbarOpen(true);
    } catch (error) {
      console.error("Mutation error:", error);
      let errorMessage = "An error occurred";
      if (error.graphQLErrors?.length > 0) {
        const errorCode = error.graphQLErrors[0].extensions?.code;
        if (errorCode === "BAD_USER_INPUT" && error.message.includes("duplicate key value violates unique constraint")) {
          setErrors((prev) => ({
            ...prev,
            visibilityStart: "A quote already exists for this visibility start date.",
          }));
          errorMessage = "Cannot update quote: A quote already exists for this visibility start date.";
        } else {
          errorMessage = error.graphQLErrors[0].message;
        }
      } else if (error.networkError) {
        errorMessage = "Network error occurred";
      }
      setSnackbarMessage(`Failed to ${opt === "update" ? "update" : "create"} quote: ${errorMessage}`);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <TextField
        margin="normal"
        size="small"
        id="quote"
        label="Quote"
        error={!!errors.quote}
        helperText={errors.quote}
        multiline
        rows={6}
        placeholder="Enter quote..."
        fullWidth
        value={quote}
        onChange={(e) => {
          setQuote(e.target.value);
          if (errors.quote) setErrors((prev) => ({ ...prev, quote: "" }));
        }}
        disabled={createLoading || updateLoading}
      />
      <TextField
        margin="normal"
        size="small"
        id="author"
        label="Author"
        error={!!errors.author}
        helperText={errors.author}
        fullWidth
        value={author}
        onChange={(e) => {
          setAuthor(e.target.value);
          if (errors.author) setErrors((prev) => ({ ...prev, author: "" }));
        }}
        disabled={createLoading || updateLoading}
      />
      <DatePicker
        id="visibilityStart"
        label="Visibility Start Date"
        value={visibilityStart}
        onChange={(newValue) => {
          setVisibilityStart(newValue);
          if (errors.visibilityStart) setErrors((prev) => ({ ...prev, visibilityStart: "" }));
        }}
        minDate={dayjs()}
        slotProps={{
          textField: {
            error: !!errors.visibilityStart,
            helperText: errors.visibilityStart,
            fullWidth: true,
            margin: "normal",
          },
        }}
        sx={{ marginY: "10px" }}
        disabled={createLoading || updateLoading}
      />
      <DatePicker
        id="visibilityEnd"
        label="Visibility End Date"
        value={visibilityEnd}
        readOnly
        slotProps={{
          textField: {
            error: !!errors.visibilityEnd,
            helperText: errors.visibilityEnd,
            fullWidth: true,
            margin: "normal",
          },
        }}
        sx={{ marginY: "10px" }}
        disabled
      />
      <TextField
        id="status"
        select
        label="Status"
        error={!!errors.status}
        helperText={errors.status}
        fullWidth
        value={status}
        onChange={(e) => {
          setStatus(e.target.value);
          if (errors.status) setErrors((prev) => ({ ...prev, status: "" }));
        }}
        disabled={createLoading || updateLoading}
        margin="normal"
        SelectProps={{
          MenuProps: {
            PaperProps: {
              style: {
                backgroundColor: theme.palette.background.primary,
              },
            },
          },
        }}
      >
        <MenuItem value="true">Active</MenuItem>
        <MenuItem value="false">Inactive</MenuItem>
      </TextField>
      <Button
        onClick={submitHandler}
        disabled={createLoading || updateLoading}
       style={{
          color: theme.palette.text.white,
          background: theme.palette.text.purple,
        }}
        fullWidth
      >
        {createLoading || updateLoading ? "Processing..." : opt === "update" ? "Update" : "Submit"}
      </Button>
    </LocalizationProvider>
  );
};

const Quotes = () => {
  const [open, setOpen] = useState(false);
  const [data, setData] = useState(null);
  const [quotes, setQuotes] = useState([]);
  const [openUpdateBox, setOpenUpdateBox] = useState(false);
  const [index, setIndex] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");

  const { loading: queryLoading, error: queryError, data: queryData, refetch } = useQuery(getQuotes, {
    errorPolicy: "all",
    notifyOnNetworkStatusChange: true,
  });

  const [removeQuote] = useMutation(mutationRemoveQuote, {
    onCompleted: () => refetch(),
  });

  const deleteHandler = async (deletedId) => {
    try {
      await removeQuote({ variables: { id: String(deletedId) } });
      setQuotes((prev) => prev.filter((quote) => quote.id !== deletedId));
      setSnackbarMessage("Quote deleted successfully");
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
    } catch (error) {
      console.error("Delete error:", error);
      let errorMessage = "Failed to delete quote";
      if (error.graphQLErrors?.length > 0) {
        errorMessage = error.graphQLErrors[0].message;
      } else if (error.networkError) {
        errorMessage = "Network error occurred";
      }
      setSnackbarMessage(errorMessage);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
  };

  useEffect(() => {
    if (!queryLoading && !queryError && queryData?.findAllQuote) {
      setQuotes(queryData.findAllQuote);
    }
  }, [queryLoading, queryError, queryData]);

  const handleCloseSnackbar = (event, reason) => {
    if (reason === "clickaway") return;
    setSnackbarOpen(false);
  };

  return (
    <>
      <Dashboard>
        <Box sx={{ margin: "25px" }}>
          <Box sx={{ display: "flex", justifyContent: "space-between" }}>
            <Typography variant="h5" sx={{ marginY: "10px" }}>
              Quotes
            </Typography>
            <BasicModal
              title="Add Quotes"
              comp={
                <AddQuote
                  opt="add"
                  quotes={quotes}
                  setQuotes={setQuotes}
                  setOpen={setOpen}
                  setSnackbarMessage={setSnackbarMessage}
                  setSnackbarSeverity={setSnackbarSeverity}
                  setSnackbarOpen={setSnackbarOpen}
                  refetch={refetch}
                />
              }
              btn={true}
              open={open}
              setOpen={setOpen}
            />
          </Box>
          <UpdateModal
            title="Update Quote"
            comp={
              <AddQuote
                data={data}
                setOpenUpdate={setOpenUpdateBox}
                quotes={quotes}
                opt="update"
                setQuotes={setQuotes}
                setSnackbarMessage={setSnackbarMessage}
                setSnackbarSeverity={setSnackbarSeverity}
                setSnackbarOpen={setSnackbarOpen}
                refetch={refetch}
              />
            }
            btn={false}
            openUpdate={openUpdateBox}
            setOpenUpdate={setOpenUpdateBox}
          />
          {queryLoading ? (
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <CircularProgress color="secondary" />
            </Box>
          ) : (
            <>
              {quotes.length > 0 ? (
                <Box sx={{ maxWidth: "100%", overflow: "auto" }}>
                  <DataTable
                    columns={columns}
                    rows={quotes}
                    setData={setData}
                    setOpen={setOpenUpdateBox}
                    updateKey="quote"
                    setIndex={setIndex}
                    action={true}
                    deleteHandler={deleteHandler}
                  />
                </Box>
              ) : (
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                  <Typography variant="h5" sx={{ marginY: "10px" }}>
                    No data found
                  </Typography>
                </Box>
              )}
            </>
          )}
        </Box>
      </Dashboard>
      <SnackbarComponent open={snackbarOpen} handleClose={handleCloseSnackbar} severity={snackbarSeverity} message={snackbarMessage} />
    </>
  );
};

export default withAdminAuth(Quotes);