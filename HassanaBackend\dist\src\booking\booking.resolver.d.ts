import { BookingService } from './booking.service';
import { UpdateBookingInput } from './dto/update-booking.input';
import { UUID } from 'crypto';
export declare class BookingResolver {
    private readonly bookingService;
    constructor(bookingService: BookingService);
    findAll(): Promise<import("./entities/booking.entity").Booking[]>;
    getAllBookingOfTeaBoy(): Promise<import("./entities/booking.entity").Booking[]>;
    findByUser(id: UUID): Promise<import("./entities/booking.entity").Booking[]>;
    createBooking(updateBookingInput: UpdateBookingInput): Promise<import("./entities/booking.entity").Booking>;
}
