import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Alert,
  Box,
  Divider
} from '@mui/material';
import { Warning, ExitToApp, Refresh } from '@mui/icons-material';

/**
 * Dialog component for handling session conflicts
 * Shows when user is logged in from another device
 */
const SessionConflictDialog = ({ 
  open, 
  onClose, 
  onForceLogin, 
  onGoToLogin,
  conflictMessage = "You have been logged in from another device",
  deviceInfo = null 
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: 3
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 1,
        backgroundColor: 'warning.light',
        color: 'warning.contrastText'
      }}>
        <Warning />
        Session Conflict Detected
      </DialogTitle>
      
      <DialogContent sx={{ pt: 3 }}>
        <Alert severity="warning" sx={{ mb: 2 }}>
          {conflictMessage}
        </Alert>
        
        <Typography variant="body1" gutterBottom>
          Your account is currently active on another device or browser. For security reasons, 
          only one active session is allowed per user.
        </Typography>

        {deviceInfo && (
          <>
            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle2" gutterBottom>
              New Login Details:
            </Typography>
            <Box sx={{ pl: 2 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>Time:</strong> {new Date(deviceInfo.timestamp).toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>Device:</strong> {deviceInfo.userAgent || 'Unknown'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>IP:</strong> {deviceInfo.ip || 'Unknown'}
              </Typography>
            </Box>
          </>
        )}

        <Divider sx={{ my: 2 }} />
        
        <Typography variant="body2" color="text.secondary">
          You have the following options:
        </Typography>
        
        <Box component="ul" sx={{ mt: 1, pl: 3 }}>
          <Typography component="li" variant="body2" color="text.secondary">
            <strong>Continue Here:</strong> This will log out the other session and keep you logged in here
          </Typography>
          <Typography component="li" variant="body2" color="text.secondary">
            <strong>Go to Login:</strong> This will take you to the login page
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ p: 3, gap: 1 }}>
        <Button
          onClick={onGoToLogin}
          variant="outlined"
          startIcon={<ExitToApp />}
          color="primary"
        >
          Go to Login
        </Button>
        
        <Button
          onClick={onForceLogin}
          variant="contained"
          startIcon={<Refresh />}
          color="warning"
          autoFocus
        >
          Continue Here
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SessionConflictDialog;
