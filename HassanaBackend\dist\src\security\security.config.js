"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityConfig = void 0;
const common_1 = require("@nestjs/common");
let SecurityConfig = class SecurityConfig {
    static getCorsConfig() {
        const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
            'http://localhost:3000',
            'http://localhost:3001',
            'https://portal.hassana.com.sa',
            'https://v2-portal.hassana.com.sa'
        ];
        return {
            origin: (origin, callback) => {
                if (!origin)
                    return callback(null, true);
                if (allowedOrigins.includes(origin)) {
                    return callback(null, true);
                }
                else {
                    return callback(new Error('Not allowed by CORS'), false);
                }
            },
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            allowedHeaders: [
                'Origin',
                'X-Requested-With',
                'Content-Type',
                'Accept',
                'Authorization',
                'X-CSRF-Token',
                'X-Session-Token'
            ],
            credentials: true,
            preflightContinue: false,
            optionsSuccessStatus: 204,
        };
    }
    static getHelmetConfig() {
        return {
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                    connectSrc: ["'self'"],
                    fontSrc: ["'self'"],
                    objectSrc: ["'none'"],
                    mediaSrc: ["'self'"],
                    frameSrc: ["'none'"],
                },
            },
            crossOriginEmbedderPolicy: false,
            hsts: {
                maxAge: 31536000,
                includeSubDomains: true,
                preload: true,
            },
            noSniff: true,
            xssFilter: true,
            referrerPolicy: { policy: 'same-origin' },
        };
    }
    static getRateLimitConfig() {
        return {
            ttl: 60,
            limit: process.env.NODE_ENV === 'production' ? 100 : 1000,
            skipIf: (context) => {
                const request = context.switchToHttp().getRequest();
                return request.url === '/health' || request.url === '/';
            },
        };
    }
    static getGraphQLSecurityConfig() {
        return {
            introspection: process.env.NODE_ENV !== 'production',
            playground: process.env.NODE_ENV !== 'production',
            validationRules: process.env.NODE_ENV === 'production' ? [] : [],
            context: ({ req, res }) => {
                return {
                    req,
                    res,
                    csrfToken: req.headers['x-csrf-token'],
                    sessionToken: req.headers['x-session-token'],
                };
            },
        };
    }
    static getSessionConfig() {
        return {
            secret: process.env.SESSION_SECRET || 'change-this-in-production',
            resave: false,
            saveUninitialized: false,
            cookie: {
                secure: process.env.NODE_ENV === 'production',
                httpOnly: true,
                maxAge: 24 * 60 * 60 * 1000,
                sameSite: 'strict',
            },
        };
    }
    static getValidationConfig() {
        return {
            whitelist: true,
            forbidNonWhitelisted: true,
            transform: true,
            forbidUnknownValues: true,
            transformOptions: {
                enableImplicitConversion: true
            },
            exceptionFactory: (errors) => {
                const messages = errors.map(error => Object.values(error.constraints || {}).join(', '));
                return new Error(`Validation failed: ${messages.join('; ')}`);
            },
        };
    }
};
exports.SecurityConfig = SecurityConfig;
exports.SecurityConfig = SecurityConfig = __decorate([
    (0, common_1.Injectable)()
], SecurityConfig);
//# sourceMappingURL=security.config.js.map