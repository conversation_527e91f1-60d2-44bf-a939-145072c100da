import { Test, TestingModule } from '@nestjs/testing';
import { CsrfService } from './csrf.service';
import { CsrfGuard } from './csrf.guard';
import { ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

describe('Security Implementation', () => {
  let csrfService: CsrfService;
  let csrfGuard: CsrfGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CsrfService, CsrfGuard],
    }).compile();

    csrfService = module.get<CsrfService>(CsrfService);
    csrfGuard = module.get<CsrfGuard>(CsrfGuard);
  });

  describe('CsrfService', () => {
    it('should be defined', () => {
      expect(csrfService).toBeDefined();
    });

    it('should generate CSRF token', () => {
      const sessionToken = 'test-session-token';
      const csrfToken = csrfService.generateCsrfToken(sessionToken);
      
      expect(csrfToken).toBeDefined();
      expect(typeof csrfToken).toBe('string');
      expect(csrfToken.length).toBeGreaterThan(0);
    });

    it('should verify valid CSRF token', () => {
      const sessionToken = 'test-session-token';
      const csrfToken = csrfService.generateCsrfToken(sessionToken);
      
      const isValid = csrfService.verifyCsrfToken(csrfToken, sessionToken);
      expect(isValid).toBe(true);
    });

    it('should reject invalid CSRF token', () => {
      const sessionToken = 'test-session-token';
      const invalidToken = 'invalid-token';
      
      const isValid = csrfService.verifyCsrfToken(invalidToken, sessionToken);
      expect(isValid).toBe(false);
    });

    it('should generate different tokens for different sessions', () => {
      const sessionToken1 = 'session-1';
      const sessionToken2 = 'session-2';
      
      const csrfToken1 = csrfService.generateCsrfToken(sessionToken1);
      const csrfToken2 = csrfService.generateCsrfToken(sessionToken2);
      
      expect(csrfToken1).not.toBe(csrfToken2);
    });

    it('should generate same token for same session', () => {
      const sessionToken = 'test-session-token';
      
      const csrfToken1 = csrfService.generateCsrfToken(sessionToken);
      const csrfToken2 = csrfService.generateCsrfToken(sessionToken);
      
      expect(csrfToken1).toBe(csrfToken2);
    });

    it('should generate session token', () => {
      const sessionToken = csrfService.generateSessionToken();
      
      expect(sessionToken).toBeDefined();
      expect(typeof sessionToken).toBe('string');
      expect(sessionToken.length).toBe(64); // 32 bytes * 2 (hex)
    });

    it('should create CSRF response', () => {
      const response = csrfService.createCsrfResponse();
      
      expect(response).toHaveProperty('csrfToken');
      expect(response).toHaveProperty('sessionToken');
      expect(typeof response.csrfToken).toBe('string');
      expect(typeof response.sessionToken).toBe('string');
    });
  });

  describe('CsrfGuard', () => {
    it('should be defined', () => {
      expect(csrfGuard).toBeDefined();
    });

    it('should allow GET requests', async () => {
      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => ({ method: 'GET' })
        })
      } as ExecutionContext;

      // Mock GqlExecutionContext.create
      jest.spyOn(GqlExecutionContext, 'create').mockReturnValue({
        getContext: () => ({
          req: { method: 'GET' }
        })
      } as any);

      const result = await csrfGuard.canActivate(mockContext);
      expect(result).toBe(true);
    });

    it('should reject POST requests without CSRF token', async () => {
      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => ({ 
            method: 'POST',
            headers: {}
          })
        })
      } as ExecutionContext;

      jest.spyOn(GqlExecutionContext, 'create').mockReturnValue({
        getContext: () => ({
          req: { 
            method: 'POST',
            headers: {}
          }
        })
      } as any);

      await expect(csrfGuard.canActivate(mockContext)).rejects.toThrow('CSRF token is required');
    });

    it('should reject POST requests with invalid CSRF token', async () => {
      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => ({ 
            method: 'POST',
            headers: {
              'x-csrf-token': 'invalid-token',
              'x-session-token': 'session-token'
            }
          })
        })
      } as ExecutionContext;

      jest.spyOn(GqlExecutionContext, 'create').mockReturnValue({
        getContext: () => ({
          req: { 
            method: 'POST',
            headers: {
              'x-csrf-token': 'invalid-token',
              'x-session-token': 'session-token'
            }
          }
        })
      } as any);

      await expect(csrfGuard.canActivate(mockContext)).rejects.toThrow('Invalid CSRF token');
    });
  });
});
