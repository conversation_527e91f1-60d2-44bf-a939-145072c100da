{"version": 3, "file": "csrf.guard.js", "sourceRoot": "", "sources": ["../../../src/security/csrf.guard.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAMwB;AACxB,6CAAsD;AAEtD,iCAAiC;AAG1B,IAAM,SAAS,GAAf,MAAM,SAAS;IAAf;QACY,eAAU,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,0CAA0C,CAAC;IA8DtG,CAAC;IA5DC,KAAK,CAAC,WAAW,CAAC,OAAyB;QACzC,MAAM,GAAG,GAAG,6BAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,CAAC;QAC7D,MAAM,OAAO,GAAY,GAAG,CAAC,GAAG,CAAC;QAGjC,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE,CAAC;YAChF,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,CAAW,CAAC;QAC5D,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,iBAAiB,CAAW,CAAC;QAElE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,MAAM,IAAI,4BAAmB,CAAC,wBAAwB,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,4BAAmB,CAAC,2BAA2B,CAAC,CAAC;QAC7D,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,2BAAkB,CAAC,oBAAoB,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,oBAAoB,CAAC,OAAgB;QAC3C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1B,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC1E,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,eAAe,CAAC,KAAa,EAAE,YAAoB;QACzD,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAC3D,OAAO,MAAM,CAAC,eAAe,CAC3B,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,EACzB,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAClC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,iBAAiB,CAAC,YAAoB;QACpC,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAC1D,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;CACF,CAAA;AA/DY,8BAAS;oBAAT,SAAS;IADrB,IAAA,mBAAU,GAAE;GACA,SAAS,CA+DrB"}