.MuiPaper-root {
  border: none !important;
}
.logo {
  width: 30px;
  height: 30px;
}
/* .paper{
  outline: none!important;
} */
.text {
  margin-left: 3rem;
  /* margin-top: 100px; */
  @media (max-width: 900px) {
    margin-left: 1rem;
    display: none;
  }
}

/* Container */
.container {
  display: flex;
  background-image: url(/images/login/login.png);
  background-repeat: no-repeat;
  object-fit: cover;
  border-radius: 7px;
  height: 525px !important;
  @media (max-width: 900px) {
    flex-direction: column;
    background-image: none;
  }
}

.button {
  border-radius: 10px;
  border: 2px solid #a665e1;
  margin-top: 20px;
  background: #a665e1;
  width: 100%;
}
/* Welcome text */
.welcomeText {
  font-weight: 800 !important;
  margin-top: 2rem !important;
  font-style: normal;
  font-size: 2.5rem !important;
  width: 50%;
  color: white;

  @media (max-width: 1440px) {
    font-size: 24px;
  }
}

/* Main text */
.mainText {
  margin-top: 30px !important;
  font-size: 14px;
  color: white;

  @media (max-width: 1440px) {
    font-size: 18px;
  }
}

/* Additional text */
.additionalText {
  margin-top: 20px;
  font-family: Helvetica;
  font-weight: 400;
  color: white;
  font-size: 14px;
  @media (max-width: 1440px) {
    font-size: 12px;
  }
}
