import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  Typography,
  Avatar,
  Box,
  Button,
  TextField,
  Rating,
  Alert,
} from '@mui/material';
import StarIcon from '@mui/icons-material/Star';
import { lightTheme } from '@/theme';
import ThankYouModal from './NewModal';
import { baseUrl } from '@/Data/ApolloClient';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';

const RatePopup = ({ open, onClose, employeeId, profileImage, employeeName, onReviewSubmitted }) => {
  const { data: session } = useSession();
  const [rating, setRating] = useState(1);
  const [review, setReview] = useState('');
  const [successOpen, setSuccessOpen] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const router = useRouter();

  // Cancel handler
  const handleClose = () => {
    setRating(1);
    setReview('');
    setErrorMessage(null);
    onClose();
  };

  // Submit handler with API call
  const handleSubmit = async () => {
    setErrorMessage(null);

    if (!session?.accessToken) {
      setErrorMessage('Please log in to submit a review.');
      return;
    }

    if (!employeeId) {
      setErrorMessage('Employee ID is missing.');
      return;
    }

    const payload = {
      rating,
      review: review || undefined,
      user_id: employeeId,
    };

    try {
      const response = await fetch(`${baseUrl}/v1/user-reviews`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${session.accessToken}`,
        },
        body: JSON.stringify(payload),
        
      });
        
        
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.errorMessage || 'Failed to submit review');
      }

      if (result.status && result.data) {
        setSuccessOpen(true);
        setTimeout(() => {
          setSuccessOpen(false);
          handleClose();
          if (onReviewSubmitted) onReviewSubmitted(); 
         
            router.push('/AddressBook'); 
         
        }, 2000);
      } else {
        throw new Error(result.errorMessage || 'Review submission failed');
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      let message = error.message || 'An error occurred while submitting your review.';
      if (error.message.includes('Unauthorized')) {
        message = 'Authentication failed. Please log in again.';
      } else if (error.message.includes('Validation')) {
        message = 'Invalid review data. Please check your input.';
      }
      setErrorMessage(message);
    }
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ fontWeight: 'bold', fontSize: "24px" }}>Thank you program</DialogTitle>
        <DialogContent>
          {errorMessage && (
            <Alert severity="error" sx={{ mb: 2 }} onClose={() => setErrorMessage(null)}>
              {errorMessage}
            </Alert>
          )}
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Avatar src={profileImage} alt={employeeName} />
            <Box>
              <Typography fontWeight="bold" fontSize={20} color={lightTheme.palette.text.purple}>
                {employeeName || 'Employee Review'}
              </Typography>
              <Typography variant="body2" color={lightTheme.palette.text.gray}>
                ID: {employeeId}
              </Typography>
            </Box>
          </Box>

          <Typography mb={1} color={lightTheme.palette.text.gray}>
            Rate this Employee
          </Typography>
          <Rating
            name="employee-rating"
            value={rating}
            max={1}
            onChange={(event, newValue) => {
              setRating(newValue || 1);
            }}
            icon={<StarIcon fontSize="inherit" />}
          />

          <TextField
            label="Review (Optional)"
            placeholder="Add Your Remarks"
            fullWidth
            multiline
            minRows={4}
            variant="outlined"
            margin="normal"
            value={review}
            onChange={(e) => setReview(e.target.value)}
          />

          <Box sx={{ display: 'flex', width: '100%', gap: 2 }}>
            <Button
              fullWidth
              variant="outlined"
              onClick={handleClose}
              sx={{
                borderColor: '#a66bcb',
                color: '#a66bcb',
                fontWeight: 'bold',
                borderRadius: 2,
                py: 1.5,
                '&:hover': {
                  borderColor: '#914db9',
                  backgroundColor: '#f9f2fd',
                },
              }}
            >
              Cancel
            </Button>
            <Button
              fullWidth
              variant="outlined"
              onClick={handleSubmit}
              sx={{
                borderColor: '#a66bcb',
                color: '#a66bcb',
                fontWeight: 'bold',
                borderRadius: 2,
                py: 1.5,
                '&:hover': {
                  backgroundColor: '#914db9',
                  color: '#fff',
                },
              }}
            >
              Confirm
            </Button>
          </Box>
        </DialogContent>
      </Dialog>

      <ThankYouModal open={successOpen} onClose={() => setSuccessOpen(false)} />
    </>
  );
};

export default RatePopup;