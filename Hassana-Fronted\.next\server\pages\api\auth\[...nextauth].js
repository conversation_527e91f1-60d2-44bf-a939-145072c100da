"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "@apollo/client":
/*!*********************************!*\
  !*** external "@apollo/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@apollo/client");

/***/ }),

/***/ "@apollo/client/link/context":
/*!**********************************************!*\
  !*** external "@apollo/client/link/context" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("@apollo/client/link/context");

/***/ }),

/***/ "@apollo/client/link/error":
/*!********************************************!*\
  !*** external "@apollo/client/link/error" ***!
  \********************************************/
/***/ ((module) => {

module.exports = require("@apollo/client/link/error");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/credentials":
/*!**************************************************!*\
  !*** external "next-auth/providers/credentials" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/credentials");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "axios":
/*!************************!*\
  !*** external "axios" ***!
  \************************/
/***/ ((module) => {

module.exports = import("axios");;

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.js&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.js&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_nextauth_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\[...nextauth].js */ \"(api)/./src/pages/api/auth/[...nextauth].js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_api_auth_nextauth_js__WEBPACK_IMPORTED_MODULE_3__]);\n_src_pages_api_auth_nextauth_js__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_nextauth_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/Data/ApolloClient.js":
/*!**********************************!*\
  !*** ./src/Data/ApolloClient.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baseUrl: () => (/* binding */ baseUrl),\n/* harmony export */   base_url: () => (/* binding */ base_url),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _apollo_client_link_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @apollo/client/link/context */ \"@apollo/client/link/context\");\n/* harmony import */ var _apollo_client_link_context__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_apollo_client_link_context__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client_link_error__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @apollo/client/link/error */ \"@apollo/client/link/error\");\n/* harmony import */ var _apollo_client_link_error__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_apollo_client_link_error__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_csrfUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/csrfUtils */ \"(api)/./src/utils/csrfUtils.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_csrfUtils__WEBPACK_IMPORTED_MODULE_4__]);\n_utils_csrfUtils__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n//export const baseUrl = \"http://*************:3001/v1\";\n//export const baseUrl = \"https://portal.hassana.com.sa/v1\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com/v1\";\n////export const baseUrl = \"http://localhost:3001\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com\";\nconst baseUrl = \"https://v2-portal.hassana.com.sa\";\nconst base_url = \"https://v2-portal.hassana.com.sa/v1\";\n//export const base_url = \"https://localhost:3001/v1\";\nconst httpLink = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.createHttpLink)({\n    uri: baseUrl + \"/graphql\"\n});\n// Auth link to add JWT token and CSRF headers\nconst authLink = (0,_apollo_client_link_context__WEBPACK_IMPORTED_MODULE_1__.setContext)(async (_, { headers })=>{\n    // Get token from session or localStorage\n    let token = null;\n    try {\n        const session = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.getSession)();\n        token = session?.accessToken;\n    } catch (error) {\n        console.warn(\"Could not get session:\", error);\n    }\n    // Fallback to localStorage if session token not available\n    if (!token && \"undefined\" !== \"undefined\") {}\n    // Get CSRF headers for secure requests\n    let csrfHeaders = {};\n    try {\n        csrfHeaders = await (0,_utils_csrfUtils__WEBPACK_IMPORTED_MODULE_4__.getSecureHeaders)();\n    } catch (error) {\n        console.warn(\"Could not get CSRF headers:\", error);\n    }\n    return {\n        headers: {\n            ...headers,\n            ...token && {\n                authorization: `Bearer ${token}`\n            },\n            ...csrfHeaders\n        }\n    };\n});\n// Error link to handle GraphQL and network errors\nconst errorLink = (0,_apollo_client_link_error__WEBPACK_IMPORTED_MODULE_2__.onError)(({ graphQLErrors, networkError })=>{\n    if (graphQLErrors) {\n        graphQLErrors.forEach(({ message, locations, path, extensions })=>{\n            console.error(`[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`);\n            // Handle authentication errors\n            if (extensions?.code === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\")) {\n                console.warn(\"Authentication error detected, redirecting to login...\");\n                // Clear invalid token\n                if (false) {}\n                // Redirect to login page\n                if (false) {}\n            }\n            // Handle authorization errors\n            if (extensions?.code === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\")) {\n                console.warn(\"Authorization error detected\");\n                // You can show a toast or redirect to unauthorized page\n                if (false) {}\n            }\n        });\n    }\n    if (networkError) {\n        console.error(`[Network error]: ${networkError}`);\n        // Handle network errors that might indicate auth issues\n        if (networkError.statusCode === 401) {\n            console.warn(\"Network 401 error, clearing token and redirecting...\");\n            if (false) {}\n        }\n    }\n});\nconst client = new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.ApolloClient({\n    link: (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.from)([\n        errorLink,\n        authLink,\n        httpLink\n    ]),\n    cache: new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.InMemoryCache(),\n    defaultOptions: {\n        watchQuery: {\n            errorPolicy: \"all\"\n        },\n        query: {\n            errorPolicy: \"all\"\n        }\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (client);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/Data/ApolloClient.js\n");

/***/ }),

/***/ "(api)/./src/Data/ApolloClientLocal.js":
/*!***************************************!*\
  !*** ./src/Data/ApolloClientLocal.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baseUrl: () => (/* binding */ baseUrl),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\n//export const baseUrl = \"http://*************:3001/v1\";\n//export const baseUrl = \"https://portal.hassana.com.sa/v1\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com/v1\";\nconst baseUrl = \"http://localhost:3001\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com\";\n//export const baseUrl = \"https://v2-api.hassana.com.sa\";\nconst httpLink = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.createHttpLink)({\n    uri: baseUrl + \"/graphql\"\n});\nconst Client = new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.ApolloClient({\n    link: httpLink,\n    cache: new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.InMemoryCache(),\n    onError: ({ operation, networkError, response })=>{\n        console.log(`[GraphQL Error]: Operation: ${operation.operationName}, Message: ${networkError?.message}, Response: `, response);\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Client);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/Data/ApolloClientLocal.js\n");

/***/ }),

/***/ "(api)/./src/Data/Auth.js":
/*!**************************!*\
  !*** ./src/Data/Auth.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LOGIN_USER: () => (/* binding */ LOGIN_USER)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst LOGIN_USER = _apollo_client__WEBPACK_IMPORTED_MODULE_0__.gql`\r\n  query LoginUser($username: String!, $password: String!) {\r\n    loginUser(username: $username, password: $password) {\r\n      username\r\n      role\r\n      token\r\n      id\r\n    }\r\n  }\r\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvRGF0YS9BdXRoLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxQztBQUU5QixNQUFNQyxhQUFhRCwrQ0FBRyxDQUFDOzs7Ozs7Ozs7QUFTOUIsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktZGFzaGJvYXJkLy4vc3JjL0RhdGEvQXV0aC5qcz82MDBjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdxbCB9IGZyb20gXCJAYXBvbGxvL2NsaWVudFwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IExPR0lOX1VTRVIgPSBncWxgXHJcbiAgcXVlcnkgTG9naW5Vc2VyKCR1c2VybmFtZTogU3RyaW5nISwgJHBhc3N3b3JkOiBTdHJpbmchKSB7XHJcbiAgICBsb2dpblVzZXIodXNlcm5hbWU6ICR1c2VybmFtZSwgcGFzc3dvcmQ6ICRwYXNzd29yZCkge1xyXG4gICAgICB1c2VybmFtZVxyXG4gICAgICByb2xlXHJcbiAgICAgIHRva2VuXHJcbiAgICAgIGlkXHJcbiAgICB9XHJcbiAgfVxyXG5gO1xyXG4iXSwibmFtZXMiOlsiZ3FsIiwiTE9HSU5fVVNFUiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./src/Data/Auth.js\n");

/***/ }),

/***/ "(api)/./src/Data/AuthClientServer.js":
/*!**************************************!*\
  !*** ./src/Data/AuthClientServer.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client */ \"@apollo/client\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_apollo_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ApolloClientLocal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ApolloClientLocal */ \"(api)/./src/Data/ApolloClientLocal.js\");\n\n\n//export const baseUrl = \"http://*********:3001\";\n//export const baseUrl = \"https://portal.hassana.com.sa/v1\";\n// export const baseUrl = \"http://*************:3001\";\n// 1. Create a logging link\nconst loggingLink = new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.ApolloLink((operation, forward)=>{\n    // This code runs before each request\n    console.log(`[Request] Starting: ${operation.operationName}`, {\n        variables: operation.variables\n    });\n    //export const baseurl = \"http://localhost:3500\";\n    // Call the next link in the chain\n    return forward(operation).map((response)=>{\n        // This code runs after a successful response is received\n        console.log(`[Response] Completed: ${operation.operationName}`, {\n            data: response.data\n        });\n        return response;\n    });\n});\nconst httpLink = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_0__.createHttpLink)({\n    uri: _ApolloClientLocal__WEBPACK_IMPORTED_MODULE_1__.baseUrl + \"/graphql\"\n});\nconst authclient = new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.ApolloClient({\n    link: _apollo_client__WEBPACK_IMPORTED_MODULE_0__.ApolloLink.from([\n        loggingLink,\n        httpLink\n    ]),\n    cache: new _apollo_client__WEBPACK_IMPORTED_MODULE_0__.InMemoryCache(),\n    onError: ({ operation, networkError, response })=>{\n        console.log(`[GraphQL Error]: Operation: ${operation.operationName}, Message: ${networkError?.message}, Response: `, response);\n    }\n});\nauthclient.resetCache = async ()=>{\n    await authclient.cache.reset();\n    console.log(\"Apollo Client cache has been reset.\");\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authclient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/Data/AuthClientServer.js\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].js":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].js ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _Data_AuthClientServer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/Data/AuthClientServer */ \"(api)/./src/Data/AuthClientServer.js\");\n/* harmony import */ var _Data_Auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Data/Auth */ \"(api)/./src/Data/Auth.js\");\n/* harmony import */ var _Data_ApolloClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/Data/ApolloClient */ \"(api)/./src/Data/ApolloClient.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/providers/credentials */ \"next-auth/providers/credentials\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _Data_ApolloClientLocal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/Data/ApolloClientLocal */ \"(api)/./src/Data/ApolloClientLocal.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_2__]);\n_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_3___default()({\n    providers: [\n        next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_4___default()({\n            name: \"Credentials\",\n            credentials: {\n                username: {\n                    label: \"Username\",\n                    type: \"text\",\n                    placeholder: \"jsmith\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\"\n                }\n            },\n            async authorize (credentials) {\n                const { username, password } = credentials;\n                console.log(\"\\uD83D\\uDD10 Login attempt received:\", {\n                    username\n                });\n                try {\n                    console.log(\"\\uD83D\\uDCE1 Sending GraphQL query to backend...\");\n                    const { data, error } = await _Data_AuthClientServer__WEBPACK_IMPORTED_MODULE_0__[\"default\"].query({\n                        query: _Data_Auth__WEBPACK_IMPORTED_MODULE_1__.LOGIN_USER,\n                        variables: {\n                            username,\n                            password\n                        }\n                    });\n                    console.log(\"\\uD83D\\uDCCA GraphQL Response:\", {\n                        data,\n                        error\n                    });\n                    if (error) {\n                        console.error(\"❌ GraphQL Error:\", error);\n                        console.error(\"Error details:\", error.graphQLErrors, error.networkError);\n                        return null;\n                    }\n                    const { loginUser } = data;\n                    console.log(\"\\uD83D\\uDC64 Login User Data:\", loginUser);\n                    if (loginUser) {\n                        console.log(\"✅ Login successful:\", loginUser);\n                        return {\n                            ...loginUser,\n                            accessToken: loginUser.token || \"test\"\n                        };\n                    } else {\n                        console.log(\"❌ No user found or invalid credentials\");\n                        return null;\n                    }\n                } catch (error) {\n                    console.error(\"\\uD83D\\uDD25 Authorization Error:\", error);\n                    console.error(\"Error message:\", error.message);\n                    console.error(\"Error stack:\", error.stack);\n                    if (error.networkError) {\n                        console.error(\"Network error details:\", error.networkError);\n                    }\n                    return null;\n                }\n            }\n        })\n    ],\n    session: {\n        jwt: false\n    },\n    jwt: {\n        secret: \"test\",\n        encryption: true\n    },\n    callbacks: {\n        async jwt ({ token, user }) {\n            console.log(\"JWT----->\", user);\n            if (user) {\n                token.id = user.id;\n                token.role = user.role.toUpperCase();\n                token.name = user.username;\n                token.accessToken = user.accessToken;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            session.user.id = token.id;\n            session.user.role = token.role;\n            session.user.username = token.name;\n            session.accessToken = token.accessToken;\n            console.log(\"JSESSIOWT----->\", session);\n            return session;\n        }\n    }\n}));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].js\n");

/***/ }),

/***/ "(api)/./src/utils/csrfUtils.js":
/*!********************************!*\
  !*** ./src/utils/csrfUtils.js ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearCsrfTokens: () => (/* binding */ clearCsrfTokens),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getSecureHeaders: () => (/* binding */ getSecureHeaders),\n/* harmony export */   getSessionToken: () => (/* binding */ getSessionToken),\n/* harmony export */   initializeCsrfTokens: () => (/* binding */ initializeCsrfTokens),\n/* harmony export */   verifyCsrfToken: () => (/* binding */ verifyCsrfToken)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var _Data_ApolloClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/Data/ApolloClient */ \"(api)/./src/Data/ApolloClient.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_0__, _Data_ApolloClient__WEBPACK_IMPORTED_MODULE_1__]);\n([axios__WEBPACK_IMPORTED_MODULE_0__, _Data_ApolloClient__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n/**\n * CSRF Token Management Utility\n * Handles fetching, storing, and using CSRF tokens for secure requests\n */ class CsrfManager {\n    constructor(){\n        this.csrfToken = null;\n        this.sessionToken = null;\n        this.tokenExpiry = null;\n    }\n    /**\n   * Fetch CSRF token from backend\n   * @returns {Promise<{csrfToken: string, sessionToken: string}>}\n   */ async fetchCsrfToken() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`${_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_1__.baseUrl}/security/csrf-token`, {\n                withCredentials: true\n            });\n            const { csrfToken, sessionToken } = response.data;\n            // Store tokens\n            this.csrfToken = csrfToken;\n            this.sessionToken = sessionToken;\n            this.tokenExpiry = Date.now() + 23 * 60 * 60 * 1000; // 23 hours\n            // Store in localStorage for persistence\n            localStorage.setItem(\"csrf-token\", csrfToken);\n            localStorage.setItem(\"session-token\", sessionToken);\n            localStorage.setItem(\"csrf-expiry\", this.tokenExpiry.toString());\n            console.log(\"CSRF token fetched successfully\");\n            return {\n                csrfToken,\n                sessionToken\n            };\n        } catch (error) {\n            console.error(\"Failed to fetch CSRF token:\", error);\n            throw new Error(\"Failed to fetch CSRF token\");\n        }\n    }\n    /**\n   * Get current CSRF token, fetch new one if expired\n   * @returns {Promise<string>}\n   */ async getCsrfToken() {\n        // Check if we have a valid token\n        if (this.isTokenValid()) {\n            return this.csrfToken;\n        }\n        // Try to load from localStorage\n        const storedToken = localStorage.getItem(\"csrf-token\");\n        const storedSession = localStorage.getItem(\"session-token\");\n        const storedExpiry = localStorage.getItem(\"csrf-expiry\");\n        if (storedToken && storedSession && storedExpiry) {\n            const expiry = parseInt(storedExpiry);\n            if (Date.now() < expiry) {\n                this.csrfToken = storedToken;\n                this.sessionToken = storedSession;\n                this.tokenExpiry = expiry;\n                return this.csrfToken;\n            }\n        }\n        // Fetch new token\n        const { csrfToken } = await this.fetchCsrfToken();\n        return csrfToken;\n    }\n    /**\n   * Get current session token\n   * @returns {Promise<string>}\n   */ async getSessionToken() {\n        if (!this.isTokenValid()) {\n            await this.getCsrfToken(); // This will fetch both tokens\n        }\n        return this.sessionToken;\n    }\n    /**\n   * Check if current token is valid\n   * @returns {boolean}\n   */ isTokenValid() {\n        return this.csrfToken && this.sessionToken && this.tokenExpiry && Date.now() < this.tokenExpiry;\n    }\n    /**\n   * Clear stored tokens\n   */ clearTokens() {\n        this.csrfToken = null;\n        this.sessionToken = null;\n        this.tokenExpiry = null;\n        localStorage.removeItem(\"csrf-token\");\n        localStorage.removeItem(\"session-token\");\n        localStorage.removeItem(\"csrf-expiry\");\n    }\n    /**\n   * Get headers for secure requests\n   * @returns {Promise<Object>}\n   */ async getSecureHeaders() {\n        const csrfToken = await this.getCsrfToken();\n        const sessionToken = await this.getSessionToken();\n        return {\n            \"X-CSRF-Token\": csrfToken,\n            \"X-Session-Token\": sessionToken\n        };\n    }\n    /**\n   * Verify CSRF token with backend (for testing)\n   * @returns {Promise<boolean>}\n   */ async verifyCsrfToken() {\n        try {\n            const headers = await this.getSecureHeaders();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(`${_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_1__.baseUrl}/security/verify-csrf`, {}, {\n                headers,\n                withCredentials: true\n            });\n            return response.data.valid;\n        } catch (error) {\n            console.error(\"CSRF token verification failed:\", error);\n            return false;\n        }\n    }\n}\n// Create singleton instance\nconst csrfManager = new CsrfManager();\n/**\n * Get CSRF token for use in requests\n * @returns {Promise<string>}\n */ const getCsrfToken = ()=>csrfManager.getCsrfToken();\n/**\n * Get session token for use in requests\n * @returns {Promise<string>}\n */ const getSessionToken = ()=>csrfManager.getSessionToken();\n/**\n * Get secure headers for GraphQL requests\n * @returns {Promise<Object>}\n */ const getSecureHeaders = ()=>csrfManager.getSecureHeaders();\n/**\n * Clear CSRF tokens (on logout)\n */ const clearCsrfTokens = ()=>csrfManager.clearTokens();\n/**\n * Initialize CSRF tokens (call on app startup)\n * @returns {Promise<void>}\n */ const initializeCsrfTokens = async ()=>{\n    try {\n        await csrfManager.getCsrfToken();\n        console.log(\"CSRF tokens initialized\");\n    } catch (error) {\n        console.warn(\"Failed to initialize CSRF tokens:\", error);\n    }\n};\n/**\n * Verify CSRF token (for testing)\n * @returns {Promise<boolean>}\n */ const verifyCsrfToken = ()=>csrfManager.verifyCsrfToken();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (csrfManager);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/utils/csrfUtils.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();