import { gql } from "@apollo/client";

export const mutationCreateEvent = gql`
mutation createEvent($createEventInput: CreateEventInput!) {
  createEvent(createEventInput: $createEventInput) {
    id
    title
    details
    category
    status
    date
    createdAt
    updatedAt
  }
}
`;

export const mutationUpdateEvent = gql`
  mutation updateEvent($id: ID!, $updateEventInput: UpdateEventInput!) {
  updateEvent(id: $id, updateEventInput: $updateEventInput) {
    id
    title
    details
    category
    status
    date
    createdAt
    updatedAt
  }
}
`;


export const getEvents = gql`
  query {
    events {
      id
      title
      details
      category
      status
      date
      createdAt
      updatedAt
    }
  }
`;
export const getTodaysEvents = gql`
query GetEvents($today: DateTime!, $category:String!) {
  todaysEvents(date: $today, category: $category) {
    id
    title
    details
    category
    status
    date
    createdAt
    updatedAt
  }
}
`;
// # export const getTodaysEvents = gql`
// #   query {
// #     events(where: { date: { eq: "2023-11-29" } }) {
// #       id
// #       title
// #       details
// #       category
// #       status
// #       date
// #       createdAt
// #       updatedAt
// #     }
// #   }
// # `;
export const mutationRemoveEvent = gql`
  mutation RemoveEvent($id: ID!) {
    removeEvent(id: $id) {
      #id
      title
    }
  }
`;