import { StreamableFile } from '@nestjs/common';
import { Response } from 'express';
export declare class FileSystemController {
    private readonly basePath;
    private getAbsolutePath;
    createFolder(folderPath: string, folderName: string): Promise<{
        message: string;
        path: string;
    }>;
    uploadFile(file: Express.Multer.File, folderPath?: string): {
        message: string;
        path: string;
        fileName: string;
    };
    listDirectory(folderPath?: string): any[];
    getFile(filePath: string, download: string, res: Response): Promise<StreamableFile>;
    deleteItem(itemPath: string): {
        message: string;
    };
    renameFile(filePath: string, newName: string): {
        message: string;
        path: string;
        fileName: string;
    };
    renameFolder(folderPath: string, newName: string): {
        message: string;
        path: string;
        folderName: string;
    };
}
