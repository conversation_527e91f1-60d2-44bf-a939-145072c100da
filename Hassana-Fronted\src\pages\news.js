import { useState, useEffect } from "react";
import { styled } from "@mui/system";
import { Box, Grid } from "@mui/material";
import AnnouncementDialog from "@/components/AnnouncementDialog";
import NewsList from "@/components/News";
import PreviousAnnouncement from "@/components/PreviousAnnouncement";
import NewsSection from "@/components/NewsSection";
import CurrentAnnouncement from "@/components/CurrentAnnouncement";
import ViewAnnouncement from "@/components/ViewAnnouncement";
import InformationController from "@/components/InformationController";
import useMediaQuery from "@mui/material/useMediaQuery";
import PreviousAnnouncementHeading from "@/components/PreviousAnnouncementHeading";
import Dashboard from "@/components/Dashboard";
import { useTheme } from "@mui/material/styles";
import { useColor } from "@/components/ColorContext";
import {
  getSortedAnnouncements,
  separateLatestData,
  useSelectedColor,
} from "@/components/HelperFunctions";
import { getAllAnnouncements, getAnnouncements } from "@/Data/Announcement";
import { useQuery } from "@apollo/client";
import withAuth from "@/components/auth/withAuth";
import { getInternalNews,  getAllNews } from "@/Data/News";
import { useSession } from "next-auth/react";



const ResponsiveBox = styled(Box)(

  ({ theme, backgroundColor, disableBorder, isCurrentAnnouncement }) => ({
    width: "100%",
    height: 170,
    background: backgroundColor || "white",
    backgroundColor: !isCurrentAnnouncement
      ? theme.palette.type === "light"
        ? "#F6F5FD"
        : theme.palette.background.secondary
      : theme.palette.background.secondary,
    boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.05)",
    borderRadius: 10,
    position: "relative",

    "&::before": !disableBorder && {
      content: '""',
      position: "absolute",
      top: 0,
      left: 0.6,
      width: "5px",
      height: "100%",
      background: !isCurrentAnnouncement
        ? theme.palette.type === "light"
          ? "#F6F5FD"
          : theme.palette.background.secondary
        : isCurrentAnnouncement
          ? `linear-gradient(180deg, #A665E1 0%, #62B6F3 99.99%)`
          : "none",
      borderRadius: "20px 0 0 20px",
    },
  })
);

const News = () => {
  const [announcements, setAnnouncements] = useState([]);
  const [openModal, setOpenModal] = useState(false);
  const [selectedAnnouncement, setSelectedAnnouncement] = useState();
  const [activeButton, setActiveButton] = useState("announcements");
  const { data: session, status } = useSession();
  const handleButtonClick = (buttonType) => {
    setDisplay(buttonType);
    setActiveButton(buttonType);
  };
  const handleOpenModal = (announcement) => {
    setSelectedAnnouncement(announcement);
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };
  const [display, setDisplay] = useState("announcements");
  const isWideScreen = useMediaQuery("(max-width:1150px)");
  const isLargeTablet = useMediaQuery("(max-width:1079px)");
  const isTablet = useMediaQuery("(max-width:1060px)");
  const isSmallTablet = useMediaQuery("(max-width:967px)");
  const isWideMobile = useMediaQuery("(max-width:869px)");
  const isMediumMobile = useMediaQuery("(max-width:823px)");
  const isNarrowMobile = useMediaQuery("(max-width:528px)");

  const isStandardDesktop = useMediaQuery("(min-width:967px)");

  const isXXLargeScreen = useMediaQuery("(min-width:1921px)");

  const isXLargeScreen = useMediaQuery(
    "(min-width:1701px) and (max-width:1920px)"
  );

  const isExtraLargeScreen = useMediaQuery(
    "(min-width:1401px) and (max-width:1700px)"
  );
  const isLargeScreen = useMediaQuery(
    "(min-width:1201px) and (max-width:1400px)"
  );
  const isMediumScreen2 = useMediaQuery(
    "(min-width:1025px) and (max-width:1200px)"
  );
  const isMediumScreen = useMediaQuery(
    "(min-width:769px) and (max-width:1024px)"
  );
  const isSmallScreen = useMediaQuery(
    "(min-width:481px) and (max-width:768px)"
  );
  const isExtraSmallScreen = useMediaQuery("(max-width:480px)");

  // const currentAnnouncements = announcements.slice(0, 2);
  // const previousAnnouncements = announcements.slice(2);
  const theme = useTheme();
  const { color } = useColor();
  const selectedColor = useSelectedColor(color);

  console.log({ theme })

  // const {                
  //   loading: queryLoading,
  //   error: queryError,
  //   data: { announcements: announcementsData } = {},
  // } = useQuery(getAnnouncements);
  // // console.log(announcementsData);
  const [news, setNews] = useState([]);

  useEffect(() => {
    const fetchNews = async () => {
      try {
        let response = await getInternalNews();

        console.log("internalll", separateLatestData(response.data));
        setNews(separateLatestData(response.data));

      } catch (error) {
        console.log(error);
      }
    }
    fetchNews()
  }, []);

  useEffect(() => {
    const fetchAnnouncements = async () => {
      try {
        let response = await getAllAnnouncements(session.accessToken);
        console.log("internal Announcements", response.data);
        setAnnouncements(response.data);

      } catch (error) {
        console.log(error);
      }
    }
    fetchAnnouncements()
  }, [])

  // useEffect(() => {
  //   if (!queryLoading && !queryError && announcementsData) {
  //     setAnnouncements(announcementsData);
  //     setSelectedAnnouncement(announcementsData[0]);
  //   }
  // }, [queryLoading, queryError, announcementsData]);

  console.log(announcements, "iuhyuikhkjhljooooo");

  return (
    <>
      <Dashboard>
        <Box sx={{ display: "flex", }}>
          <Box
            component="main"
            sx={{
              background:
                selectedColor == theme.palette.background.primary
                  ? theme.palette.background.secondary
                  : selectedColor,
              paddingLeft: isLargeTablet ? "50px" : "",
              paddingRight: isLargeTablet ? "50px" : "",
              overflow: "auto",
              overflowY: "auto",

              overflowX: "hidden",
              height: "100vh",
              "&::-webkit-scrollbar": {
                width: 0,
              },
              width: isMediumMobile
                ? "100%"
                : isWideMobile
                  ? "45rem"
                  : isSmallTablet
                    ? "48rem"
                    : isTablet
                      ? "54rem"
                      : "60vw",
              margin: "auto",
            }}
          >
            <InformationController
              handleButtonClick={handleButtonClick}
              activeButton={activeButton}
              isWideScreen={isWideScreen}
              isNarrowMobile={isNarrowMobile}
              isStandardDesktop={isStandardDesktop}
              display={display}
            />
            {display === "news" && (
              <NewsSection
                isWideScreen={isWideScreen}
                isLargeTablet={isWideScreen}
                isSmallTablet={isSmallTablet}
                isMediumMobile={isMediumMobile}
                news={news[0]}
              />
            )}

            {display === "announcements" ? (
              <>
                <Grid container spacing={3} sx={{ padding: "3%" }}>
                  <Grid
                    item
                    xs={12}
                    md={12}
                    lg={4}
                    order={{ xs: 2, sm: 2, md: 2, lg: 1, xl: 1 }}
                  >
                    <Box sx={{ width: '58vw', borderRadius: "10px" }}>
                      {getSortedAnnouncements(announcements)[0].map(
                        (announcement, index) => (
                          <CurrentAnnouncement
                            ResponsiveBox={ResponsiveBox}
                            key={index}
                            announcement={announcement}
                            setSelectedAnnouncement={setSelectedAnnouncement}
                            handleOpenModal={handleOpenModal}
                            isCurrentAnnouncement={true}
                          />


                        )
                      )}
                    </Box>
                    <PreviousAnnouncementHeading />
                    <Box sx={{ width: '58vw'}}>
                      {getSortedAnnouncements(announcements)[1].map(
                        (announcement, index) => (
                          <PreviousAnnouncement
                            ResponsiveBox={ResponsiveBox}
                            key={index}
                            announcement={announcement}
                            handleOpenModal={handleOpenModal}
                            isCurrentAnnouncement={false}
                          />
                        )
                      )}
                    </Box>
                  </Grid>
                  {/* Second Column (Width: 8 units) */}
                  <Grid
                    item
                    xs={12}
                    md={12}
                    lg={8}
                    order={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2 }}
                    sx={{
                      display: {
                        xs: "none",
                        sm: "none",
                        md: "block", // Display on large screens and above
                        lg: "block",
                        xl: "block",
                      },
                    }}
                  >
                    {/* {selectedAnnouncement && <ViewAnnouncement
                      ResponsiveBox={ResponsiveBox}
                      selectedAnnouncement={selectedAnnouncement}
                    />} */}
                  </Grid>
                </Grid>
                {/* <AnnouncementDialog
                  ResponsiveBox={ResponsiveBox}
                  open={openModal}
                  handleCloseModal={handleCloseModal}
                  title={selectedAnnouncement && selectedAnnouncement.title}
                  details={selectedAnnouncement && selectedAnnouncement.details}
                  image={selectedAnnouncement && selectedAnnouncement.image}
                /> */}
              </>

            ) : (
              news && <NewsList news={news[1]} />
            )}
          </Box>
        </Box>
      </Dashboard>
    </>
  );
};

export default withAuth(News);
