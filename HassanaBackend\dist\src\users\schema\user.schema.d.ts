import { UUID } from 'crypto';
export declare class UserSchema {
    id: UUID;
    profile: string;
    email: string;
    name: string;
    name_arabic: string;
    designation: string;
    designation_arabic: string;
    department: string;
    department_arabic: string;
    bio_link: string;
    new_joiner: string;
    status: string;
    is_cultural_ambassador: string;
    dn: string;
    gender: string;
    account_expires: string;
    user_principal_name: string;
    role: string;
    monthly_rating: number;
    yearly_rating: number;
    createdAt: Date;
    updatedAt: Date;
    activity: string;
    extesion: string;
}
