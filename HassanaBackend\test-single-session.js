const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:3001';
const GRAPHQL_URL = `${BASE_URL}/graphql`;

// Test credentials (update with your test user)
const TEST_USER = {
  username: 'testuser',
  password: 'testpassword'
};

/**
 * Test script to verify single session functionality
 */
async function testSingleSession() {
  console.log('🧪 Testing Single Session Implementation...\n');

  try {
    // Step 1: Login from "Device 1"
    console.log('📱 Step 1: Login from Device 1...');
    const login1Response = await loginUser(TEST_USER.username, TEST_USER.password, 'Device 1 - Chrome');
    const token1 = login1Response.token;
    console.log('✅ Device 1 login successful');
    console.log('🔑 Token 1:', token1.substring(0, 20) + '...\n');

    // Step 2: Test authenticated request from Device 1
    console.log('📱 Step 2: Test authenticated request from Device 1...');
    const authTest1 = await testAuthenticatedRequest(token1, 'Device 1');
    console.log('✅ Device 1 authenticated request successful\n');

    // Step 3: Login from "Device 2" (should invalidate Device 1)
    console.log('📱 Step 3: Login from Device 2 (should invalidate Device 1)...');
    const login2Response = await loginUser(TEST_USER.username, TEST_USER.password, 'Device 2 - Firefox');
    const token2 = login2Response.token;
    console.log('✅ Device 2 login successful');
    console.log('🔑 Token 2:', token2.substring(0, 20) + '...\n');

    // Step 4: Test Device 1 token (should fail)
    console.log('📱 Step 4: Test Device 1 token (should fail now)...');
    try {
      await testAuthenticatedRequest(token1, 'Device 1');
      console.log('❌ ERROR: Device 1 token should have been invalidated!');
    } catch (error) {
      console.log('✅ Device 1 token correctly invalidated:', error.response?.data?.errors?.[0]?.message || error.message);
    }

    // Step 5: Test Device 2 token (should work)
    console.log('\n📱 Step 5: Test Device 2 token (should work)...');
    const authTest2 = await testAuthenticatedRequest(token2, 'Device 2');
    console.log('✅ Device 2 authenticated request successful\n');

    // Step 6: Test logout
    console.log('📱 Step 6: Test logout from Device 2...');
    await logoutUser(login2Response.id, token2);
    console.log('✅ Logout successful');

    // Step 7: Test Device 2 token after logout (should fail)
    console.log('\n📱 Step 7: Test Device 2 token after logout (should fail)...');
    try {
      await testAuthenticatedRequest(token2, 'Device 2');
      console.log('❌ ERROR: Device 2 token should have been invalidated after logout!');
    } catch (error) {
      console.log('✅ Device 2 token correctly invalidated after logout:', error.response?.data?.errors?.[0]?.message || error.message);
    }

    console.log('\n🎉 Single Session Test Completed Successfully!');
    console.log('✅ Users can only have one active session at a time');
    console.log('✅ Previous sessions are automatically invalidated');
    console.log('✅ Logout properly invalidates sessions');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

/**
 * Login user and get JWT token
 */
async function loginUser(username, password, deviceInfo) {
  const query = `
    query LoginUser($username: String!, $password: String!) {
      loginUser(username: $username, password: $password) {
        id
        username
        role
        token
      }
    }
  `;

  const response = await axios.post(GRAPHQL_URL, {
    query,
    variables: { username, password }
  }, {
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': deviceInfo
    }
  });

  if (response.data.errors) {
    throw new Error(response.data.errors[0].message);
  }

  return response.data.data.loginUser;
}

/**
 * Test authenticated GraphQL request
 */
async function testAuthenticatedRequest(token, deviceInfo) {
  const query = `
    query GetSecuredDataForUser {
      securedDataForUser
    }
  `;

  const response = await axios.post(GRAPHQL_URL, {
    query
  }, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      'User-Agent': deviceInfo
    }
  });

  if (response.data.errors) {
    throw new Error(response.data.errors[0].message);
  }

  return response.data.data.securedDataForUser;
}

/**
 * Logout user
 */
async function logoutUser(userId, token) {
  const mutation = `
    mutation LogoutUser($userId: String!) {
      logoutUser(userId: $userId)
    }
  `;

  const response = await axios.post(GRAPHQL_URL, {
    query: mutation,
    variables: { userId }
  }, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  });

  if (response.data.errors) {
    throw new Error(response.data.errors[0].message);
  }

  return response.data.data.logoutUser;
}

// Run the test
if (require.main === module) {
  testSingleSession().catch(console.error);
}

module.exports = { testSingleSession };
