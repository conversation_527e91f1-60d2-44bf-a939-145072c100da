import client from "@/Data/ApolloClient";
import { DrawerProvider } from "@/components/Header/DrawerContext";
import { ModeProvider } from "@/components/ModeContext";
import { ApolloProvider } from "@apollo/client";
import CssBaseline from "@mui/material/CssBaseline";
import { createTheme } from "@mui/material/styles";
import { SessionProvider, useSession } from "next-auth/react";
import "../../dist/output.css";
import TicketScreenOfficeBoy from "../components/TicketScreen";
import "../styles/fonts.css";
import "../styles/globals.css";
import CircularProgress from "@mui/material/CircularProgress";
import Box from "@mui/material/Box";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css"


const theme = createTheme();

function MyApp({ Component, pageProps }) {
  return (
    <SessionProvider session={pageProps.session}>
      <ModeProvider>
        <DrawerProvider>
          <ApolloProvider client={client}>
            <CssBaseline />
            <RoleBasedComponent Component={Component} pageProps={pageProps} />
            {/* <Component {...pageProps} /> */}
          </ApolloProvider>
        </DrawerProvider>
      </ModeProvider>
    </SessionProvider>
  );
}

function RoleBasedComponent({ Component, pageProps }) {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <CircularProgress color="secondary" size={40} />
      </Box>
    );
  }
  if (
    session &&
    session.user &&
    session.user.role &&
    session.user.role != "USER" &&
    session.user.role != "ADMIN"
  ) {
    return <TicketScreenOfficeBoy />;
  }

  return <Component {...pageProps} />;
}

export default MyApp;
