"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnnouncementController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const path_1 = require("path");
const announcement_service_1 = require("./announcement.service");
const create_announcement_input_1 = require("./dto/create-announcement.input");
const update_announcement_input_1 = require("./dto/update-announcement.input");
const jwt_guard_1 = require("../auth/jwt.guard");
const swagger_1 = require("@nestjs/swagger");
let multerOptions = {
    storage: (0, multer_1.diskStorage)({
        destination: './resource/v1/announcement',
        filename: (req, image, callback) => {
            const uniqueSuffix = Date.now() + Math.round(Math.random() * 1e3);
            const ext = (0, path_1.extname)(image.originalname);
            const fileName = `${uniqueSuffix}${ext}`.toString();
            callback(null, fileName);
        }
    })
};
let AnnouncementController = class AnnouncementController {
    constructor(announcementService) {
        this.announcementService = announcementService;
    }
    async createAnnouncement(file, createAnnouncementInput) {
        try {
            let path = file?.path;
            let image = path?.replace(/resource\/v1[\/\\]/g, "");
            let data = await this.announcementService.create({ ...createAnnouncementInput, image });
            let Response = {
                code: 200,
                message: "Success",
                data: data
            };
            return Response;
        }
        catch (error) {
            console.log(error);
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError
            };
            return Response;
        }
    }
    async findAllAnnouncements(req) {
        try {
            const { id } = req['user'];
            let data = await this.announcementService.findAll(id);
            if (data) {
                let Response = {
                    code: 200,
                    message: "Success",
                    data: data
                };
                return Response;
            }
        }
        catch (error) {
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError
            };
            return Response;
        }
    }
    async createAnnouncementView(announcement_id, req) {
        try {
            const { id } = req['user'];
            await this.announcementService.createAnnouncementView(announcement_id, id);
            const data = await this.announcementService.findOne(announcement_id);
            let Response = {
                code: 200,
                message: "Success",
                data: data
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError
            };
            return Response;
        }
    }
    async findOneAnnouncement(id) {
        try {
            let data = await this.announcementService.findOne(id);
            let Response = {
                code: 200,
                message: "Success",
                data: data
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code,
                mesage: error?.message,
                error: error
            };
            return Response;
        }
    }
    async updateAnnouncement(id, updateAnnouncementInput, file) {
        try {
            let image;
            if (file) {
                let path = file?.path;
                image = path?.replace(/resource\/v1[\/\\]/g, "");
                console.log("image path", image);
            }
            let data = await this.announcementService.update(id, { ...updateAnnouncementInput, image });
            data.image = `${process.env.SERVER_URL}/${data.image}`;
            let Response = {
                code: 200,
                message: "Success",
                data: data
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code,
                message: error?.message,
                error: error?.driverError
            };
            return Response;
        }
    }
    async removeAnnouncement(id) {
        try {
            let data = await this.announcementService.remove(id);
            let Response = {
                code: 200,
                message: "Success",
                data: data
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code,
                mesage: error?.message,
                error: error
            };
            return Response;
        }
    }
};
exports.AnnouncementController = AnnouncementController;
__decorate([
    (0, swagger_1.ApiBody)({ type: create_announcement_input_1.CreateAnnouncementInput }),
    (0, common_1.Post)(),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('image', multerOptions)),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_announcement_input_1.CreateAnnouncementInput]),
    __metadata("design:returntype", Promise)
], AnnouncementController.prototype, "createAnnouncement", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Request]),
    __metadata("design:returntype", Promise)
], AnnouncementController.prototype, "findAllAnnouncements", null);
__decorate([
    (0, common_1.Post)("/view/:announcement_id"),
    __param(0, (0, common_1.Param)('announcement_id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Request]),
    __metadata("design:returntype", Promise)
], AnnouncementController.prototype, "createAnnouncementView", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AnnouncementController.prototype, "findOneAnnouncement", null);
__decorate([
    (0, swagger_1.ApiBody)({ type: update_announcement_input_1.UpdateAnnouncementInput }),
    (0, common_1.Patch)(':id'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('image', multerOptions)),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_announcement_input_1.UpdateAnnouncementInput, Object]),
    __metadata("design:returntype", Promise)
], AnnouncementController.prototype, "updateAnnouncement", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AnnouncementController.prototype, "removeAnnouncement", null);
exports.AnnouncementController = AnnouncementController = __decorate([
    (0, common_1.UseGuards)(jwt_guard_1.JwtGuard),
    (0, common_1.Controller)('v1/our-announcement'),
    __metadata("design:paramtypes", [announcement_service_1.AnnouncementService])
], AnnouncementController);
//# sourceMappingURL=announcement.controller.js.map