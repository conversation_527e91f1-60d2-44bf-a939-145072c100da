# Security Configuration Environment Variables
# Copy this file to .env and update with your actual values

# CSRF Protection
CSRF_SECRET=change-this-to-a-strong-random-string-in-production
SESSION_SECRET=change-this-to-another-strong-random-string-in-production

# CORS Configuration
# Comma-separated list of allowed origins
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,https://portal.hassana.com.sa,https://v2-portal.hassana.com.sa

# JWT Configuration (existing)
JWT_KEY=your-jwt-secret-key-here
JWT_EXPIRES_IN=24h

# Database Configuration (existing)
DB_HOST=localhost
DB_PORT=5432
DB_USER=hassana
DB_PASSWORD=postgres
DB_NAME=hassana

# Server Configuration
SERVER_PORT=3001
NODE_ENV=development

# Redis Configuration (existing)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=foobared

# LDAP Configuration (existing)
LDAP_URL=ldap://your-ldap-server
LDAP_BIND_DN=your-bind-dn
LDAP_BIND_CREDENTIALS=your-bind-password
LDAP_SEARCH_BASE=your-search-base

# Security Notes:
# 1. Generate strong random strings for CSRF_SECRET and SESSION_SECRET
# 2. Use different secrets for different environments
# 3. Keep these secrets secure and never commit them to version control
# 4. Rotate secrets regularly in production
# 5. Use environment-specific values for ALLOWED_ORIGINS

# Example commands to generate secure secrets:
# node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
# openssl rand -hex 64
