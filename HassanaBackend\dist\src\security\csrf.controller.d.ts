import { Request, Response } from 'express';
import { CsrfService } from './csrf.service';
import { SessionService } from '../auth/session.service';
export declare class CsrfController {
    private readonly csrfService;
    private readonly sessionService;
    constructor(csrfService: CsrfService, sessionService: SessionService);
    getCsrfToken(req: Request, res: Response): Response<any, Record<string, any>>;
    verifyCsrfToken(req: Request, res: Response): Response<any, Record<string, any>>;
    getSessionStatus(userId: string, res: Response): Promise<Response<any, Record<string, any>>>;
}
