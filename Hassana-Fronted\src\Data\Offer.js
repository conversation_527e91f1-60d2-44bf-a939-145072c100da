import { gql } from "@apollo/client";

export const CREATE_OFFER = gql`
  mutation CreateOffer($createOffersInput: CreateOffersInput!) {
    createOffer(createOffersInput: $createOffersInput) {
      id
      name
      contact_information
      code
      expiry_date
      description
      status
      is_read
      createdAt
      created_by
      updatedAt
      updated_by
    }
  }
`;


export const GET_OFFERS = gql`
  query Offers($user_id: String!) {
    offers(user_id: $user_id) {
      id
      name
      contact_information
      code
      expiry_date
      description
      status
      createdAt
      created_by
      updatedAt
      updated_by
      is_read
    }
  }
`;

export const GET_VALID_OFFERS = gql`
  query ValidOffers($user_id: String!) {
    validOffers(user_id: $user_id) {
      id
      name
      contact_information
      code
      expiry_date
      description
      status
      createdAt
      created_by
      updatedAt
      updated_by
      is_read
    }
  }
`;


export const UPDATE_OFFER = gql`
  mutation UpdateOffer($id: ID!,$updateOffersInput: UpdateOffersInput!) {
    updateOffer(id: $id, updateOffersInput: $updateOffersInput) {
      id
      name
      contact_information
      code
      expiry_date
      description
      status
      createdAt
      created_by
      updatedAt
      updated_by
      is_read
    }
  }
`;


export const DELETE_OFFER = gql`
  mutation RemoveOffer($id: ID!) {
    removeOffer(id: $id) {
      id
    }
  }
`;


export const TOGGLE_OFFER_STATUS = gql`
  mutation ToggleOfferStatus($id: ID!) {
    toggleOfferStatus(id: $id) {
      id
      status
    }
  }
`;


export const OFFER_VIEW = gql`
  mutation OfferView($offer_id: String!, $user_id: String!) {
    offerView(offer_id: $offer_id, user_id: $user_id) {
      id
      is_read
    }
  }
`;