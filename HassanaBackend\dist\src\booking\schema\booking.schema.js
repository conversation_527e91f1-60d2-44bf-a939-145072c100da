"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingSchema = void 0;
const graphql_1 = require("@nestjs/graphql");
let BookingSchema = class BookingSchema {
};
exports.BookingSchema = BookingSchema;
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    __metadata("design:type", String)
], BookingSchema.prototype, "id", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], BookingSchema.prototype, "title", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], BookingSchema.prototype, "details", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], BookingSchema.prototype, "uid", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], BookingSchema.prototype, "registrationDoc", void 0);
__decorate([
    (0, graphql_1.Field)(() => String, { nullable: true }),
    __metadata("design:type", String)
], BookingSchema.prototype, "location", void 0);
__decorate([
    (0, graphql_1.Field)(() => graphql_1.ID),
    __metadata("design:type", String)
], BookingSchema.prototype, "userId", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], BookingSchema.prototype, "start", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], BookingSchema.prototype, "end", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], BookingSchema.prototype, "teaBoy", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], BookingSchema.prototype, "parking", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], BookingSchema.prototype, "itTechnician", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], BookingSchema.prototype, "createdAt", void 0);
__decorate([
    (0, graphql_1.Field)(),
    __metadata("design:type", Date)
], BookingSchema.prototype, "updatedAt", void 0);
exports.BookingSchema = BookingSchema = __decorate([
    (0, graphql_1.ObjectType)()
], BookingSchema);
//# sourceMappingURL=booking.schema.js.map