import { QuoteService } from './quote.service';
import { CreateQuoteInput } from './dto/create-quote.input';
import { UpdateQuoteInput } from './dto/update-quote.input';
import { UUID } from 'crypto';
export declare class QuoteResolver {
    private readonly quoteService;
    constructor(quoteService: QuoteService);
    createQuote(createQuoteInput: CreateQuoteInput): Promise<any>;
    findAllQuote(): Promise<import("./entities/quote.entity").QuoteEntity[]>;
    findOneQuote(id: number): Promise<string>;
    todaysQuote(): Promise<import("./entities/quote.entity").QuoteEntity>;
    updateQuote(id: UUID, updateQuoteInput: UpdateQuoteInput): Promise<UpdateQuoteInput & import("./entities/quote.entity").QuoteEntity>;
    removeQuote(id: UUID): Promise<import("./entities/quote.entity").QuoteEntity>;
}
