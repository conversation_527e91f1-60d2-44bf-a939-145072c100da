import { Box, Grid, IconButton, Typography, useTheme } from "@mui/material";
import SouthEast from "@mui/icons-material/SouthEast";
import Image from "next/image";
import { getSelectedColor, useSelectedColor } from "./HelperFunctions";
import { useColor } from "./ColorContext";
import Link from "next/link";
import { useSession } from "next-auth/react";

const data = [
  {
    name: "Oracle Fusion",
    src: "/launchpadIcons/icon1.png",
    link: "https://login-exsq-saasfaprod1.fa.ocs.oraclecloud.com/oam/server/obrareq.cgi?encquery%3DFlIdsfWf9%2BWcLK%2Bujlmbg1OVST4JeXBsmH%2B32%2FTHCS5Rkg3DEu8Rc5voNP7vjISEMvmVp2tRw4iQ1s0ptn%2Bh3HAp2alCnuPfEWtPAE2SId%2FkkvcYkrVMOrctqqNb%2BZKbTM5MUksiwhy3Cui4HSxUtPnElFB4sthJ77kFttWfygR0Zk7YQ8ynAjLiX69h9TPYeM%2B%2BzDNmVhx1AF0VtgCX6r2YF3wVHboFjUKLNwB7lL4iBk5E1RGNf%2BLlTnJDbRTAY95aMM6hK8c%2FvzST7UtFP1LCEIRNq5qViC7BX9Sl9WjcDQ5FOfS0Symj7xi4voGMfPV915OSbandQQ37lK7RNszqJeds9m%2FrDkaPNVMr8F8i%2Fho%2F0nOfMxIlmBbylPExoTcaviEsp1l4AKjzf09HLtrO%2Frgu0DCni7SuIVqVufMs6DpBnoPN%2B5XlCDgZC8xVc8QUapPXJZCFA%2BolfcNCyLe9QG0FTnZsCUlBYay%2FJzPbKz1ElWT%2FpMNpa6LHc2FKQL4yYDCa6eLjgmz9MEEEadHpx1nqL%2FzbITx41gdlLOghFGnJ7fUMmTQomy0V4baNV6o5Sy0DbeDhJoiTlUgLst6cxaIFccRwBW%2Bqg2ETjrnAzZTUcaCOCHj3BkYz8KgeL9rOhj%2BvlP1s2DCdSHo7NOMJjaJdm%2B8nPnpLg0qZJbJW6T8eVLQeZbiMci99RdvT74ILt0A%2Bch9%2BF6Qgj8q7cCAeeMyo8I8NhXhTTxxvfn9hl%2BBfM097KracKKpUKvgDwnDPVHYqrvY8q7egwuzmDe9LooeQmNIxiunOTee%2BFUqTjD%2FMC%2FOrifN3yJ82ZYyGtzZr2yRYgv02C7MLlZ9awWUbhWjAYuITrtrpxGQySixgHY9zVq5lNWq8fJVB2z2Vv7MgGnC2%2BtMkW3gHvkg7y%2BVAk9tikfgRzwPKpBwxcoGvBiqynsRgK9mwHlqJeYD3SRub1%2BwqlNh7%2BYjuklBrGnqqEG42hpEN2voP7S3SfMMgaPO8PH%2Fl0BWZF4wqoRG4BGJ6SDlE14zLFXE%2BqHN5BRApO6ihzMipMla46NvSYrldHm5OhCTqX%2FKirSUoo36OHH%2F5T%2FVZlizOrxVGDfbM6gFMZHozICMfeNZ%2BgdYSIoESitElbSxTUb7kCoYEYXbOMBbBbJm%2F84wKEiIQHkUJfm1bok8YyEyto%2BonIKj2BPxBKdo3e5BwPNbG7s8lDicspT7JA8LnAN%2Bkv1Fck%2FxGzr7gbkET8tnEo0SFzRzH57yi%2BKZ8CjHkwne8Eoo0o1Wu2IKuZ4SCcN7JX6rV3jOVzVYAg7eysLgfPaJEV9KZ3IzBlZthqhHvNrOGOaT3iXzIUIENz5sU3mSW0u%2F0o%2B5GVsaMhmWOI5n9XmX74hWO3krffWhE5jRmQm4mBwljPTjeTqhwHvhPKy4w0aoheU7vh5DAwJkNjR%2FADN4kVGVYSu47FLw2dFrUqWKNNKtMUn%2FXF6pDwaavShbFKz8dsFRdwthEqYMYaJKt4%2FY37FnPWZXIlpsKHvZnqz%2B9sLpKYtc4mgzjYJeQMSG4MitZWiDofgV%2FJxiYNRXaIkEwQOPc9eLoEmf631VGE0B7EsaQRk1uYFs%2B8ZP%2BQqj1gbUWLVUT4EsAy7ZwICtGgSkGw7qbHAYCnk0KhVClK5T%2BHjT7FvUqG9tA%3D%3D%20agentid%3DOraFusionApp_11AG%20ver%3D1%20crmethod%3D2%26cksum%3Ddacd26bdd0f1fd62c3008ba45457134605911e15&ECID-Context=1.0065nT%5Emnn53v1K6yVjc6G005QMW0002W2%3BkXjE",
  },
  {
    name: "Support Portal",
    src: "/launchpadIcons/icon2.png",
    link: "https://support.hassana.com.sa",
  },
  {
    name: "Business Trips",
    src: "/launchpadIcons/icon3.png",
    link: "https://flyakeed.com/",
  },
  {
    name: "Microsoft Copilot",
    src: "/launchpadIcons/icon1.png",
    link: "https://copilot.microsoft.com/",
  },
  {
    name: "One Drive",
    src: "/launchpadIcons/icon2.png",
    link: "https://hassanainvestmentcompany-my.sharepoint.com/",
  },
  {
    name: "Adobe ",
    src: "/launchpadIcons/icon4.png",
    link: "https://hassana.na1.echosign.com/public/login",
  },
  {
    name: "Hassana offers",
    src: "/launchpadIcons/icon3.png",
    link: "/HassanaOffers", // Adjusted to absolute path for clarity
  },
  {
    name: "Employee Wellbeing",
    src: "/launchpadIcons/icon1.png",
    link: "https://www.tawuniya.com/ar/",
  },
];
const data2 = [
  {
    name: "Oracle Fusion",
    src: "/launchpadIcons/light/icon1.png",
    link: "https://login-exsq-saasfaprod1.fa.ocs.oraclecloud.com/oam/server/obrareq.cgi?encquery%3DFlIdsfWf9%2BWcLK%2Bujlmbg1OVST4JeXBsmH%2B32%2FTHCS5Rkg3DEu8Rc5voNP7vjISEMvmVp2tRw4iQ1s0ptn%2Bh3HAp2alCnuPfEWtPAE2SId%2FkkvcYkrVMOrctqqNb%2BZKbTM5MUksiwhy3Cui4HSxUtPnElFB4sthJ77kFttWfygR0Zk7YQ8ynAjLiX69h9TPYeM%2B%2BzDNmVhx1AF0VtgCX6r2YF3wVHboFjUKLNwB7lL4iBk5E1RGNf%2BLlTnJDbRTAY95aMM6hK8c%2FvzST7UtFP1LCEIRNq5qViC7BX9Sl9WjcDQ5FOfS0Symj7xi4voGMfPV915OSbandQQ37lK7RNszqJeds9m%2FrDkaPNVMr8F8i%2Fho%2F0nOfMxIlmBbylPExoTcaviEsp1l4AKjzf09HLtrO%2Frgu0DCni7SuIVqVufMs6DpBnoPN%2B5XlCDgZC8xVc8QUapPXJZCFA%2BolfcNCyLe9QG0FTnZsCUlBYay%2FJzPbKz1ElWT%2FpMNpa6LHc2FKQL4yYDCa6eLjgmz9MEEEadHpx1nqL%2FzbITx41gdlLOghFGnJ7fUMmTQomy0V4baNV6o5Sy0DbeDhJoiTlUgLst6cxaIFccRwBW%2Bqg2ETjrnAzZTUcaCOCHj3BkYz8KgeL9rOhj%2BvlP1s2DCdSHo7NOMJjaJdm%2B8nPnpLg0qZJbJW6T8eVLQeZbiMci99RdvT74ILt0A%2Bch9%2BF6Qgj8q7cCAeeMyo8I8NhXhTTxxvfn9hl%2BBfM097KracKKpUKvgDwnDPVHYqrvY8q7egwuzmDe9LooeQmNIxiunOTee%2BFUqTjD%2FMC%2FOrifN3yJ82ZYyGtzZr2yRYgv02C7MLlZ9awWUbhWjAYuITrtrpxGQySixgHY9zVq5lNWq8fJVB2z2Vv7MgGnC2%2BtMkW3gHvkg7y%2BVAk9tikfgRzwPKpBwxcoGvBiqynsRgK9mwHlqJeYD3SRub1%2BwqlNh7%2BYjuklBrGnqqEG42hpEN2voP7S3SfMMgaPO8PH%2Fl0BWZF4wqoRG4BGJ6SDlE14zLFXE%2BqHN5BRApO6ihzMipMla46NvSYrldHm5OhCTqX%2FKirSUoo36OHH%2F5T%2FVZlizOrxVGDfbM6gFMZHozICMfeNZ%2BgdYSIoESitElbSxTUb7kCoYEYXbOMBbBbJm%2F84wKEiIQHkUJfm1bok8YyEyto%2BonIKj2BPxBKdo3e5BwPNbG7s8lDicspT7JA8LnAN%2Bkv1Fck%2FxGzr7gbkET8tnEo0SFzRzH57yi%2BKZ8CjHkwne8Eoo0o1Wu2IKuZ4SCcN7JX6rV3jOVzVYAg7eysLgfPaJEV9KZ3IzBlZthqhHvNrOGOaT3iXzIUIENz5sU3mSW0u%2F0o%2B5GVsaMhmWOI5n9XmX74hWO3krffWhE5jRmQm4mBwljPTjeTqhwHvhPKy4w0aoheU7vh5DAwJkNjR%2FADN4kVGVYSu47FLw2dFrUqWKNNKtMUn%2FXF6pDwaavShbFKz8dsFRdwthEqYMYaJKt4%2FY37FnPWZXIlpsKHvZnqz%2B9sLpKYtc4mgzjYJeQMSG4MitZWiDofgV%2FJxiYNRXaIkEwQOPc9eLoEmf631VGE0B7EsaQRk1uYFs%2B8ZP%2BQqj1gbUWLVUT4EsAy7ZwICtGgSkGw7qbHAYCnk0KhVClK5T%2BHjT7FvUqG9tA%3D%3D%20agentid%3DOraFusionApp_11AG%20ver%3D1%20crmethod%3D2%26cksum%3Ddacd26bdd0f1fd62c3008ba45457134605911e15&ECID-Context=1.0065nT%5Emnn53v1K6yVjc6G005QMW0002W2%3BkXjE",
  },
  {
    name: "Support Portal",
    src: "/launchpadIcons/light/icon2.png",
    link: "https://support.hassana.com.sa",
  },
  {
    name: "Business Trips",
    src: "/launchpadIcons/light/icon3.png",
    link: "https://flyakeed.com/",
  },
  {
    name: "Microsoft Copilot",
    src: "/launchpadIcons/light/icon1.png",
    link: "https://copilot.microsoft.com/",
  },
  {
    name: "One Drive",
    src: "/launchpadIcons/light/icon2.png",
    link: "https://hassanainvestmentcompany-my.sharepoint.com/",
  },
  {
    name: "Adobe",
    src: "/launchpadIcons/light/icon4.png",
    link: "https://hassana.na1.echosign.com/public/login",
  },
  {
    name: "Hassana offers",
    src: "/launchpadIcons/light/icon3.png",
    link: "/HassanaOffers",
  },
  {
    name: "Employee Wellbeing",
    src: "/launchpadIcons/light/icon1.png",
    link: "https://www.tawuniya.com/ar/",
  },
];

const LaunchPad = () => {
  const theme = useTheme();
  const { color } = useColor();
  const selectedColor = useSelectedColor(color);
  const { data: session } = useSession();

  // Helper function to check if the link is external
  const isExternalLink = (link) => {
    return link.startsWith("http") || link.startsWith("https");
  };

  // Helper function to get the dynamic link for "Hassana offers"
  const getHassanaOfferLink = (item) => {
    if (item.name === "Hassana offers") {
      return session?.user?.role === "ADMIN" ? "/HassanaOffers" : "/HassanaOffers";
    }
    return item.link; // Return originallink for other items
  };

  return (
    <>
      <Typography
        variant="Heading2"
        sx={{
          fontFamily: "Urbanist",
          color: theme.palette.text.primary,
          fontSize: "1.3rem",
          fontWeight: "600",
          marginBottom: "50px",
        }}
      >
        Launch Pad
      </Typography>
      <Grid container spacing={2}>
        {(color !== "white" ? data2 : data).map((item, index) => {
          return (
            <Grid item xs={6} key={index}>
              <Link
                href={getHassanaOfferLink(item)} // Pass the entire item object
                target={isExternalLink(item.link) ? "_blank" : "_self"}
                style={{ textDecoration: "none", color: "inherit" }}
              >
                <Box
                  sx={{
                    margin: "1px",
                    display: "flex",
                    flexDirection: "column",
                    marginTop: "10px",
                    height: "115px",
                    borderRadius: "10px",
                    background: selectedColor,
                    color:
                      selectedColor === theme.palette.background.primary
                        ? theme.palette.text.primary
                        : theme.palette.text.white,
                  }}
                >
                  <Image
                    src={item.src}
                    alt={item.name}
                    width={25}
                    height={25}
                    style={{
                      objectFit: "contain",
                      marginLeft: "8px",
                      margin: "6px",
                    }}
                  />
                  <Typography
                    variant="body1"
                    sx={{
                      fontSize: "1rem",
                      fontWeight: "600",
                      fontFamily: "Helvetica",
                      marginLeft: "10px",
                    }}
                  >
                    {item.name}
                  </Typography>
                  <Box
                    sx={{
                      flexGrow: 1,
                      display: "flex",
                      justifyContent: "flex-end",
                      alignItems: "flex-end",
                    }}
                  >
                    <SouthEast
                      sx={{
                        width: "20px",
                        height: "20px",
                        marginTop: "10px",
                        marginRight: "5px",
                      }}
                    />
                  </Box>
                </Box>
              </Link>
            </Grid>
          );
        })}
      </Grid>
    </>
  );
};

export default LaunchPad;