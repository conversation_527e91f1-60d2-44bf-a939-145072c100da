import authclient from "@/Data/AuthClientServer";
import { LOGIN_USER } from "@/Data/Auth";
import { client } from "@/Data/ApolloClient";

import NextAuth from "next-auth";
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials";
import Client from "@/Data/ApolloClientLocal";

export default NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Username", type: "text", placeholder: "jsmith" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        const { username, password } = credentials;
        console.log("🔐 Login attempt received:", { username });

        try {
          console.log("📡 Sending GraphQL query to backend...");
          const { data, error } = await authclient.query({
            query: LOGIN_USER,
            variables: { username, password },
          });
          
          console.log("📊 GraphQL Response:", { data, error });
          
          if (error) {
            console.error("❌ GraphQL Error:", error);
            console.error("Error details:", error.graphQLErrors, error.networkError);
            return null;
          }

          const { loginUser } = data;
          console.log("👤 Login User Data:", loginUser);

          if (loginUser) {
            console.log("✅ Login successful:", loginUser);
            return {
              ...loginUser,
              accessToken: loginUser.token || "test",
            };
          } else {
            console.log("❌ No user found or invalid credentials");
            return null;
          }
        } catch (error) {
          console.error("🔥 Authorization Error:", error);
          console.error("Error message:", error.message);
          console.error("Error stack:", error.stack);
          if (error.networkError) {
            console.error("Network error details:", error.networkError);
          }
          return null;
        }
      },
    }),
  ],
  session: {
    jwt: false, // You're using session-based auth, not JWT
  },
  jwt: {
    secret: "test",
    encryption: true,
  },
  callbacks: {
    async jwt({ token, user }) {
      console.log('JWT----->',user);
      if (user) {
        token.id = user.id;
        token.role = user.role.toUpperCase();
        token.name = user.username;
        token.accessToken = user.accessToken; 
      }
      return token;
    },
    async session({ session, token }) {
      session.user.id = token.id;
      session.user.role = token.role;
      session.user.username = token.name;
      session.accessToken = token.accessToken;
      console.log('JSESSIOWT----->',session); 
      return session;
    },
  },
});