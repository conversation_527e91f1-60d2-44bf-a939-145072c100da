import { getSession } from "next-auth/react";

export const withAuthServerSideProps = (getServerSidePropsFunc) => {
  return async (context) => {
    const session = await getSession(context);

    if (!session) {
      return {
        redirect: {
          destination: "/login?login=false",
          permanent: false,
        },
      };
    }

    if (getServerSidePropsFunc) {
      return await getServerSidePropsFunc(context, session);
    }

    return { props: { session } };
  };
};
