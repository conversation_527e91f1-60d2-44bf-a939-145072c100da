export declare class SecurityConfig {
    static getCorsConfig(): {
        origin: (origin: string, callback: (err: Error | null, allow?: boolean) => void) => void;
        methods: string[];
        allowedHeaders: string[];
        credentials: boolean;
        preflightContinue: boolean;
        optionsSuccessStatus: number;
    };
    static getHelmetConfig(): {
        contentSecurityPolicy: {
            directives: {
                defaultSrc: string[];
                styleSrc: string[];
                scriptSrc: string[];
                imgSrc: string[];
                connectSrc: string[];
                fontSrc: string[];
                objectSrc: string[];
                mediaSrc: string[];
                frameSrc: string[];
            };
        };
        crossOriginEmbedderPolicy: boolean;
        hsts: {
            maxAge: number;
            includeSubDomains: boolean;
            preload: boolean;
        };
        referrerPolicy: {
            policy: string;
        };
    };
    static getRateLimitConfig(): {
        ttl: number;
        limit: number;
        skipIf: (context: any) => boolean;
    };
    static getGraphQLSecurityConfig(): {
        introspection: boolean;
        playground: boolean;
        validationRules: any[];
        context: ({ req, res }: {
            req: any;
            res: any;
        }) => {
            req: any;
            res: any;
            csrfToken: any;
            sessionToken: any;
        };
    };
    static getSessionConfig(): {
        secret: string;
        resave: boolean;
        saveUninitialized: boolean;
        cookie: {
            secure: boolean;
            httpOnly: boolean;
            maxAge: number;
            sameSite: "strict";
        };
    };
    static getValidationConfig(): {
        whitelist: boolean;
        forbidNonWhitelisted: boolean;
        transform: boolean;
        forbidUnknownValues: boolean;
        transformOptions: {
            enableImplicitConversion: boolean;
        };
        exceptionFactory: (errors: any) => Error;
    };
}
