import { CategoryService } from './category.service';
import { CreateCategoryInput } from './dto/create-category.input';
import { UpdateCategoryInput } from './dto/update-category.input';
import { Category } from './entities/category.entity';
import { UUID } from 'crypto';
export declare class CategoryResolver {
    private readonly categoryService;
    constructor(categoryService: CategoryService);
    createCategory(createCategoryInput: CreateCategoryInput): Promise<Category>;
    findAll(): Promise<Category[]>;
    findOne(id: UUID): Promise<Category>;
    updateCategory(updateCategoryInput: UpdateCategoryInput): Promise<Category>;
    removeCategory(id: UUID): Promise<Category>;
}
