import { getSession } from 'next-auth/react';

/**
 * Creates GraphQL query/mutation options with authentication headers
 * @param {Object} session - NextAuth session object
 * @param {Object} additionalOptions - Additional Apollo options
 * @returns {Object} Apollo query/mutation options with auth headers
 */
export const createAuthOptions = (session, additionalOptions = {}) => {
  const token = session?.accessToken || (typeof window !== 'undefined' ? localStorage.getItem("jwtToken") : null);
  
  return {
    ...additionalOptions,
    context: {
      ...additionalOptions.context,
      headers: {
        ...additionalOptions.context?.headers,
        ...(token && { authorization: `Bearer ${token}` }),
      },
    },
    errorPolicy: 'all', // This allows partial data and errors to be returned
  };
};

/**
 * Creates error handler for GraphQL operations
 * @param {Function} onAuthError - Callback for authentication errors
 * @param {Function} onForbiddenError - Callback for authorization errors
 * @param {Function} onOtherError - Callback for other errors
 * @returns {Function} Error handler function
 */
export const createGraphQLErrorHandler = (onAuthError, onForbiddenError, onOtherError) => {
  return (error) => {
    console.error("GraphQL Error:", error);

    // Handle GraphQL errors
    if (error.graphQLErrors && error.graphQLErrors.length > 0) {
      error.graphQLErrors.forEach((gqlError) => {
        const { message, extensions } = gqlError;
        
        // Authentication errors
        if (
          extensions?.code === 'UNAUTHENTICATED' ||
          message.includes('Unauthorized') ||
          message.includes('Authorization header not found') ||
          message.includes('Token has expired') ||
          message.includes('Invalid token')
        ) {
          if (onAuthError) {
            onAuthError(gqlError);
          } else {
            console.warn('Authentication error:', message);
            // Clear invalid token
            if (typeof window !== 'undefined') {
              localStorage.removeItem("jwtToken");
              window.location.href = '/login?login=false&reason=auth_error';
            }
          }
        }
        // Authorization errors
        else if (
          extensions?.code === 'FORBIDDEN' ||
          message.includes('Access denied') ||
          message.includes('Forbidden') ||
          message.includes('Required role')
        ) {
          if (onForbiddenError) {
            onForbiddenError(gqlError);
          } else {
            console.warn('Authorization error:', message);
          }
        }
        // Other GraphQL errors
        else {
          if (onOtherError) {
            onOtherError(gqlError);
          } else {
            console.error('GraphQL error:', message);
          }
        }
      });
    }

    // Handle network errors
    if (error.networkError) {
      const { statusCode } = error.networkError;
      
      if (statusCode === 401) {
        if (onAuthError) {
          onAuthError(error.networkError);
        } else {
          console.warn('Network authentication error');
          if (typeof window !== 'undefined') {
            localStorage.removeItem("jwtToken");
            window.location.href = '/login?login=false&reason=network_auth';
          }
        }
      } else if (statusCode === 403) {
        if (onForbiddenError) {
          onForbiddenError(error.networkError);
        } else {
          console.warn('Network authorization error');
        }
      } else {
        if (onOtherError) {
          onOtherError(error.networkError);
        } else {
          console.error('Network error:', error.networkError);
        }
      }
    }
  };
};

/**
 * Utility function to check if an error is an authentication error
 * @param {Object} error - Apollo error object
 * @returns {boolean} Whether the error is an authentication error
 */
export const isAuthError = (error) => {
  if (!error) return false;

  // Check GraphQL errors
  if (error.graphQLErrors && error.graphQLErrors.length > 0) {
    return error.graphQLErrors.some((gqlError) => {
      const { message, extensions } = gqlError;
      return (
        extensions?.code === 'UNAUTHENTICATED' ||
        message.includes('Unauthorized') ||
        message.includes('Authorization header not found') ||
        message.includes('Token has expired') ||
        message.includes('Invalid token')
      );
    });
  }

  // Check network errors
  if (error.networkError && error.networkError.statusCode === 401) {
    return true;
  }

  return false;
};

/**
 * Utility function to check if an error is an authorization error
 * @param {Object} error - Apollo error object
 * @returns {boolean} Whether the error is an authorization error
 */
export const isForbiddenError = (error) => {
  if (!error) return false;

  // Check GraphQL errors
  if (error.graphQLErrors && error.graphQLErrors.length > 0) {
    return error.graphQLErrors.some((gqlError) => {
      const { message, extensions } = gqlError;
      return (
        extensions?.code === 'FORBIDDEN' ||
        message.includes('Access denied') ||
        message.includes('Forbidden') ||
        message.includes('Required role')
      );
    });
  }

  // Check network errors
  if (error.networkError && error.networkError.statusCode === 403) {
    return true;
  }

  return false;
};

/**
 * Extracts user-friendly error message from GraphQL error
 * @param {Object} error - Apollo error object
 * @returns {string} User-friendly error message
 */
export const getErrorMessage = (error) => {
  if (!error) return 'An unknown error occurred';

  // Check for GraphQL errors first
  if (error.graphQLErrors && error.graphQLErrors.length > 0) {
    const gqlError = error.graphQLErrors[0];
    
    // Return custom messages for common errors
    if (isAuthError(error)) {
      return 'Authentication required. Please log in again.';
    }
    
    if (isForbiddenError(error)) {
      return 'Access denied. You do not have permission to perform this action.';
    }
    
    return gqlError.message || 'A GraphQL error occurred';
  }

  // Check for network errors
  if (error.networkError) {
    if (error.networkError.statusCode === 401) {
      return 'Authentication required. Please log in again.';
    }
    if (error.networkError.statusCode === 403) {
      return 'Access denied. You do not have permission to perform this action.';
    }
    return error.networkError.message || 'A network error occurred';
  }

  return error.message || 'An error occurred';
};
