// DataTable.js
import React, { useState, useEffect } from 'react';
import { Button, Modal, TextField, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@mui/material';

const DataTable = () => {
  // State for managing data in the table
  const [data, setData] = useState([]);
  
  // State for managing the modal
  const [modalOpen, setModalOpen] = useState(false);
  
  // State for managing form inputs
  const [formData, setFormData] = useState({ name: '', age: '' });

  // Function to open the modal for adding/updating data
  const openModal = () => {
    setModalOpen(true);
  };

  // Function to close the modal
  const closeModal = () => {
    setModalOpen(false);
  };

  // Function to handle form input changes
  const handleInputChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  // Function to add/update data
  const handleSave = () => {
    setData([...data, formData]);
    closeModal();
  };

  // Function to delete data
  const handleDelete = (index) => {
    // Implement your logic to delete data
    // For simplicity, just remove the data from the state
    const newData = [...data];
    newData.splice(index, 1);
    setData(newData);
  };

  useEffect(() => {
    // You can fetch initial data here or populate it as needed
    // For simplicity, let's initialize with some sample data
    setData([
      { name: 'John Doe', age: 25 },
      { name: 'Jane Doe', age: 30 },
    ]);
  }, []);

  return (
    <div>
      <Button variant="contained" color="primary" onClick={openModal}>
        Add Data
      </Button>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Age</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data.map((row, index) => (
              <TableRow key={index}>
                <TableCell>{row.name}</TableCell>
                <TableCell>{row.age}</TableCell>
                <TableCell>
                  <Button variant="contained" color="secondary" onClick={() => handleDelete(index)}>
                    Delete
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Modal for adding/updating data */}
      <Modal open={modalOpen} onClose={closeModal}>
        <div>
          <TextField label="Name" name="name" value={formData.name} onChange={handleInputChange} />
          <TextField label="Age" name="age" value={formData.age} onChange={handleInputChange} />
          <Button variant="contained" color="primary" onClick={handleSave}>
            Save
          </Button>
          <Button variant="contained" color="secondary" onClick={closeModal}>
            Cancel
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default DataTable;
