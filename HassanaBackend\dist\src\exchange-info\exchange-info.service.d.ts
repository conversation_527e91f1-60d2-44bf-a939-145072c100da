import { UpdateExchangeInfoDto } from './dto/update-exchange-info.dto';
export declare class ExchangeInfoService {
    findAll(): string;
    findOne(id: string): Promise<any>;
    update(id: number, updateExchangeInfoDto: UpdateExchangeInfoDto): string;
    remove(id: number): string;
    private url;
    fetchDataForUser(username: string, password: string): Promise<any>;
    private createRequestBody;
    private parseXMLResponse;
}
