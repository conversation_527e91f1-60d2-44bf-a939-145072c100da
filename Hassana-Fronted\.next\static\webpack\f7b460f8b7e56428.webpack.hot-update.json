{"c": ["webpack"], "r": ["pages/HassanaOffers"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=F%3A%5CProjects%5CHBack%5CHassana-Fronted%5Csrc%5Cpages%5CHassanaOffers.js&page=%2FHassanaOffers!", "./src/components/SideCard.jsx", "./src/components/WalaOfferCard.jsx", "./src/hooks/useGraphQLAuth.js", "./src/pages/HassanaOffers.js", "__barrel_optimize__?names=<PERSON><PERSON>,<PERSON>,<PERSON>,Card<PERSON>ontent,Dialog,DialogContent,DialogTitle,Grid,Typography!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=Box,Link,Typography!=!./node_modules/@mui/material/index.js"]}