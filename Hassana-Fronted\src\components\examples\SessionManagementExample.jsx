import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Alert,
  Chip,
  Stack,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import { 
  AccountCircle, 
  ExitToApp, 
  Warning, 
  CheckCircle,
  Schedule,
  Computer
} from '@mui/icons-material';
import { useSessionManagement, useSessionConflictDetection } from '@/hooks/useSessionManagement';
import SessionConflictDialog from '@/components/auth/SessionConflictDialog';

/**
 * Example component demonstrating session management features
 */
const SessionManagementExample = () => {
  const [showConflictDialog, setShowConflictDialog] = useState(false);
  const [message, setMessage] = useState('');

  const {
    sessionInfo,
    sessionConflict,
    isAuthenticated,
    isLoading,
    logout,
    checkSessionValidity,
    resolveSessionConflict
  } = useSessionManagement({
    enableMonitoring: true,
    onSessionConflict: () => {
      setShowConflictDialog(true);
      setMessage('Session conflict detected! You have been logged in from another device.');
    }
  });

  const { hasConflict, conflictMessage, dismissConflict } = useSessionConflictDetection();

  const handleLogout = async () => {
    try {
      await logout();
      setMessage('Logged out successfully');
    } catch (error) {
      setMessage('Logout failed: ' + error.message);
    }
  };

  const handleCheckSession = () => {
    const isValid = checkSessionValidity();
    setMessage(isValid ? 'Session is valid' : 'Session is invalid');
  };

  const handleForceLogin = () => {
    setShowConflictDialog(false);
    // In a real app, this would trigger a new login that invalidates other sessions
    window.location.reload();
  };

  const handleGoToLogin = () => {
    setShowConflictDialog(false);
    window.location.href = '/login';
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" p={3}>
        <Typography>Loading session information...</Typography>
      </Box>
    );
  }

  if (!isAuthenticated) {
    return (
      <Card>
        <CardContent>
          <Alert severity="info">
            Please log in to view session management features.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Session Management Example
      </Typography>

      {message && (
        <Alert 
          severity={message.includes('failed') || message.includes('invalid') ? 'error' : 'success'} 
          sx={{ mb: 2 }}
          onClose={() => setMessage('')}
        >
          {message}
        </Alert>
      )}

      {sessionConflict && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          Session conflict detected! You may have been logged in from another device.
          <Button 
            size="small" 
            onClick={resolveSessionConflict}
            sx={{ ml: 1 }}
          >
            Resolve
          </Button>
        </Alert>
      )}

      {hasConflict && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {conflictMessage}
          <Button 
            size="small" 
            onClick={dismissConflict}
            sx={{ ml: 1 }}
          >
            Dismiss
          </Button>
        </Alert>
      )}

      {/* Current Session Info */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            <AccountCircle sx={{ mr: 1, verticalAlign: 'middle' }} />
            Current Session Information
          </Typography>
          
          {sessionInfo ? (
            <List dense>
              <ListItem>
                <ListItemIcon><AccountCircle /></ListItemIcon>
                <ListItemText 
                  primary="User ID" 
                  secondary={sessionInfo.userId} 
                />
              </ListItem>
              
              <ListItem>
                <ListItemIcon><CheckCircle /></ListItemIcon>
                <ListItemText 
                  primary="Session ID" 
                  secondary={sessionInfo.sessionId} 
                />
              </ListItem>
              
              <ListItem>
                <ListItemIcon><Schedule /></ListItemIcon>
                <ListItemText 
                  primary="Login Time" 
                  secondary={sessionInfo.loginTime ? new Date(sessionInfo.loginTime).toLocaleString() : 'Unknown'} 
                />
              </ListItem>
              
              <ListItem>
                <ListItemIcon><Computer /></ListItemIcon>
                <ListItemText 
                  primary="Device Info" 
                  secondary={sessionInfo.deviceInfo?.userAgent || 'Unknown'} 
                />
              </ListItem>
              
              <ListItem>
                <ListItemText 
                  primary="Role" 
                  secondary={
                    <Chip 
                      label={sessionInfo.role} 
                      size="small" 
                      color={sessionInfo.role === 'ADMIN' ? 'primary' : 'secondary'}
                    />
                  } 
                />
              </ListItem>
            </List>
          ) : (
            <Alert severity="info">
              No session information available
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Session Actions */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Session Actions
          </Typography>
          
          <Stack direction="row" spacing={2} flexWrap="wrap">
            <Button
              variant="outlined"
              onClick={handleCheckSession}
              startIcon={<CheckCircle />}
            >
              Check Session Validity
            </Button>
            
            <Button
              variant="outlined"
              color="warning"
              onClick={() => setShowConflictDialog(true)}
              startIcon={<Warning />}
            >
              Simulate Session Conflict
            </Button>
            
            <Button
              variant="contained"
              color="error"
              onClick={handleLogout}
              startIcon={<ExitToApp />}
            >
              Logout
            </Button>
          </Stack>
        </CardContent>
      </Card>

      {/* Session Security Info */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Session Security Features
          </Typography>
          
          <List dense>
            <ListItem>
              <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
              <ListItemText 
                primary="Single Session Per User" 
                secondary="Only one active session allowed per user account" 
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
              <ListItemText 
                primary="Automatic Session Invalidation" 
                secondary="Previous sessions are automatically invalidated on new login" 
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
              <ListItemText 
                primary="Session Monitoring" 
                secondary="Real-time monitoring for session conflicts" 
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
              <ListItemText 
                primary="Token Blacklisting" 
                secondary="Invalid tokens are blacklisted in Redis" 
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon><CheckCircle color="success" /></ListItemIcon>
              <ListItemText 
                primary="Device Tracking" 
                secondary="Login device and IP information is tracked" 
              />
            </ListItem>
          </List>
        </CardContent>
      </Card>

      {/* Session Conflict Dialog */}
      <SessionConflictDialog
        open={showConflictDialog}
        onClose={() => setShowConflictDialog(false)}
        onForceLogin={handleForceLogin}
        onGoToLogin={handleGoToLogin}
        conflictMessage="This is a simulated session conflict for demonstration purposes."
        deviceInfo={sessionInfo?.deviceInfo}
      />
    </Box>
  );
};

export default SessionManagementExample;
