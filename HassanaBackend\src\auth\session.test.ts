import { Test, TestingModule } from '@nestjs/testing';
import { SessionService } from './session.service';
import { JwtGuard } from './jwt.guard';
import { ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import * as jwt from 'jsonwebtoken';

// Mock Redis
const mockRedis = {
  hgetall: jest.fn(),
  hmset: jest.fn(),
  expire: jest.fn(),
  del: jest.fn(),
  setex: jest.fn(),
  get: jest.fn(),
  keys: jest.fn(),
};

describe('Single Session Implementation Test', () => {
  let sessionService: SessionService;
  let jwtGuard: JwtGuard;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SessionService,
        JwtGuard,
        {
          provide: 'default_IORedisModuleConnectionToken',
          useValue: mockRedis,
        },
      ],
    }).compile();

    sessionService = module.get<SessionService>(SessionService);
    jwtGuard = module.get<JwtGuard>(JwtGuard);

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('Single Session Enforcement', () => {
    it('should invalidate previous session when user logs in from new device', async () => {
      const userId = 'test-user-123';
      const tokenPayload = { id: userId, username: 'testuser', role: 'USER' };
      const deviceInfo1 = { userAgent: 'Browser 1', ip: '***********' };
      const deviceInfo2 = { userAgent: 'Browser 2', ip: '***********' };

      // Mock existing session
      mockRedis.hgetall.mockResolvedValueOnce({
        sessionId: 'old-session-123',
        token: 'old-jwt-token',
        userId: userId,
        isActive: 'true'
      });

      // Mock Redis operations for new session
      mockRedis.hmset.mockResolvedValue('OK');
      mockRedis.expire.mockResolvedValue(1);
      mockRedis.setex.mockResolvedValue('OK'); // For blacklisting

      // First login
      const token1 = await sessionService.createUserSession(userId, tokenPayload, deviceInfo1);
      expect(token1).toBeDefined();

      // Second login (should invalidate first)
      mockRedis.hgetall.mockResolvedValueOnce({
        sessionId: 'session-from-token1',
        token: token1,
        userId: userId,
        isActive: 'true'
      });

      const token2 = await sessionService.createUserSession(userId, tokenPayload, deviceInfo2);
      expect(token2).toBeDefined();
      expect(token2).not.toBe(token1);

      // Verify that old token was blacklisted
      expect(mockRedis.setex).toHaveBeenCalled();
    });

    it('should reject requests with invalidated session', async () => {
      const userId = 'test-user-123';
      const sessionId = 'old-session-123';
      const token = 'old-jwt-token';

      // Mock no active session (session was invalidated)
      mockRedis.hgetall.mockResolvedValue({});
      mockRedis.get.mockResolvedValue(null); // Not blacklisted

      const isValid = await sessionService.validateUserSession(userId, sessionId, token);
      expect(isValid).toBe(false);
    });

    it('should reject requests with mismatched session ID', async () => {
      const userId = 'test-user-123';
      const sessionId = 'old-session-123';
      const currentSessionId = 'new-session-456';
      const token = 'jwt-token';

      // Mock current active session with different session ID
      mockRedis.hgetall.mockResolvedValue({
        sessionId: currentSessionId,
        token: 'current-token',
        userId: userId,
        isActive: 'true'
      });
      mockRedis.get.mockResolvedValue(null); // Not blacklisted
      mockRedis.setex.mockResolvedValue('OK'); // For blacklisting

      const isValid = await sessionService.validateUserSession(userId, sessionId, token);
      expect(isValid).toBe(false);

      // Verify that mismatched token was blacklisted
      expect(mockRedis.setex).toHaveBeenCalled();
    });

    it('should accept requests with valid session', async () => {
      const userId = 'test-user-123';
      const sessionId = 'current-session-123';
      const token = 'current-jwt-token';

      // Mock current active session
      mockRedis.hgetall.mockResolvedValue({
        sessionId: sessionId,
        token: token,
        userId: userId,
        isActive: 'true'
      });
      mockRedis.get.mockResolvedValue(null); // Not blacklisted

      const isValid = await sessionService.validateUserSession(userId, sessionId, token);
      expect(isValid).toBe(true);
    });

    it('should reject blacklisted tokens', async () => {
      const userId = 'test-user-123';
      const sessionId = 'session-123';
      const token = 'blacklisted-token';

      // Mock blacklisted token
      mockRedis.get.mockResolvedValue('true'); // Token is blacklisted

      const isValid = await sessionService.validateUserSession(userId, sessionId, token);
      expect(isValid).toBe(false);
    });
  });

  describe('JWT Guard Session Validation', () => {
    it('should validate session in JWT guard', async () => {
      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => ({
            headers: {
              authorization: 'Bearer valid-jwt-token'
            }
          })
        })
      } as ExecutionContext;

      // Mock JWT verification
      const mockUser = {
        id: 'test-user-123',
        sessionId: 'session-123',
        username: 'testuser',
        role: 'USER'
      };

      jest.spyOn(jwt, 'verify').mockReturnValue(mockUser);
      
      // Mock GqlExecutionContext
      jest.spyOn(GqlExecutionContext, 'create').mockReturnValue({
        getContext: () => ({
          req: {
            headers: { authorization: 'Bearer valid-jwt-token' }
          }
        })
      } as any);

      // Mock session validation
      jest.spyOn(sessionService, 'validateUserSession').mockResolvedValue(true);

      const result = await jwtGuard.canActivate(mockContext);
      expect(result).toBe(true);
      expect(sessionService.validateUserSession).toHaveBeenCalledWith(
        'test-user-123',
        'session-123',
        'valid-jwt-token'
      );
    });

    it('should reject request with invalid session', async () => {
      const mockContext = {
        switchToHttp: () => ({
          getRequest: () => ({
            headers: {
              authorization: 'Bearer invalid-session-token'
            }
          })
        })
      } as ExecutionContext;

      const mockUser = {
        id: 'test-user-123',
        sessionId: 'old-session-123',
        username: 'testuser',
        role: 'USER'
      };

      jest.spyOn(jwt, 'verify').mockReturnValue(mockUser);
      
      jest.spyOn(GqlExecutionContext, 'create').mockReturnValue({
        getContext: () => ({
          req: {
            headers: { authorization: 'Bearer invalid-session-token' }
          }
        })
      } as any);

      // Mock session validation failure
      jest.spyOn(sessionService, 'validateUserSession').mockResolvedValue(false);

      await expect(jwtGuard.canActivate(mockContext)).rejects.toThrow(
        'Session is no longer valid. Please log in again.'
      );
    });
  });

  describe('Session Cleanup', () => {
    it('should properly logout user and blacklist token', async () => {
      const userId = 'test-user-123';
      const token = 'jwt-token-to-blacklist';

      mockRedis.del.mockResolvedValue(1);
      mockRedis.setex.mockResolvedValue('OK');

      // Mock JWT decode for blacklisting
      jest.spyOn(jwt, 'decode').mockReturnValue({
        exp: Math.floor(Date.now() / 1000) + 3600 // 1 hour from now
      });

      await sessionService.logoutUser(userId, token);

      expect(mockRedis.del).toHaveBeenCalledWith(`user_session:${userId}`);
      expect(mockRedis.setex).toHaveBeenCalled(); // Token blacklisted
    });

    it('should force logout user', async () => {
      const userId = 'test-user-123';

      mockRedis.hgetall.mockResolvedValue({
        token: 'user-token',
        sessionId: 'session-123'
      });
      mockRedis.del.mockResolvedValue(1);
      mockRedis.setex.mockResolvedValue('OK');

      jest.spyOn(jwt, 'decode').mockReturnValue({
        exp: Math.floor(Date.now() / 1000) + 3600
      });

      await sessionService.forceLogoutUser(userId);

      expect(mockRedis.del).toHaveBeenCalledWith(`user_session:${userId}`);
      expect(mockRedis.setex).toHaveBeenCalled(); // Token blacklisted
    });
  });
});
