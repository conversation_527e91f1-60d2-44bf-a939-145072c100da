{"name": "@types/cookie-parser", "version": "1.4.9", "description": "TypeScript definitions for cookie-parser", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookie-parser", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "santial<PERSON>", "url": "https://github.com/santialbo"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookie-parser"}, "scripts": {}, "dependencies": {}, "peerDependencies": {"@types/express": "*"}, "typesPublisherContentHash": "2f09243789fa087340511f46b2916af401aba29fb951b7459573a6b793e0596d", "typeScriptVersion": "5.1"}