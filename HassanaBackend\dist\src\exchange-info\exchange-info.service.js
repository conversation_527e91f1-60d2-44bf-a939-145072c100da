"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExchangeInfoService = void 0;
const common_1 = require("@nestjs/common");
const axios_ntlm_1 = require("axios-ntlm");
const xml2js = require("xml2js");
const redis_1 = require("../../redis");
let ExchangeInfoService = class ExchangeInfoService {
    constructor() {
        this.url = 'https://mail.hassana.com.sa/EWS/Exchange.asmx';
    }
    findAll() {
        return `This action returns all exchangeInfo`;
    }
    async findOne(id) {
        let redisValue = await redis_1.redis.hgetall(`user: ${id}`);
        console.log(redisValue);
        let DataForUser = await this.fetchDataForUser(redisValue.username, redisValue.password);
        return DataForUser;
    }
    update(id, updateExchangeInfoDto) {
        return `This action updates a #${id} exchangeInfo`;
    }
    remove(id) {
        return `This action removes a #${id} exchangeInfo`;
    }
    async fetchDataForUser(username, password) {
        const axiosNTLM = (0, axios_ntlm_1.NtlmClient)({
            username: username,
            password: password,
            domain: '',
            workstation: '',
        });
        const options = {
            method: 'post',
            url: this.url,
            headers: {
                Accept: 'application/json, text/plain, */*',
                'Content-Type': 'text/xml; charset=utf-8',
                SOAPAction: 'http://schemas.microsoft.com/exchange/services/2006/messages/FindItem',
            },
            data: this.createRequestBody(),
        };
        try {
            const res = await axiosNTLM(options);
            const parsedData = await this.parseXMLResponse(res.data);
            if (parsedData) {
                const errorCheck = parsedData['s:Envelope']['s:Body']['m:FindItemResponse']['m:ResponseMessages']['m:FindItemResponseMessage']['m:ResponseCode'];
                console.log('ErrorCheck' + errorCheck);
                let data = [];
                if (errorCheck == 'NoError') {
                    let jsonData = parsedData['s:Envelope']['s:Body']['m:FindItemResponse']['m:ResponseMessages']['m:FindItemResponseMessage']['m:RootFolder']['t:Items']['t:CalendarItem'];
                    if (!Array.isArray(jsonData)) {
                        jsonData = [jsonData];
                    }
                    jsonData.map((item) => {
                        const returnObject = {
                            title: item['t:Subject'],
                            status: item['t:LegacyFreeBusyStatus'],
                            description: item['t:Body'],
                            t_start: item['t:Start'],
                            t_end: item['t:End'],
                            uid: item['t:UID'],
                            location: item['t:Location'],
                        };
                        data.push(returnObject);
                    });
                    return data;
                }
                else {
                    return data;
                }
            }
            else {
                throw Error(`No exchange data found against ${username} username`);
            }
        }
        catch (error) {
            console.error(error.message);
            throw error;
        }
    }
    createRequestBody() {
        return `<?xml version="1.0" encoding="utf-8"?>
      <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                     xmlns:m="http://schemas.microsoft.com/exchange/services/2006/messages"
                     xmlns:t="http://schemas.microsoft.com/exchange/services/2006/types"
                     xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
          <soap:Header>
              <t:RequestServerVersion Version="Exchange2010" />
          </soap:Header>
          <soap:Body>
              <m:FindItem Traversal="Shallow">
                  <m:ItemShape>
                      <t:BaseShape>AllProperties</t:BaseShape>
                      <t:AdditionalProperties>
                          <t:FieldURI FieldURI="item:Subject" />
                          <t:FieldURI FieldURI="item:Body" />
                          <t:FieldURI FieldURI="calendar:LegacyFreeBusyStatus" />
                          <t:FieldURI FieldURI="calendar:Start" />
                          <t:FieldURI FieldURI="calendar:End" />
                          <t:FieldURI FieldURI="calendar:UID" />
                          <t:FieldURI FieldURI="calendar:Location" />
                      </t:AdditionalProperties>
                  </m:ItemShape>
                  <m:CalendarView 
                                  StartDate="${new Date().getFullYear()}-01-01T00:00:00Z"
                                  EndDate="${new Date().getFullYear()}-12-31T23:59:59Z" />
                  <m:ParentFolderIds>
                      <t:DistinguishedFolderId Id="calendar" />
                  </m:ParentFolderIds>
              </m:FindItem>
          </soap:Body>
      </soap:Envelope>`;
    }
    ;
    parseXMLResponse(xmlBody) {
        return new Promise((resolve, reject) => {
            const parser = new xml2js.Parser({
                explicitArray: false,
                ignoreAttrs: true,
            });
            parser.parseString(xmlBody, (err, result) => {
                if (err) {
                    console.error('Error parsing XML:', err);
                    return reject(err);
                }
                resolve(result);
            });
        });
    }
    ;
};
exports.ExchangeInfoService = ExchangeInfoService;
exports.ExchangeInfoService = ExchangeInfoService = __decorate([
    (0, common_1.Injectable)()
], ExchangeInfoService);
//# sourceMappingURL=exchange-info.service.js.map