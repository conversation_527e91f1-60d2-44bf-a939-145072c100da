import * as React from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TablePagination from "@mui/material/TablePagination";
import TableRow from "@mui/material/TableRow";
import BasicModal from "@/components/Modal";
import ScrollDialog from "./DialogBox";
import {
  AddShoppingCart,
  Delete,
  Group,
  RemoveRedEye,
} from "@mui/icons-material";
import { DataGrid, GridActionsCellItem } from "@mui/x-data-grid";
import { IconButton, Stack, useTheme } from "@mui/material";
import { formatDateTimeUTC, formatedValue } from "@/components/HelperFunctions";

export default function DataTable(props) {
  const {
    columns,
    rows = [],
    setIndex,
    setOpen,
    setData,
    deleteHandler,
    action,
    updateKey,
  } = props;
  const [page, setPage] = React.useState(0);
  const [rowsPerPage, setRowsPerPage] = React.useState(10);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };
  const theme = useTheme();
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };
  return (
    <>
      <Paper sx={{ width: "100%", border: `1px solid ${theme.palette.text.purple}` }}>
        {/* <TableContainer sx={{ maxHeight: "100vh", overflow: "auto" }}> */}
        <TableContainer sx={{ maxHeight: "75vh", overflow: "auto" }}>
          <Table stickyHeader aria-label="sticky table" sx={{ position: "sticky", top: 0, zIndex: 2 }}>
            <TableHead>
              <TableRow>
                {/* <TableCell align='center' style={{ minWidth: 30 }} >#</TableCell> */}
                {columns &&
                  columns.map((column) => (
                    <TableCell
                      key={column.id}
                      align={column.align}
                      style={{
                        minWidth: column.minWidth,
                        zIndex:"inherit",
                        backgroundColor: theme.palette.background.header,
                        borderBottomColor: theme.palette.background.primary,
                        color: theme.palette.text.white
                      }}
                    >
                      {column.label}
                    </TableCell>
                  ))}
                {action && (
                  <TableCell style={{
                    minWidth: 50,
                    backgroundColor: theme.palette.background.header,
                    borderBottomColor: theme.palette.background.primary,
                    zIndex: 10,
                    color: theme.palette.text.white
                  }} align="center">
                    Action
                  </TableCell>
                )}
              </TableRow>
            </TableHead>
            <TableBody>
            {Array.isArray(rows) && rows.length > 0 ? (
              rows.map((row, index) => (
                <TableRow hover tabIndex={-1} key={index}>
                  {columns.map((column) => {
                    const value = row[column.id];
                    return (
                      <TableCell
                        key={column.id}
                        align={column.align}
                        sx={{
                          backgroundColor: theme.palette.background.secondary,
                          borderBottomColor: theme.palette.background.primary,
                          color: theme.palette.text.primary,
                          cursor: column.id === updateKey ? "pointer" : "default",
                        }}
                        onClick={() => {
                          if (column.id === updateKey) {
                            setOpen(true);
                            setData(row);
                          }
                        }}
                      >
                        {column.format && typeof value === "number"
                          ? column.format(value)
                          : formatedValue(column.id, value)}
                      </TableCell>
                    );
                  })}
                  {action && (
                    <TableCell
                      align="center"
                      sx={{
                        backgroundColor: theme.palette.background.secondary,
                        borderBottomColor: theme.palette.background.primary,
                        color: theme.palette.text.primary,
                      }}
                    >
                      <IconButton
                        aria-label="delete"
                        size="small"
                        onClick={() => deleteHandler(row.id, index)}
                        style={{ color: theme.palette.text.primary }}
                      >
                        <Delete fontSize="small" />
                      </IconButton>
                    </TableCell>
                  )}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length + (action ? 1 : 0)} align="center">
                  No data found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          </Table>
        </TableContainer>
        {/* <TablePagination
          sx={{
            backgroundColor: theme.palette.background.primary,
            color: theme.palette.text.primary
          }}
          rowsPerPageOptions={[10, 25, 100]}
          component="div"
          count={rows && rows.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        /> */}
      </Paper>
    </>
  );
}
