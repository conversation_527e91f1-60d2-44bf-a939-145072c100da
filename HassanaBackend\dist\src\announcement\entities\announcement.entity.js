"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnnouncementViewEntity = exports.Announcement = void 0;
const BaseEntity_1 = require("../../BaseEntity");
const user_entity_1 = require("../../users/entities/user.entity");
const typeorm_1 = require("typeorm");
let Announcement = class Announcement extends BaseEntity_1.BaseEntity {
};
exports.Announcement = Announcement;
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Announcement.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Announcement.prototype, "details", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Announcement.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Boolean)
], Announcement.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Date)
], Announcement.prototype, "visibility", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Announcement.prototype, "image", void 0);
exports.Announcement = Announcement = __decorate([
    (0, typeorm_1.Entity)()
], Announcement);
let AnnouncementViewEntity = class AnnouncementViewEntity extends BaseEntity_1.BaseEntity {
};
exports.AnnouncementViewEntity = AnnouncementViewEntity;
__decorate([
    (0, typeorm_1.ManyToOne)(() => Announcement, (announcement) => announcement.id, { onDelete: 'CASCADE', nullable: false }),
    (0, typeorm_1.JoinColumn)({ name: 'announcement_id' }),
    __metadata("design:type", String)
], AnnouncementViewEntity.prototype, "announcement_id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", String)
], AnnouncementViewEntity.prototype, "user_id", void 0);
exports.AnnouncementViewEntity = AnnouncementViewEntity = __decorate([
    (0, typeorm_1.Entity)({ name: 'announcement_view' }),
    (0, typeorm_1.Unique)(["user_id", "announcement_id"])
], AnnouncementViewEntity);
//# sourceMappingURL=announcement.entity.js.map