// Import necessary dependencies
import React from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import { useTheme } from "@mui/material/styles";
// import useMediaQuery from "@mui/material/useMediaQuery";
import { useColor } from "@/components/ColorContext";

// Define the NewsComponent
const NewsComponent = ({
  display,
  isStandardDesktop,
  isNarrowMobile,
  isWideScreen,
  activeButton,
  handleButtonClick,
}) => {
  const theme = useTheme();
  console.log(theme.palette.text, "color palette test===")
  // const { color } = useColor();
  return (
    <Box>
      <div style={{ marginLeft: "15px" }}>
        <Typography
          variant="h5"
          style={{
            marginTop: "32px",
            fontSize: "16.024px",
            marginLeft: isStandardDesktop ? "16px" : "",
          }}
        >
          Latest news and announcement from <PERSON><PERSON>
        </Typography>
      </div>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          paddingRight: "25px",
          marginTop: "20px",
        }}
      >
        {display === "news" ? (
          <Typography
            variant="h1"
            style={{
              fontSize: "27.47px",
              fontWeight: "700",
              marginTop: "0",
              marginLeft: isStandardDesktop ? "30px" : "15px",
              fontSize: isWideScreen ? "22px" : "27.47px",
            }}
          >
            Newsroom
          </Typography>
        ) : (
          <Typography
            variant="h1"
            style={{
              fontSize: "27.47px",
              fontWeight: "700",
              marginTop: "0",
              marginLeft: isStandardDesktop ? "30px" : "15px",
              fontSize: isWideScreen ? "22px" : "27.47px",
            }}
          >
            Announcements{" "}
          </Typography>
        )}

        <Box
          sx={{
            padding: isNarrowMobile ? "0" : "3px 3px",
            background: theme.palette.background.secondary,
            boxShadow:
              "0px 3.5113511085510254px 17.55675506591797px rgba(0, 0, 0, 0.05)",
          }}
        >
          <Button
            onClick={() => handleButtonClick("announcements")}
            style={{
              color: theme.palette.text.secondary,
              width: isNarrowMobile
                ? "80px"
                : isWideScreen
                ? "110px"
                : "166.23px",
              fontSize: isNarrowMobile ? "7px" : isWideScreen ? "9px" : "11px",
              backgroundColor:
                activeButton === "announcements" ? "#62B6F3" : "",
              color:
                activeButton === "announcements" && "#FDFBFF"
                  ? "#fff"
                  : theme.palette.text.secondary,
              borderRadius: 5,
            }}
          >{console.log(theme.palette.text.secondary, "color test1====")}
            Announcements
          </Button>
          <Button
            onClick={() => handleButtonClick("news")}
            style={{
              color: theme.palette.text.secondary,
              width: isNarrowMobile
                ? "80px"
                : isWideScreen
                ? "110px"
                : "166.23px",
              fontSize: isNarrowMobile ? "7px" : isWideScreen ? "9px" : "11px",
              backgroundColor: activeButton === "news" ? "#62B6F3" : "",
              borderRadius: 5,
              color:
                activeButton === "news" && "#FDFBFF"
                  ? "#fff"
                  : theme.palette.text.secondary,
            }}
          >
            News
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default NewsComponent;
