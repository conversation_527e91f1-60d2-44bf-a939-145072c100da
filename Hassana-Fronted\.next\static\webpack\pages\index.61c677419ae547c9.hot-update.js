"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/graphqlAuth.js":
/*!**********************************!*\
  !*** ./src/utils/graphqlAuth.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAuthOptions: function() { return /* binding */ createAuthOptions; },\n/* harmony export */   createAuthOptionsSync: function() { return /* binding */ createAuthOptionsSync; },\n/* harmony export */   createGraphQLErrorHandler: function() { return /* binding */ createGraphQLErrorHandler; },\n/* harmony export */   getErrorMessage: function() { return /* binding */ getErrorMessage; },\n/* harmony export */   isAuthError: function() { return /* binding */ isAuthError; },\n/* harmony export */   isForbiddenError: function() { return /* binding */ isForbiddenError; }\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _csrfUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./csrfUtils */ \"./src/utils/csrfUtils.js\");\n\n\n/**\r\n * Creates GraphQL query/mutation options with authentication and CSRF headers\r\n * @param {Object} session - NextAuth session object\r\n * @param {Object} additionalOptions - Additional Apollo options\r\n * @returns {Object} Apollo query/mutation options with auth and CSRF headers\r\n */ const createAuthOptions = async function(session) {\n    let additionalOptions = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _additionalOptions_context;\n    const token = (session === null || session === void 0 ? void 0 : session.accessToken) || ( true ? localStorage.getItem(\"jwtToken\") : 0);\n    // Get CSRF headers for secure requests\n    let csrfHeaders = {};\n    try {\n        csrfHeaders = await (0,_csrfUtils__WEBPACK_IMPORTED_MODULE_1__.getSecureHeaders)();\n    } catch (error) {\n        console.warn(\"Failed to get CSRF headers:\", error);\n    }\n    return {\n        ...additionalOptions,\n        context: {\n            ...additionalOptions.context,\n            headers: {\n                ...(_additionalOptions_context = additionalOptions.context) === null || _additionalOptions_context === void 0 ? void 0 : _additionalOptions_context.headers,\n                ...token && {\n                    authorization: \"Bearer \".concat(token)\n                },\n                ...csrfHeaders\n            }\n        },\n        errorPolicy: \"all\"\n    };\n};\n/**\r\n * Synchronous version of createAuthOptions (without CSRF protection)\r\n * Use this only when CSRF protection is not required\r\n * @param {Object} session - NextAuth session object\r\n * @param {Object} additionalOptions - Additional Apollo options\r\n * @returns {Object} Apollo query/mutation options with auth headers only\r\n */ const createAuthOptionsSync = function(session) {\n    let additionalOptions = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _additionalOptions_context;\n    const token = (session === null || session === void 0 ? void 0 : session.accessToken) || ( true ? localStorage.getItem(\"jwtToken\") : 0);\n    return {\n        ...additionalOptions,\n        context: {\n            ...additionalOptions.context,\n            headers: {\n                ...(_additionalOptions_context = additionalOptions.context) === null || _additionalOptions_context === void 0 ? void 0 : _additionalOptions_context.headers,\n                ...token && {\n                    authorization: \"Bearer \".concat(token)\n                }\n            }\n        },\n        errorPolicy: \"all\"\n    };\n};\n/**\r\n * Creates error handler for GraphQL operations\r\n * @param {Function} onAuthError - Callback for authentication errors\r\n * @param {Function} onForbiddenError - Callback for authorization errors\r\n * @param {Function} onOtherError - Callback for other errors\r\n * @returns {Function} Error handler function\r\n */ const createGraphQLErrorHandler = (onAuthError, onForbiddenError, onOtherError)=>{\n    return (error)=>{\n        console.error(\"GraphQL Error:\", error);\n        // Handle GraphQL errors\n        if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n            error.graphQLErrors.forEach((gqlError)=>{\n                const { message, extensions } = gqlError;\n                // Authentication errors\n                if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\") || message.includes(\"Token has expired\") || message.includes(\"Invalid token\")) {\n                    if (onAuthError) {\n                        onAuthError(gqlError);\n                    } else {\n                        console.warn(\"Authentication error:\", message);\n                        // Clear invalid token\n                        if (true) {\n                            localStorage.removeItem(\"jwtToken\");\n                            window.location.href = \"/login?login=false&reason=auth_error\";\n                        }\n                    }\n                } else if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\") || message.includes(\"Required role\")) {\n                    if (onForbiddenError) {\n                        onForbiddenError(gqlError);\n                    } else {\n                        console.warn(\"Authorization error:\", message);\n                    }\n                } else {\n                    if (onOtherError) {\n                        onOtherError(gqlError);\n                    } else {\n                        console.error(\"GraphQL error:\", message);\n                    }\n                }\n            });\n        }\n        // Handle network errors\n        if (error.networkError) {\n            const { statusCode } = error.networkError;\n            if (statusCode === 401) {\n                if (onAuthError) {\n                    onAuthError(error.networkError);\n                } else {\n                    console.warn(\"Network authentication error\");\n                    if (true) {\n                        localStorage.removeItem(\"jwtToken\");\n                        window.location.href = \"/login?login=false&reason=network_auth\";\n                    }\n                }\n            } else if (statusCode === 403) {\n                if (onForbiddenError) {\n                    onForbiddenError(error.networkError);\n                } else {\n                    console.warn(\"Network authorization error\");\n                }\n            } else {\n                if (onOtherError) {\n                    onOtherError(error.networkError);\n                } else {\n                    console.error(\"Network error:\", error.networkError);\n                }\n            }\n        }\n    };\n};\n/**\r\n * Utility function to check if an error is an authentication error\r\n * @param {Object} error - Apollo error object\r\n * @returns {boolean} Whether the error is an authentication error\r\n */ const isAuthError = (error)=>{\n    if (!error) return false;\n    // Check GraphQL errors\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        return error.graphQLErrors.some((gqlError)=>{\n            const { message, extensions } = gqlError;\n            return (extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\") || message.includes(\"Token has expired\") || message.includes(\"Invalid token\");\n        });\n    }\n    // Check network errors\n    if (error.networkError && error.networkError.statusCode === 401) {\n        return true;\n    }\n    return false;\n};\n/**\r\n * Utility function to check if an error is an authorization error\r\n * @param {Object} error - Apollo error object\r\n * @returns {boolean} Whether the error is an authorization error\r\n */ const isForbiddenError = (error)=>{\n    if (!error) return false;\n    // Check GraphQL errors\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        return error.graphQLErrors.some((gqlError)=>{\n            const { message, extensions } = gqlError;\n            return (extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\") || message.includes(\"Required role\");\n        });\n    }\n    // Check network errors\n    if (error.networkError && error.networkError.statusCode === 403) {\n        return true;\n    }\n    return false;\n};\n/**\r\n * Extracts user-friendly error message from GraphQL error\r\n * @param {Object} error - Apollo error object\r\n * @returns {string} User-friendly error message\r\n */ const getErrorMessage = (error)=>{\n    if (!error) return \"An unknown error occurred\";\n    // Check for GraphQL errors first\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        const gqlError = error.graphQLErrors[0];\n        // Return custom messages for common errors\n        if (isAuthError(error)) {\n            return \"Authentication required. Please log in again.\";\n        }\n        if (isForbiddenError(error)) {\n            return \"Access denied. You do not have permission to perform this action.\";\n        }\n        return gqlError.message || \"A GraphQL error occurred\";\n    }\n    // Check for network errors\n    if (error.networkError) {\n        if (error.networkError.statusCode === 401) {\n            return \"Authentication required. Please log in again.\";\n        }\n        if (error.networkError.statusCode === 403) {\n            return \"Access denied. You do not have permission to perform this action.\";\n        }\n        return error.networkError.message || \"A network error occurred\";\n    }\n    return error.message || \"An error occurred\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/graphqlAuth.js\n"));

/***/ })

});