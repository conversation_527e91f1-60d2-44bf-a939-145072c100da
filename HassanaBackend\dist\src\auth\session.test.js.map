{"version": 3, "file": "session.test.js", "sourceRoot": "", "sources": ["../../../src/auth/session.test.ts"], "names": [], "mappings": ";;AAAA,6CAAsD;AACtD,uDAAmD;AACnD,2CAAuC;AAEvC,6CAAsD;AACtD,oCAAoC;AAGpC,MAAM,SAAS,GAAG;IAChB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;IAClB,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;IAChB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;IACjB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;IACd,KAAK,EAAE,IAAI,CAAC,EAAE,EAAE;IAChB,GAAG,EAAE,IAAI,CAAC,EAAE,EAAE;IACd,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE;CAChB,CAAC;AAEF,QAAQ,CAAC,oCAAoC,EAAE,GAAG,EAAE;IAClD,IAAI,cAA8B,CAAC;IACnC,IAAI,QAAkB,CAAC;IAEvB,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,gCAAc;gBACd,oBAAQ;gBACR;oBACE,OAAO,EAAE,sCAAsC;oBAC/C,QAAQ,EAAE,SAAS;iBACpB;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAEb,cAAc,GAAG,MAAM,CAAC,GAAG,CAAiB,gCAAc,CAAC,CAAC;QAC5D,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAW,oBAAQ,CAAC,CAAC;QAG1C,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE,GAAG,EAAE;QAC1C,EAAE,CAAC,sEAAsE,EAAE,KAAK,IAAI,EAAE;YACpF,MAAM,MAAM,GAAG,eAAe,CAAC;YAC/B,MAAM,YAAY,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;YACxE,MAAM,WAAW,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC;YAClE,MAAM,WAAW,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE,EAAE,EAAE,aAAa,EAAE,CAAC;YAGlE,SAAS,CAAC,OAAO,CAAC,qBAAqB,CAAC;gBACtC,SAAS,EAAE,iBAAiB;gBAC5B,KAAK,EAAE,eAAe;gBACtB,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;YAGH,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACxC,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YACtC,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAGxC,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,iBAAiB,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;YACzF,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAG7B,SAAS,CAAC,OAAO,CAAC,qBAAqB,CAAC;gBACtC,SAAS,EAAE,qBAAqB;gBAChC,KAAK,EAAE,MAAM;gBACb,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,iBAAiB,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;YACzF,MAAM,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;YAC7B,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAGhC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,MAAM,GAAG,eAAe,CAAC;YAC/B,MAAM,SAAS,GAAG,iBAAiB,CAAC;YACpC,MAAM,KAAK,GAAG,eAAe,CAAC;YAG9B,SAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;YACxC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEtC,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,mBAAmB,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YACnF,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;YACjE,MAAM,MAAM,GAAG,eAAe,CAAC;YAC/B,MAAM,SAAS,GAAG,iBAAiB,CAAC;YACpC,MAAM,gBAAgB,GAAG,iBAAiB,CAAC;YAC3C,MAAM,KAAK,GAAG,WAAW,CAAC;YAG1B,SAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC;gBAClC,SAAS,EAAE,gBAAgB;gBAC3B,KAAK,EAAE,eAAe;gBACtB,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;YACH,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACtC,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExC,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,mBAAmB,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YACnF,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAG5B,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,MAAM,GAAG,eAAe,CAAC;YAC/B,MAAM,SAAS,GAAG,qBAAqB,CAAC;YACxC,MAAM,KAAK,GAAG,mBAAmB,CAAC;YAGlC,SAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC;gBAClC,SAAS,EAAE,SAAS;gBACpB,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,MAAM;gBACd,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;YACH,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAEtC,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,mBAAmB,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YACnF,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,MAAM,GAAG,eAAe,CAAC;YAC/B,MAAM,SAAS,GAAG,aAAa,CAAC;YAChC,MAAM,KAAK,GAAG,mBAAmB,CAAC;YAGlC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAExC,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,mBAAmB,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YACnF,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,8BAA8B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,WAAW,GAAG;gBAClB,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC;oBACnB,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;wBACjB,OAAO,EAAE;4BACP,aAAa,EAAE,wBAAwB;yBACxC;qBACF,CAAC;iBACH,CAAC;aACiB,CAAC;YAGtB,MAAM,QAAQ,GAAG;gBACf,EAAE,EAAE,eAAe;gBACnB,SAAS,EAAE,aAAa;gBACxB,QAAQ,EAAE,UAAU;gBACpB,IAAI,EAAE,MAAM;aACb,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAGpD,IAAI,CAAC,KAAK,CAAC,6BAAmB,EAAE,QAAQ,CAAC,CAAC,eAAe,CAAC;gBACxD,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;oBACjB,GAAG,EAAE;wBACH,OAAO,EAAE,EAAE,aAAa,EAAE,wBAAwB,EAAE;qBACrD;iBACF,CAAC;aACI,CAAC,CAAC;YAGV,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,qBAAqB,CAAC,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE1E,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,MAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC,oBAAoB,CAC7D,eAAe,EACf,aAAa,EACb,iBAAiB,CAClB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,MAAM,WAAW,GAAG;gBAClB,YAAY,EAAE,GAAG,EAAE,CAAC,CAAC;oBACnB,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;wBACjB,OAAO,EAAE;4BACP,aAAa,EAAE,8BAA8B;yBAC9C;qBACF,CAAC;iBACH,CAAC;aACiB,CAAC;YAEtB,MAAM,QAAQ,GAAG;gBACf,EAAE,EAAE,eAAe;gBACnB,SAAS,EAAE,iBAAiB;gBAC5B,QAAQ,EAAE,UAAU;gBACpB,IAAI,EAAE,MAAM;aACb,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAEpD,IAAI,CAAC,KAAK,CAAC,6BAAmB,EAAE,QAAQ,CAAC,CAAC,eAAe,CAAC;gBACxD,UAAU,EAAE,GAAG,EAAE,CAAC,CAAC;oBACjB,GAAG,EAAE;wBACH,OAAO,EAAE,EAAE,aAAa,EAAE,8BAA8B,EAAE;qBAC3D;iBACF,CAAC;aACI,CAAC,CAAC;YAGV,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,qBAAqB,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAE3E,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAC7D,kDAAkD,CACnD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,MAAM,GAAG,eAAe,CAAC;YAC/B,MAAM,KAAK,GAAG,wBAAwB,CAAC;YAEvC,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YACnC,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAGxC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,eAAe,CAAC;gBACxC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI;aAC1C,CAAC,CAAC;YAEH,MAAM,cAAc,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;YAE/C,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,gBAAgB,MAAM,EAAE,CAAC,CAAC;YACrE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;YACxC,MAAM,MAAM,GAAG,eAAe,CAAC;YAE/B,SAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC;gBAClC,KAAK,EAAE,YAAY;gBACnB,SAAS,EAAE,aAAa;aACzB,CAAC,CAAC;YACH,SAAS,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YACnC,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAExC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,eAAe,CAAC;gBACxC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,IAAI;aAC1C,CAAC,CAAC;YAEH,MAAM,cAAc,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAE7C,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,gBAAgB,MAAM,EAAE,CAAC,CAAC;YACrE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}