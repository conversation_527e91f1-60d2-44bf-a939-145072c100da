"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoleGuard = exports.Roles = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
exports.Roles = {
    ADMIN: "ADMIN",
    USER: "USER"
};
class RoleGuard {
    constructor(role) {
        this.role = role;
    }
    canActivate(context) {
        const ctx = graphql_1.GqlExecutionContext.create(context).getContext();
        if (!ctx.user) {
            throw new common_1.HttpException('User not found in context. Ensure JWT authentication is applied first.', common_1.HttpStatus.UNAUTHORIZED);
        }
        const { role } = ctx.user;
        if (!role) {
            throw new common_1.HttpException('User role not found', common_1.HttpStatus.FORBIDDEN);
        }
        if (role !== this.role) {
            throw new common_1.HttpException(`Access denied. Required role: ${this.role}, User role: ${role}`, common_1.HttpStatus.FORBIDDEN);
        }
        return true;
    }
}
exports.RoleGuard = RoleGuard;
//# sourceMappingURL=role.guard.js.map