import { OffersService } from './offers.service';
import { CreateOffersInput } from './dto/create-offer.dto';
import { UpdateOffersInput } from './dto/update-offer.dto';
import { UUID } from 'crypto';
export declare class OffersResolver {
    private readonly offerService;
    constructor(offerService: OffersService);
    createOffer(createOffersInput: CreateOffersInput): Promise<import("./entities/offers.entity").Offers>;
    offerView(offer_id: UUID, user_id: UUID): Promise<import("./entities/offers.entity").Offers>;
    findAll(user_id: UUID): Promise<import("./entities/offers.entity").Offers[]>;
    findValidOffers(user_id: UUID): Promise<import("./entities/offers.entity").Offers[]>;
    updateOffer(id: UUID, updateOffersInput: UpdateOffersInput): Promise<import("./entities/offers.entity").Offers>;
    removeOffer(id: UUID): Promise<import("./entities/offers.entity").Offers>;
}
