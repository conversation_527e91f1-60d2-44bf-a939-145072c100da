"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendMailToItTechnician = exports.sendMailToParking = exports.sendMail = void 0;
const nodemailer = require('nodemailer');
const smtpTransport = require('nodemailer-smtp-transport');
const createTransporter = () => nodemailer.createTransport(smtpTransport({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: false,
    auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASSWORD,
    },
    tls: {
        rejectUnauthorized: false,
    },
}));
const sendMail = ({ from, to, subject, html, text }) => {
    const transporter = createTransporter();
    transporter.sendMail({ from, to, subject, html, text }, (error, info) => {
        if (error) {
            return console.error('Error sending email:', error);
        }
        console.log('Email sent:', info.response);
    });
    transporter.close();
};
exports.sendMail = sendMail;
const sendMailToParking = (booking) => {
    const imagePath = `${process.env.SERVER_URL}/${booking.registrationDoc}`;
    (0, exports.sendMail)({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Request for Guest Parking',
        text: null,
        html: `
            <p>Dear Admin,</p>
            <p>I hope this email finds you well. I am reaching out to request guest parking for ${new Date(booking.start).toDateString()} at Hassana Building. I will have guests joining me, and I wanted to ensure a smooth parking experience for them.</p>
            <p>Your assistance in this matter is highly appreciated.</p>
            <p>Thank you in advance.</p>
            <p>Best regards</p>
            <p>${imagePath}</p>
            <p>${booking.userName}</p>
            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRT6bZMOCVGXGWmAzgHwOxq-A4lUTNWke-GiixH6D-bGEbFehiPcwzukWlhnbXlqyXeejk&usqp=CAU" alt="Guest Parking Image" style="max-width: 100%;">
            `,
    });
};
exports.sendMailToParking = sendMailToParking;
const sendMailToItTechnician = (booking) => {
    (0, exports.sendMail)({
        from: '<EMAIL>',
        to: '<EMAIL>',
        subject: 'Request for IT Technician',
        text: `Dear IT Technician,\nWe would like to request your presence at an upcoming meeting scheduled to take place in ${booking.location} on ${new Date(booking.start).toDateString()} at ${new Date(booking.end).toLocaleTimeString()}. Your assistance is essential.\nIf you have any conflicting commitments or if there are any concerns, please let us know at your earliest convenience.\nThank you for your cooperation.\nBest regards,\n${booking.userName}`,
        html: null
    });
};
exports.sendMailToItTechnician = sendMailToItTechnician;
//# sourceMappingURL=Emailer.js.map