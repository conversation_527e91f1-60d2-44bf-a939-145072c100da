import React, { createContext, useContext, useEffect, useState } from 'react';
import { initializeCsrfTokens, clearCsrfTokens, getCsrfToken, getSessionToken } from '@/utils/csrfUtils';

const CsrfContext = createContext({
  isInitialized: false,
  csrfToken: null,
  sessionToken: null,
  refreshTokens: () => {},
  clearTokens: () => {},
});

/**
 * CSRF Provider Component
 * Manages CSRF tokens for the entire application
 */
export const CsrfProvider = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [csrfToken, setCsrfToken] = useState(null);
  const [sessionToken, setSessionToken] = useState(null);
  const [error, setError] = useState(null);

  // Initialize CSRF tokens on mount
  useEffect(() => {
    const initTokens = async () => {
      try {
        await initializeCsrfTokens();
        const csrf = await getCsrfToken();
        const session = await getSessionToken();
        
        setCsrfToken(csrf);
        setSessionToken(session);
        setIsInitialized(true);
        setError(null);
        
        console.log('CSRF tokens initialized successfully');
      } catch (err) {
        console.error('Failed to initialize CSRF tokens:', err);
        setError(err.message);
        setIsInitialized(true); // Still mark as initialized to prevent infinite loading
      }
    };

    initTokens();
  }, []);

  // Refresh tokens function
  const refreshTokens = async () => {
    try {
      setError(null);
      await initializeCsrfTokens();
      const csrf = await getCsrfToken();
      const session = await getSessionToken();
      
      setCsrfToken(csrf);
      setSessionToken(session);
      
      console.log('CSRF tokens refreshed successfully');
    } catch (err) {
      console.error('Failed to refresh CSRF tokens:', err);
      setError(err.message);
    }
  };

  // Clear tokens function
  const clearTokens = () => {
    clearCsrfTokens();
    setCsrfToken(null);
    setSessionToken(null);
    console.log('CSRF tokens cleared');
  };

  const contextValue = {
    isInitialized,
    csrfToken,
    sessionToken,
    error,
    refreshTokens,
    clearTokens,
  };

  return (
    <CsrfContext.Provider value={contextValue}>
      {children}
    </CsrfContext.Provider>
  );
};

/**
 * Hook to use CSRF context
 * @returns {Object} CSRF context value
 */
export const useCsrf = () => {
  const context = useContext(CsrfContext);
  if (!context) {
    throw new Error('useCsrf must be used within a CsrfProvider');
  }
  return context;
};

/**
 * HOC to provide CSRF protection to components
 * @param {React.Component} WrappedComponent - Component to wrap
 * @returns {React.Component} Wrapped component with CSRF protection
 */
export const withCsrfProtection = (WrappedComponent) => {
  return function CsrfProtectedComponent(props) {
    const { isInitialized, error } = useCsrf();

    if (!isInitialized) {
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh' 
        }}>
          <div>Initializing security...</div>
        </div>
      );
    }

    if (error) {
      console.warn('CSRF initialization error:', error);
      // Continue rendering even with CSRF error for better UX
      // The error will be logged and can be handled by individual components
    }

    return <WrappedComponent {...props} />;
  };
};

export default CsrfProvider;
