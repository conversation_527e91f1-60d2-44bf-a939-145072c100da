import { Box, Typography, Grid, Divider } from '@mui/material';

const SideCard = () => {
  return (
    <Box
    sx={{
        position: 'relative',
        width: '100%',
        // maxWidth: 476,
        p: 3,
        borderRadius: 3,
        boxShadow: '0 8px 15px rgba(0, 0, 0, 0.1)', 
        backgroundColor: '#fff',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          width: '5px',
          height: '100%',
          background: 'linear-gradient(to bottom, #a368e2, #66b1f2)',
          borderTopLeftRadius: 12,
          borderBottomLeftRadius: 12,
        },
      }}
    >
      <Typography variant="subtitle1" sx={{ color: '#2c3e50', fontWeight: 600,  mb: 2 }}>
  <Box
    component="span"
    sx={{
      borderBottom: '3px solid #9C71E4', 
      display: 'inline-block',
      pb: '2px',
    }}
  >
      CFO - Blessed with a{' '}
  </Box>
    BABY BOY!!
      </Typography>

      <Grid container spacing={20} mb={2}>
        <Grid item xs={6} >
          <Typography variant="body2"   sx={{ color: '#b0b0b0' }}>Name:</Typography>
          <Typography fontWeight="bold" sx={{color: '#2c3e50'}}>John David</Typography>
        </Grid>
        <Grid item xs={6}   >
          <Typography variant="body2"   sx={{ color: '#b0b0b0' }}>Code</Typography>
          <Typography fontWeight="bold" sx={{color: '#2c3e50'}}>256325</Typography>
        </Grid>
        </Grid>

        <Grid container spacing={15} mb={2}>
        <Grid item xs={6}>
          <Typography variant="body2"  sx={{ color: '#b0b0b0' }}>Contact Information</Typography>
          <Typography fontWeight="bold" sx={{color: '#2c3e50'}}>0332569986</Typography>
        </Grid>
        <Grid item xs={6}>
          <Typography variant="body2"  sx={{ color: '#b0b0b0' }}>Expiry Date</Typography>
          <Typography fontWeight="bold" sx={{color: '#2c3e50'}}>11/13/2023 09:00 AM</Typography>
        </Grid>
      </Grid>

      

      <Typography variant="body2" sx={{ color: '#b0b0b0' }} gutterBottom>
        Description
      </Typography>
      <Typography variant="body2" color="text.secondary">
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Enim, dolorum! Sed assumenda reiciendis quos blanditiis aliquam fuga porro cupiditate repellendus et quaerat, harum fugit eligendi eveniet voluptate voluptates dignissimos, odio amet provident alias debitis? Laboriosam?
      </Typography>
    </Box>
  );
};

export default SideCard;
