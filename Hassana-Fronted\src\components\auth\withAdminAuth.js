import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import CircularProgress from "@mui/material/CircularProgress";
import UnauthorizedAccessPage from "./UnauthorizedAccessPage.jsx"; // Import or create this component

const withAdminAuth = (WrappedComponent) => {
  return function AdminProtectedComponent(props) {
    const { data: session, status } = useSession();
    const router = useRouter();
    const [isAuthorized, setIsAuthorized] = useState(false);

    useEffect(() => {
      if (status === "loading") return;
      if (status === "unauthenticated" || session?.user.role !== "ADMIN") {
        setIsAuthorized(false);
      } else {
        setIsAuthorized(true);
      }
    }, [status, session, router]);

    if (status === "loading") {
      return <CircularProgress color="secondary" />;
    }

    if (!isAuthorized) {
      return <UnauthorizedAccessPage />;
    }

    return <WrappedComponent {...props} />;
  };
};

export default withAdminAuth;
