import { Args, Mutation, Query, Resolver, Context } from '@nestjs/graphql';
import { UserService } from './user.service';
// import { User } from './entities/user.entity';
import { UserSchema } from './schema/user.schema';
import * as jwt from 'jsonwebtoken';
import { LoginUser } from './dto/login-user';
import { User } from './entities/user.entity';
import { Logger } from '@nestjs/common';
import { redis } from '../../redis';
import { UUID } from 'crypto';
import { UserPaginationSchema } from './schema/usersMeta.schema';
import { SessionService } from '../auth/session.service';
@Resolver(() => UserSchema)
export class UserResolver {
  constructor(
    private readonly userService: UserService,
    private readonly sessionService: SessionService
  ) { }

  @Query(() => UserPaginationSchema, { name: 'users' })
  async getAllUsers(@Args('page') page: number, @Args('pageSize') pageSize: number): Promise<any> {
    try {
      const { users, meta } = await this.userService.findAllUsers(page, pageSize);
      return { users, meta };
    } catch (error) {
      return error;
    };
  }
  
  @Query((returns) => [UserSchema])
  getNewUsers(@Args('days') days: number) {
    return this.userService.findNewUsers(days);
  }

  @Query((returns) => [UserSchema])
  async getCulturalAmbassadors() {
    return await this.userService.findAllCulturalAmbassadors();
  }

  @Mutation((returns) => String)
  async logoutUser(
    @Args('userId') userId: string,
    @Context() context: any
  ): Promise<string> {
    try {
      const req = context.req;
      const authHeader = req.headers.authorization;

      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new Error('Authorization header not found');
      }

      const token = authHeader.split(' ')[1];

      // Logout user and invalidate session
      await this.sessionService.logoutUser(userId, token);

      Logger.log(`User ${userId} logged out successfully`);
      return 'Logged out successfully';
    } catch (error) {
      Logger.error('Logout error:', error);
      throw new Error('Logout failed: ' + error.message);
    }
  }

  @Mutation((returns) => String)
  async forceLogoutUser(@Args('userId') userId: string): Promise<string> {
    try {
      // Force logout user from all devices (admin function)
      await this.sessionService.forceLogoutUser(userId);

      Logger.log(`User ${userId} force logged out successfully`);
      return 'User force logged out successfully';
    } catch (error) {
      Logger.error('Force logout error:', error);
      throw new Error('Force logout failed: ' + error.message);
    }
  }

  @Query((returns) => LoginUser)
  async loginUser(
    @Args('username') username: string,
    @Args('password') password: string,
    @Context() context: any
  ): Promise<object> {
    const req = context.req;
    try {
      let userAuthenticationResult = await this.userService.userAuth(username, password);
      Logger.log("User Authentication: ", userAuthenticationResult);
      if (userAuthenticationResult) {
        let user: User;
        user = await this.userService.findUserByEmail(userAuthenticationResult.userPrincipalName);

        if (!user?.id) {
          Logger.log('User not found...');
          user = await this.userService.createNewUser(userAuthenticationResult);
          Logger.log("Created new user > ");
        }

        await redis.hset(`user: ${user.id}`, 'password', password);
        await redis.hset(`user: ${user.id}`, 'username', username);

        let redisValue = await redis.hgetall(`user: ${user.id}`);
        Logger.log('Get all hashes', redisValue);

        let tokenPayload = {
          id: user.id,
          username: user.name,
          role: user.role,
        };

        // Get device/browser information from request headers
        const userAgent = req?.headers?.['user-agent'] || 'Unknown';
        const deviceInfo = {
          userAgent,
          ip: req?.ip || req?.connection?.remoteAddress || 'Unknown',
          timestamp: new Date().toISOString()
        };

        // Create session using SessionService (this will invalidate any existing sessions)
        const token = await this.sessionService.createUserSession(
          user.id.toString(),
          tokenPayload,
          deviceInfo
        );

        let payload: LoginUser = {
          id: user.id,
          username: user.name,
          role: user.role,
          token: token,
        };

        return payload;
      } else {
        Logger.log('Authentication failed...');
        throw new Error('Authentication failed');
      }
    } catch (error) {
      Logger.error('An error occurred:', error);
      console.log(error);
      throw new Error('Server Error');
    }
  }

  // @Mutation(() => UserSchema)
  // @Mutation((returns) => String)
  // createUser() {
  //   return this.userService.createUser();
  // }

  //   @Query(() => [User], { name: 'users' })
  //   findAll() {
  //     return this.usersService.findAll();
  //   }

  @Query(() => UserSchema)
  findUserById(@Args('id') id: UUID) {
    return this.userService.findUserById(id);
  }

  // @Mutation((returns) => UserSchema)
  // updateUser(@Args('updateUserInput') updateUserInput: UpdateUserInput) {
  //   return this.userService.update(updateUserInput.id, updateUserInput);
  // }

  //   @Mutation(() => User)
  //   removeUser(@Args('id', { type: () => Int }) id: number) {
  //     return this.usersService.remove(id);
  //   }
}
