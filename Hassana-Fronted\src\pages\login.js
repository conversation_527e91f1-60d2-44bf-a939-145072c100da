import AppBar from "../components/AppBar";
import React, { useEffect, useState } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import SideBar from "@/components/SideBar";
import { Box, Grid, Typography } from "@mui/material";
import { useMediaQuery, CssBaseline } from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import News from "../components/News";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";

// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { getExternalNews } from "@/Data/News";

const baseSettings = {
  infinite: true,
  speed: 1200,
  slidesToShow: 1,
  slidesToScroll: 1,
  autoplay: true,
  autoplaySpeed: 2000,
  arrows: false,
};

const settings = {
  ...baseSettings,
  dots: true,
};

const verticalSettings = {
  ...baseSettings,
  dots: false,
  vertical: true,
};
const verticalSlides = [{ title: "Vision" }, { title: "Mission" }];

const horizontalSlides = [
  {
    content:
      "To be a global benchmark in investing for national social security",
  },
  {
    content:
      "To invest for the long term across global asset classes, using our thoughtful approach, robust process, and world-class talent",
  },
];

const secondslides = [
  {
    content:
      "<strong>We empower our people</strong>, We encourage our people to take charge of tasks. We empower them to keep learning and upskilling.",
  },
  {
    content:
      "We consider broader and think deeper to deliver results, while managing risk on par with global standards.",
  },
];


const myStyles = {
  background: `linear-gradient(180deg, #A665E1 0%, #62B6F3 99.99%)`,
  width: "9px",
  height: "12em",
};

const AnnouncementSliderDiv = ({ children }) => (
  <div
    style={{
      display: "flex",
      marginTop: "10px",
      padding: "10px 15px",
      width: "100%",
      height: "auto",
    }}
  >
    <div style={myStyles}></div>
    <div style={{ padding: "25px" }}>
      <h1
        style={{
          color: "#1B3745",
          fontSize: "28px",
          fontWeight: "700",
          lineHeight: "normal",
        }}
      >
        {children[0]}
      </h1>
      <div
        style={{
          width: "20%",
          height: "2px",
          border: "2px #62B6F3 solid",
          margin: "20px 0px",
        }}
      ></div>
      <p
        style={{
          color: "gray",
          color: "#A7A7A7",
          overflow: "hidden",
          fontSize: "18px",
          fontStyle: "normal",
          fontWeight: "400",
          lineHeight: "normal",
          marginTop: "25x",
        }}
      >
        {children[1]}
      </p>
    </div>
  </div>
);

const SliderDiv = ({ children }) => {
  const isTablet = useMediaQuery("(max-width:1024px)");

  const isLargeScreen = useMediaQuery("(max-width:1230px)");
  return (
    <div
      style={{
        opacity: 1,
        width: "80%",
        height: "9.6875rem",
        borderRadius: "0.625rem",
        margin: "auto",
        textAlign: "left",
        padding: "20px",
        background:
          "linear-gradient(180deg, rgba(43, 78, 96, 0.50) 0%, rgba(55, 95, 116, 0.50) 100%)",
        display: "flex",
      }}
    >
      <h3
        style={{
          fontSize: isTablet ? "13px" : isLargeScreen ? "15px" : "17px",
          color: "white",
        }}
      >
        {children}
      </h3>
    </div>
  );
};

const theme = createTheme();

export default function Main() {
  const isTablet = useMediaQuery("(max-width:1024px)");
  const isBetween1023And1024 = useMediaQuery(
    "(min-width: 1023px) and (max-width: 1024px)"
  );
  // const isExtraSmallScreen = useMediaQuery((theme) =>
  //   theme.breakpoints.down("xs")
  // );
  // const isTablet = useMediaQuery("(max-width:1024px)");
  // const isExtraLargeScreen = useMediaQuery("(max-width:1300px)");

  // const isXLargeScreen = useMediaQuery("(min-width:1200px)");
  // const isSmallScreen = useMediaQuery((theme) => theme.breakpoints.down("sm"));
  // const isLargeScreen = useMediaQuery((theme) => theme.breakpoints.down("lg"));

  const [news, setNews] = useState([]);

  useEffect(() => {
    const fetchNews = async () => {
      try {
        let response = await getExternalNews();

        console.log("external", response.data);
        setNews(response.data);

      } catch (error) {
        console.log(error);
      }
    }
    fetchNews()
  }, [])

  return (
    // <ThemeProvider theme={theme}>
      <>
        <AppBar />
        <Grid container>
          <Grid item></Grid>
          <SideBar />
          <Grid
            item
            xs={12}
            sm={12}
            md={isBetween1023And1024 ? 8 : isTablet ? 12 : 8}
            lg={isTablet ? 8 : 9}
          >
            {/* Announcement and News Components */}
            <div
              style={{
                overflow: "hidden",
                marginTop: "70px",
                height: "90vh",
                width: "100%",
              }}
            >
              <div style={{ width: "100%" }}>
                <div style={{ padding: "25px 0px 0px 35px", height: "auto" }}>
                  <h1
                    style={{
                      fontWeight: "600",
                      fontSize: "28px",
                      lineHeight: "17px",
                      font: "Helvetica",
                      color: "#1B3745",
                    }}
                  >
                    Latest News From Hassana
                  </h1>
                  <div
                    style={{
                      width: "120px",
                      height: "5px",
                      marginTop: "10px",
                      background: "#00BC82",
                    }}
                  ></div>
                 
                </div>
              
              </div>

              <div
                style={{
                  width: "94%",
                  padding: "20px 20px",
                  marginTop: "15px",
                }}
              >
                <div style={{ display: "flex" }}>
                  <h1
                    style={{
                      font: "Urbanist",
                      fontWeight: "700",
                      fontSize: "28px",
                      lineHeight: "32px",
                      color: "#1B3745",
                      marginLeft: "15px",
                    }}
                  >
                    News Room
                  </h1>

                  <Box
                    sx={{
                      display: "flex",
                      height: "2px",
                      width: "80%",
                      background: "#DADADA",
                      margin: "15px 0px 0px 15px",
                    }}
                  ></Box>
                </div>

                {news && <News news={news} />}
              </div>
            </div>
          </Grid>
        </Grid>
      </>
    // </ThemeProvider>
  );
}
