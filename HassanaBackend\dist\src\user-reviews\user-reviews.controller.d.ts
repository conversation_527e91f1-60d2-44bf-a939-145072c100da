import { UserReviewsService } from './user-reviews.service';
import { CreateUserReviewDto } from './dto/create-user-review.dto';
import { UpdateUserReviewDto } from './dto/update-user-review.dto';
export declare class UserReviewsController {
    private readonly userReviewsService;
    constructor(userReviewsService: UserReviewsService);
    create(createUserReviewDto: CreateUserReviewDto, request: Request): Promise<{
        status: boolean;
        message: string;
        data: import("./entities/user-review.entity").UserReview;
        errorMessage?: undefined;
    } | {
        status: boolean;
        message: string;
        errorMessage: any;
        data?: undefined;
    }>;
    findAll(): Promise<{
        status: boolean;
        message: string;
        data: import("./entities/user-review.entity").UserReview[];
        errorMessage?: undefined;
    } | {
        status: boolean;
        message: string;
        errorMessage: any;
        data?: undefined;
    }>;
    findOne(id: string): string;
    update(id: string, updateUserReviewDto: UpdateUserReviewDto): string;
    remove(id: string): string;
}
