"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CsrfController = void 0;
const common_1 = require("@nestjs/common");
const csrf_service_1 = require("./csrf.service");
const session_service_1 = require("../auth/session.service");
const swagger_1 = require("@nestjs/swagger");
let CsrfController = class CsrfController {
    constructor(csrfService, sessionService) {
        this.csrfService = csrfService;
        this.sessionService = sessionService;
    }
    getCsrfToken(req, res) {
        try {
            const { csrfToken, sessionToken } = this.csrfService.createCsrfResponse();
            res.cookie('session-token', sessionToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'strict',
                maxAge: 24 * 60 * 60 * 1000,
            });
            return res.status(common_1.HttpStatus.OK).json({
                csrfToken,
                sessionToken,
                message: 'CSRF token generated successfully'
            });
        }
        catch (error) {
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                message: 'Failed to generate CSRF token',
                error: error.message
            });
        }
    }
    verifyCsrfToken(req, res) {
        try {
            const csrfToken = req.headers['x-csrf-token'];
            const sessionToken = req.headers['x-session-token'];
            if (!csrfToken || !sessionToken) {
                return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                    valid: false,
                    message: 'CSRF token and session token are required'
                });
            }
            const isValid = this.csrfService.verifyCsrfToken(csrfToken, sessionToken);
            return res.status(common_1.HttpStatus.OK).json({
                valid: isValid,
                message: isValid ? 'CSRF token is valid' : 'CSRF token is invalid'
            });
        }
        catch (error) {
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                valid: false,
                message: 'Failed to verify CSRF token',
                error: error.message
            });
        }
    }
    async getSessionStatus(userId, res) {
        try {
            if (!userId) {
                return res.status(common_1.HttpStatus.BAD_REQUEST).json({
                    message: 'userId parameter is required'
                });
            }
            const sessionInfo = await this.sessionService.getUserSession(userId);
            const allSessions = await this.sessionService.getAllActiveSessions();
            return res.status(common_1.HttpStatus.OK).json({
                userId,
                currentSession: sessionInfo,
                allActiveSessions: allSessions,
                message: sessionInfo ? 'User has active session' : 'No active session found'
            });
        }
        catch (error) {
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({
                message: 'Failed to get session status',
                error: error.message
            });
        }
    }
};
exports.CsrfController = CsrfController;
__decorate([
    (0, common_1.Get)('csrf-token'),
    (0, swagger_1.ApiOperation)({ summary: 'Get CSRF token for secure requests' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'CSRF token generated successfully',
        schema: {
            type: 'object',
            properties: {
                csrfToken: { type: 'string' },
                sessionToken: { type: 'string' },
                message: { type: 'string' }
            }
        }
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], CsrfController.prototype, "getCsrfToken", null);
__decorate([
    (0, common_1.Post)('verify-csrf'),
    (0, swagger_1.ApiOperation)({ summary: 'Verify CSRF token (for testing)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'CSRF token verification result',
        schema: {
            type: 'object',
            properties: {
                valid: { type: 'boolean' },
                message: { type: 'string' }
            }
        }
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", void 0)
], CsrfController.prototype, "verifyCsrfToken", null);
__decorate([
    (0, common_1.Get)('session-status'),
    (0, swagger_1.ApiOperation)({ summary: 'Get session status for debugging' }),
    __param(0, (0, common_1.Query)('userId')),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CsrfController.prototype, "getSessionStatus", null);
exports.CsrfController = CsrfController = __decorate([
    (0, swagger_1.ApiTags)('Security'),
    (0, common_1.Controller)('security'),
    __metadata("design:paramtypes", [csrf_service_1.CsrfService,
        session_service_1.SessionService])
], CsrfController);
//# sourceMappingURL=csrf.controller.js.map