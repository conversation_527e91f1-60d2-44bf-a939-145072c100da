"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MissingConfigurationsError = void 0;
/**
 * Thrown when async configurations are missing.
 */
class MissingConfigurationsError extends Error {
    constructor() {
        super(`Missing required asynchronous configurations. Expected one of: "useFactory", "useClass", "useExisting".`);
        this.name = MissingConfigurationsError.name;
    }
}
exports.MissingConfigurationsError = MissingConfigurationsError;
