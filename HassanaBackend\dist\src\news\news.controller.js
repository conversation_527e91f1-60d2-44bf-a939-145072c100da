"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const path_1 = require("path");
const news_service_1 = require("./news.service");
let multerOptions = {
    storage: (0, multer_1.diskStorage)({
        destination: './resource/v1/news',
        filename: (req, featuredImage, callback) => {
            const uniqueSuffix = Date.now() + Math.round(Math.random() * 1e3);
            const ext = (0, path_1.extname)(featuredImage.originalname);
            const fileName = `${uniqueSuffix}${ext}`.toString();
            callback(null, fileName);
        },
    }),
};
let NewsController = class NewsController {
    constructor(newsService) {
        this.newsService = newsService;
    }
    async createNews(file, createNewsDto) {
        try {
            const transformedDto = this.transformFormData(createNewsDto);
            let path = file?.path;
            console.log(path);
            let featuredImage = path?.replace(/resource\/v1[\/\\]/g, '');
            let data = await this.newsService.createNews({
                ...transformedDto,
                featuredImage,
            });
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        }
        catch (error) {
            console.log(error);
            let Response = {
                code: error?.code || 500,
                message: error?.message,
                error: error?.driverError,
            };
            return Response;
        }
    }
    transformFormData(formData) {
        const transformed = { ...formData };
        if (transformed.visibility) {
            transformed.visibility = new Date(transformed.visibility);
        }
        if (transformed.publication) {
            transformed.publication = new Date(transformed.publication);
        }
        if (transformed.created_on) {
            transformed.created_on = new Date(transformed.created_on);
        }
        if (transformed.updated_on) {
            transformed.updated_on = new Date(transformed.updated_on);
        }
        return transformed;
    }
    async findAllNews() {
        try {
            let data = await this.newsService.allNews();
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code || 500,
                message: error?.message,
                error: error,
            };
            return Response;
        }
    }
    async getExternalNews() {
        try {
            let data = await this.newsService.allExternalNews();
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code || 500,
                message: error?.message,
                error: error,
            };
            return Response;
        }
    }
    async getInternalNews() {
        try {
            let data = await this.newsService.allInternalNews();
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code || 500,
                message: error?.message,
                error: error,
            };
            return Response;
        }
    }
    async updateNews(id, updateNewsInput, file) {
        try {
            const transformedDto = this.transformFormData(updateNewsInput);
            if (file) {
                let path = file?.path;
                var featuredImage = path?.replace(/resource\/v1[\/\\]/g, '');
            }
            let data = await this.newsService.update(id, { ...transformedDto, featuredImage });
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code || 500,
                message: error?.message,
                error: error?.driverError,
            };
            return Response;
        }
    }
    async removeNews(id) {
        try {
            let data = await this.newsService.remove(id);
            let Response = {
                code: 200,
                message: 'Success',
                data: data,
            };
            return Response;
        }
        catch (error) {
            let Response = {
                code: error?.code || 500,
                message: error?.message,
                error: error,
            };
            return Response;
        }
    }
};
exports.NewsController = NewsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('featuredImage', multerOptions)),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], NewsController.prototype, "createNews", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NewsController.prototype, "findAllNews", null);
__decorate([
    (0, common_1.Get)('/external-news'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NewsController.prototype, "getExternalNews", null);
__decorate([
    (0, common_1.Get)('/internal-news'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], NewsController.prototype, "getInternalNews", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('featuredImage', multerOptions)),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], NewsController.prototype, "updateNews", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], NewsController.prototype, "removeNews", null);
exports.NewsController = NewsController = __decorate([
    (0, common_1.Controller)('v1/our-news'),
    __metadata("design:paramtypes", [news_service_1.NewsService])
], NewsController);
//# sourceMappingURL=news.controller.js.map