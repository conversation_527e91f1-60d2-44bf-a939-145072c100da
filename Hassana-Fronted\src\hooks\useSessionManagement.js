import { useEffect, useState } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useApolloClient } from '@apollo/client';
import { 
  startSessionMonitoring, 
  stopSessionMonitoring, 
  handleSessionConflict,
  logoutCurrentUser,
  getCurrentSessionInfo,
  isLoggedInElsewhere 
} from '@/utils/sessionUtils';

/**
 * Custom hook for managing single session per user
 * @param {Object} options - Configuration options
 * @param {boolean} options.enableMonitoring - Whether to enable session monitoring
 * @param {Function} options.onSessionConflict - Custom handler for session conflicts
 * @param {number} options.checkInterval - Session check interval in milliseconds
 */
export const useSessionManagement = (options = {}) => {
  const { 
    enableMonitoring = true, 
    onSessionConflict,
    checkInterval = 30000 
  } = options;

  const { data: session, status } = useSession();
  const apolloClient = useApolloClient();
  const [sessionConflict, setSessionConflict] = useState(false);
  const [sessionInfo, setSessionInfo] = useState(null);

  // Initialize session monitoring when user is authenticated
  useEffect(() => {
    if (status === 'authenticated' && session?.user && enableMonitoring) {
      const currentSession = getCurrentSessionInfo();
      
      if (currentSession) {
        setSessionInfo(currentSession);
        
        // Start monitoring session validity
        startSessionMonitoring(
          currentSession.userId,
          currentSession.sessionId,
          () => {
            console.warn('Session conflict detected - user logged in elsewhere');
            setSessionConflict(true);
            
            if (onSessionConflict) {
              onSessionConflict();
            } else {
              handleDefaultSessionConflict();
            }
          }
        );

        console.log('Session management initialized for user:', currentSession.userId);
      }
    }

    // Cleanup on unmount or when session changes
    return () => {
      stopSessionMonitoring();
    };
  }, [status, session, enableMonitoring]);

  // Handle default session conflict
  const handleDefaultSessionConflict = async () => {
    try {
      await signOut({ 
        redirect: true, 
        callbackUrl: '/login?reason=session_conflict&message=You have been logged in from another device' 
      });
    } catch (error) {
      console.error('Error during session conflict logout:', error);
      window.location.href = '/login?reason=session_conflict';
    }
  };

  // Manual logout function
  const logout = async () => {
    try {
      if (session?.user?.id) {
        await logoutCurrentUser(session.user.id, apolloClient);
      }
      
      stopSessionMonitoring();
      
      await signOut({ 
        redirect: true, 
        callbackUrl: '/login?reason=manual_logout' 
      });
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if backend call fails
      await signOut({ 
        redirect: true, 
        callbackUrl: '/login?reason=logout_error' 
      });
    }
  };

  // Force logout (admin function)
  const forceLogout = async (targetUserId) => {
    try {
      await apolloClient.mutate({
        mutation: gql`
          mutation ForceLogoutUser($userId: String!) {
            forceLogoutUser(userId: $userId)
          }
        `,
        variables: { userId: targetUserId }
      });
      
      console.log(`User ${targetUserId} force logged out`);
    } catch (error) {
      console.error('Force logout error:', error);
      throw error;
    }
  };

  // Check if current session is valid
  const checkSessionValidity = () => {
    const currentSession = getCurrentSessionInfo();
    if (!currentSession) return false;

    // Check if session ID matches what we expect
    if (sessionInfo && currentSession.sessionId !== sessionInfo.sessionId) {
      console.warn('Session ID mismatch detected');
      setSessionConflict(true);
      return false;
    }

    return true;
  };

  // Resolve session conflict
  const resolveSessionConflict = () => {
    setSessionConflict(false);
    handleDefaultSessionConflict();
  };

  return {
    // Session state
    sessionInfo,
    sessionConflict,
    isAuthenticated: status === 'authenticated',
    isLoading: status === 'loading',
    
    // Session actions
    logout,
    forceLogout,
    checkSessionValidity,
    resolveSessionConflict,
    
    // Session monitoring control
    startMonitoring: () => {
      if (sessionInfo) {
        startSessionMonitoring(
          sessionInfo.userId,
          sessionInfo.sessionId,
          () => setSessionConflict(true)
        );
      }
    },
    stopMonitoring: stopSessionMonitoring,
  };
};

/**
 * Hook specifically for detecting session conflicts
 * @returns {Object} Session conflict state and handlers
 */
export const useSessionConflictDetection = () => {
  const [hasConflict, setHasConflict] = useState(false);
  const [conflictMessage, setConflictMessage] = useState('');

  useEffect(() => {
    // Check URL parameters for session conflict indicators
    const urlParams = new URLSearchParams(window.location.search);
    const reason = urlParams.get('reason');
    const message = urlParams.get('message');

    if (reason === 'session_conflict') {
      setHasConflict(true);
      setConflictMessage(message || 'You have been logged in from another device');
    }
  }, []);

  const dismissConflict = () => {
    setHasConflict(false);
    setConflictMessage('');
    
    // Clean URL
    const url = new URL(window.location);
    url.searchParams.delete('reason');
    url.searchParams.delete('message');
    window.history.replaceState({}, '', url);
  };

  return {
    hasConflict,
    conflictMessage,
    dismissConflict
  };
};

/**
 * Component wrapper for session conflict detection
 * @param {React.Component} WrappedComponent - Component to wrap
 * @returns {React.Component} Component with session conflict detection
 */
export const withSessionConflictDetection = (WrappedComponent) => {
  return function SessionConflictWrapper(props) {
    const { hasConflict, conflictMessage, dismissConflict } = useSessionConflictDetection();

    if (hasConflict) {
      return (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.8)',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          zIndex: 9999
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '2rem',
            borderRadius: '8px',
            maxWidth: '400px',
            textAlign: 'center'
          }}>
            <h3>Session Conflict</h3>
            <p>{conflictMessage}</p>
            <button 
              onClick={dismissConflict}
              style={{
                padding: '0.5rem 1rem',
                backgroundColor: '#1976d2',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Continue to Login
            </button>
          </div>
        </div>
      );
    }

    return <WrappedComponent {...props} />;
  };
};

export default SessionManager;
