"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/graphqlAuth.js":
/*!**********************************!*\
  !*** ./src/utils/graphqlAuth.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAuthOptions: function() { return /* binding */ createAuthOptions; },\n/* harmony export */   createGraphQLErrorHandler: function() { return /* binding */ createGraphQLErrorHandler; },\n/* harmony export */   getErrorMessage: function() { return /* binding */ getErrorMessage; },\n/* harmony export */   isAuthError: function() { return /* binding */ isAuthError; },\n/* harmony export */   isForbiddenError: function() { return /* binding */ isForbiddenError; }\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\r\n * Creates GraphQL query/mutation options with authentication headers\r\n * @param {Object} session - NextAuth session object\r\n * @param {Object} additionalOptions - Additional Apollo options\r\n * @returns {Object} Apollo query/mutation options with auth headers\r\n */ const createAuthOptions = function(session) {\n    let additionalOptions = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _additionalOptions_context;\n    const token = (session === null || session === void 0 ? void 0 : session.accessToken) || ( true ? localStorage.getItem(\"jwtToken\") : 0);\n    return {\n        ...additionalOptions,\n        context: {\n            ...additionalOptions.context,\n            headers: {\n                ...(_additionalOptions_context = additionalOptions.context) === null || _additionalOptions_context === void 0 ? void 0 : _additionalOptions_context.headers,\n                ...token && {\n                    authorization: \"Bearer \".concat(token)\n                }\n            }\n        },\n        errorPolicy: \"all\"\n    };\n};\n/**\r\n * Creates error handler for GraphQL operations\r\n * @param {Function} onAuthError - Callback for authentication errors\r\n * @param {Function} onForbiddenError - Callback for authorization errors\r\n * @param {Function} onOtherError - Callback for other errors\r\n * @returns {Function} Error handler function\r\n */ const createGraphQLErrorHandler = (onAuthError, onForbiddenError, onOtherError)=>{\n    return (error)=>{\n        console.error(\"GraphQL Error:\", error);\n        // Handle GraphQL errors\n        if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n            error.graphQLErrors.forEach((gqlError)=>{\n                const { message, extensions } = gqlError;\n                // Authentication errors\n                if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\") || message.includes(\"Token has expired\") || message.includes(\"Invalid token\")) {\n                    if (onAuthError) {\n                        onAuthError(gqlError);\n                    } else {\n                        console.warn(\"Authentication error:\", message);\n                        // Clear invalid token\n                        if (true) {\n                            localStorage.removeItem(\"jwtToken\");\n                            window.location.href = \"/login?login=false&reason=auth_error\";\n                        }\n                    }\n                } else if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\") || message.includes(\"Required role\")) {\n                    if (onForbiddenError) {\n                        onForbiddenError(gqlError);\n                    } else {\n                        console.warn(\"Authorization error:\", message);\n                    }\n                } else {\n                    if (onOtherError) {\n                        onOtherError(gqlError);\n                    } else {\n                        console.error(\"GraphQL error:\", message);\n                    }\n                }\n            });\n        }\n        // Handle network errors\n        if (error.networkError) {\n            const { statusCode } = error.networkError;\n            if (statusCode === 401) {\n                if (onAuthError) {\n                    onAuthError(error.networkError);\n                } else {\n                    console.warn(\"Network authentication error\");\n                    if (true) {\n                        localStorage.removeItem(\"jwtToken\");\n                        window.location.href = \"/login?login=false&reason=network_auth\";\n                    }\n                }\n            } else if (statusCode === 403) {\n                if (onForbiddenError) {\n                    onForbiddenError(error.networkError);\n                } else {\n                    console.warn(\"Network authorization error\");\n                }\n            } else {\n                if (onOtherError) {\n                    onOtherError(error.networkError);\n                } else {\n                    console.error(\"Network error:\", error.networkError);\n                }\n            }\n        }\n    };\n};\n/**\r\n * Utility function to check if an error is an authentication error\r\n * @param {Object} error - Apollo error object\r\n * @returns {boolean} Whether the error is an authentication error\r\n */ const isAuthError = (error)=>{\n    if (!error) return false;\n    // Check GraphQL errors\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        return error.graphQLErrors.some((gqlError)=>{\n            const { message, extensions } = gqlError;\n            return (extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\") || message.includes(\"Token has expired\") || message.includes(\"Invalid token\");\n        });\n    }\n    // Check network errors\n    if (error.networkError && error.networkError.statusCode === 401) {\n        return true;\n    }\n    return false;\n};\n/**\r\n * Utility function to check if an error is an authorization error\r\n * @param {Object} error - Apollo error object\r\n * @returns {boolean} Whether the error is an authorization error\r\n */ const isForbiddenError = (error)=>{\n    if (!error) return false;\n    // Check GraphQL errors\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        return error.graphQLErrors.some((gqlError)=>{\n            const { message, extensions } = gqlError;\n            return (extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\") || message.includes(\"Required role\");\n        });\n    }\n    // Check network errors\n    if (error.networkError && error.networkError.statusCode === 403) {\n        return true;\n    }\n    return false;\n};\n/**\r\n * Extracts user-friendly error message from GraphQL error\r\n * @param {Object} error - Apollo error object\r\n * @returns {string} User-friendly error message\r\n */ const getErrorMessage = (error)=>{\n    if (!error) return \"An unknown error occurred\";\n    // Check for GraphQL errors first\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        const gqlError = error.graphQLErrors[0];\n        // Return custom messages for common errors\n        if (isAuthError(error)) {\n            return \"Authentication required. Please log in again.\";\n        }\n        if (isForbiddenError(error)) {\n            return \"Access denied. You do not have permission to perform this action.\";\n        }\n        return gqlError.message || \"A GraphQL error occurred\";\n    }\n    // Check for network errors\n    if (error.networkError) {\n        if (error.networkError.statusCode === 401) {\n            return \"Authentication required. Please log in again.\";\n        }\n        if (error.networkError.statusCode === 403) {\n            return \"Access denied. You do not have permission to perform this action.\";\n        }\n        return error.networkError.message || \"A network error occurred\";\n    }\n    return error.message || \"An error occurred\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/graphqlAuth.js\n"));

/***/ })

});