
/*
 * -------------------------------------------------------
 * THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
 * -------------------------------------------------------
 */

/* tslint:disable */
/* eslint-disable */

export interface UpdateBookingInput {
    title?: Nullable<string>;
    details?: Nullable<string>;
    uid?: Nullable<string>;
    registrationDoc?: Nullable<string>;
    location?: Nullable<string>;
    userId?: Nullable<string>;
    teaBoy?: Nullable<string>;
    parking?: Nullable<string>;
    itTechnician?: Nullable<string>;
    start?: Nullable<DateTime>;
    end?: Nullable<DateTime>;
}

export interface CreateResourceInput {
    name: string;
    type: string;
}

export interface UpdateResourceInput {
    name?: Nullable<string>;
    type?: Nullable<string>;
    id: string;
}

export interface CreateEventInput {
    title: string;
    details: string;
    category: string;
    status: boolean;
    date: DateTime;
}

export interface UpdateEventInput {
    title?: Nullable<string>;
    details?: Nullable<string>;
    category?: Nullable<string>;
    status?: Nullable<boolean>;
    date?: Nullable<DateTime>;
}

export interface CreateQuoteInput {
    quote: string;
    author: string;
    status: boolean;
    visibilityStart: string;
    visibilityEnd: string;
}

export interface UpdateQuoteInput {
    quote?: Nullable<string>;
    author?: Nullable<string>;
    status?: Nullable<boolean>;
    visibilityStart?: Nullable<string>;
    visibilityEnd?: Nullable<string>;
}

export interface CreateNotificationInput {
    notification: string;
}

export interface UpdateNotificationInput {
    notification: string;
}

export interface CreateLeaveInput {
    userId: string;
    username: string;
    remarks: string;
    date: DateTime;
    numberOfDays: number;
    typeOfLeave: string;
}

export interface UpdateLeaveInput {
    userId?: Nullable<string>;
    username?: Nullable<string>;
    remarks?: Nullable<string>;
    date?: Nullable<DateTime>;
    numberOfDays?: Nullable<number>;
    typeOfLeave?: Nullable<string>;
    id: string;
}

export interface CreateOffersInput {
    name: string;
    contact_information: string;
    code: string;
    expiry_date: DateTime;
    description?: Nullable<string>;
    status?: Nullable<boolean>;
    created_by?: Nullable<string>;
    updated_by?: Nullable<string>;
}

export interface UpdateOffersInput {
    name?: Nullable<string>;
    contact_information?: Nullable<string>;
    code?: Nullable<string>;
    expiry_date?: Nullable<DateTime>;
    description?: Nullable<string>;
    status?: Nullable<boolean>;
    created_by?: Nullable<string>;
    updated_by?: Nullable<string>;
}

export interface UserSchema {
    id: string;
    profile?: Nullable<string>;
    email?: Nullable<string>;
    name?: Nullable<string>;
    name_arabic?: Nullable<string>;
    designation?: Nullable<string>;
    designation_arabic?: Nullable<string>;
    department?: Nullable<string>;
    department_arabic?: Nullable<string>;
    bio_link?: Nullable<string>;
    new_joiner?: Nullable<string>;
    status: string;
    is_cultural_ambassador?: Nullable<string>;
    dn?: Nullable<string>;
    gender?: Nullable<string>;
    account_expires?: Nullable<string>;
    user_principal_name?: Nullable<string>;
    role: string;
    monthly_rating: number;
    yearly_rating: number;
    createdAt?: Nullable<DateTime>;
    updatedAt?: Nullable<DateTime>;
    activity?: Nullable<string>;
    extesion?: Nullable<string>;
}

export interface LoginUser {
    id: string;
    username: string;
    role: string;
    token: string;
}

export interface UserMeta {
    totalCount: number;
    currentPage: number;
    pageSize: number;
    totalPages: number;
}

export interface UserPaginationSchema {
    users: UserSchema[];
    meta: UserMeta;
}

export interface BookingSchema {
    id: string;
    title: string;
    details?: Nullable<string>;
    uid: string;
    registrationDoc?: Nullable<string>;
    location?: Nullable<string>;
    userId: string;
    start: DateTime;
    end: DateTime;
    teaBoy: string;
    parking: string;
    itTechnician: string;
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface Event {
    id: string;
    title: string;
    details: string;
    category: string;
    status: boolean;
    date: DateTime;
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface ResourceSchema {
    id: string;
    name: string;
    type: string;
}

export interface QuoteSchema {
    id?: Nullable<string>;
    quote: string;
    author: string;
    status: boolean;
    visibilityStart: string;
    visibilityEnd: string;
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface NotificationSchema {
    id: string;
    notification: string;
    createdAt: DateTime;
    views?: Nullable<UserSchema[]>;
}

export interface Leave {
    id: string;
    userid: string;
    username: string;
    remarks: string;
    date: DateTime;
    numberOfDays: number;
    typeOfLeave: string;
    createdAt: DateTime;
    updatedAt: DateTime;
}

export interface LeaveCount {
    medical: number;
    casual: number;
}

export interface OffersSchema {
    id?: Nullable<string>;
    name: string;
    contact_information?: Nullable<string>;
    code: string;
    expiry_date?: Nullable<DateTime>;
    description?: Nullable<string>;
    status?: Nullable<boolean>;
    is_read?: Nullable<boolean>;
    createdAt?: Nullable<DateTime>;
    created_by?: Nullable<string>;
    updatedAt?: Nullable<DateTime>;
    updated_by?: Nullable<string>;
}

export interface IQuery {
    users(page: number, pageSize: number): UserPaginationSchema | Promise<UserPaginationSchema>;
    getNewUsers(days: number): UserSchema[] | Promise<UserSchema[]>;
    getCulturalAmbassadors(): UserSchema[] | Promise<UserSchema[]>;
    loginUser(username: string, password: string): LoginUser | Promise<LoginUser>;
    findUserById(id: string): UserSchema | Promise<UserSchema>;
    bookings(): BookingSchema[] | Promise<BookingSchema[]>;
    bookingsOfTeaBoy(): BookingSchema[] | Promise<BookingSchema[]>;
    bookingsOfUser(id: string): BookingSchema[] | Promise<BookingSchema[]>;
    allResources(): ResourceSchema[] | Promise<ResourceSchema[]>;
    singleResource(id: string): ResourceSchema | Promise<ResourceSchema>;
    events(): Event[] | Promise<Event[]>;
    event(id: string): Event | Promise<Event>;
    todaysEvents(date: DateTime, category: string): Event[] | Promise<Event[]>;
    findAllQuote(): QuoteSchema[] | Promise<QuoteSchema[]>;
    findOneQuote(id: string): QuoteSchema | Promise<QuoteSchema>;
    todaysQuote(): QuoteSchema | Promise<QuoteSchema>;
    notifications(): NotificationSchema[] | Promise<NotificationSchema[]>;
    notificationViews(): NotificationSchema[] | Promise<NotificationSchema[]>;
    newNotificationsForUser(id: string): NotificationSchema[] | Promise<NotificationSchema[]>;
    unseenNotificationsCount(userId: string): string | Promise<string>;
    notification(id: string): NotificationSchema | Promise<NotificationSchema>;
    leaves(): Leave[] | Promise<Leave[]>;
    getUserLeaves(id: string): LeaveCount | Promise<LeaveCount>;
    leave(id: string): Leave | Promise<Leave>;
    offers(user_id: string): OffersSchema[] | Promise<OffersSchema[]>;
    validOffers(user_id: string): OffersSchema[] | Promise<OffersSchema[]>;
}

export interface IMutation {
    createBooking(CreateBookingInput: UpdateBookingInput): BookingSchema | Promise<BookingSchema>;
    createResource(createResourceInput: CreateResourceInput): ResourceSchema | Promise<ResourceSchema>;
    updateResource(updateResourceInput: UpdateResourceInput): ResourceSchema | Promise<ResourceSchema>;
    removeResource(id: string): ResourceSchema | Promise<ResourceSchema>;
    createEvent(createEventInput: CreateEventInput): Event | Promise<Event>;
    updateEvent(id: string, updateEventInput: UpdateEventInput): Event | Promise<Event>;
    removeEvent(id: string): Event | Promise<Event>;
    createQuote(createQuoteInput: CreateQuoteInput): QuoteSchema | Promise<QuoteSchema>;
    updateQuote(id: string, updateQuoteInput: UpdateQuoteInput): QuoteSchema | Promise<QuoteSchema>;
    removeQuote(id: string): QuoteSchema | Promise<QuoteSchema>;
    createNotification(createNotificationInput: CreateNotificationInput): NotificationSchema | Promise<NotificationSchema>;
    addNotificationView(notificationId: string, userId: string): NotificationSchema | Promise<NotificationSchema>;
    markAllNotificationsAsSeen(userId: string): boolean | Promise<boolean>;
    updateNotification(id: string, updateNotificationInput: UpdateNotificationInput): NotificationSchema | Promise<NotificationSchema>;
    removeNotification(id: string): NotificationSchema | Promise<NotificationSchema>;
    createLeave(createLeaveInput: CreateLeaveInput): Leave | Promise<Leave>;
    updateLeave(updateLeaveInput: UpdateLeaveInput): Leave | Promise<Leave>;
    removeLeave(id: string): Leave | Promise<Leave>;
    createOffer(createOffersInput: CreateOffersInput): OffersSchema | Promise<OffersSchema>;
    offerView(offer_id: string, user_id: string): OffersSchema | Promise<OffersSchema>;
    updateOffer(id: string, updateOffersInput: UpdateOffersInput): OffersSchema | Promise<OffersSchema>;
    removeOffer(id: string): OffersSchema | Promise<OffersSchema>;
}

export type DateTime = any;
type Nullable<T> = T | null;
