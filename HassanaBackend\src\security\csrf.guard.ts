import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { Request } from 'express';
import * as crypto from 'crypto';

@Injectable()
export class CsrfGuard implements CanActivate {
  private readonly csrfSecret = process.env.CSRF_SECRET || 'default-csrf-secret-change-in-production';

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context).getContext();
    const request: Request = ctx.req;

    // Skip CSRF check for GET requests (queries that don't modify data)
    if (request.method === 'GET') {
      return true;
    }

    // Skip CSRF check for introspection queries in development
    if (process.env.NODE_ENV !== 'production' && this.isIntrospectionQuery(request)) {
      return true;
    }

    // Check for CSRF token in headers
    const csrfToken = request.headers['x-csrf-token'] as string;
    const sessionToken = request.headers['x-session-token'] as string;

    if (!csrfToken) {
      throw new BadRequestException('CSRF token is required');
    }

    if (!sessionToken) {
      throw new BadRequestException('Session token is required');
    }

    // Verify CSRF token
    if (!this.verifyCsrfToken(csrfToken, sessionToken)) {
      throw new ForbiddenException('Invalid CSRF token');
    }

    return true;
  }

  private isIntrospectionQuery(request: Request): boolean {
    const body = request.body;
    if (body && body.query) {
      return body.query.includes('__schema') || body.query.includes('__type');
    }
    return false;
  }

  private verifyCsrfToken(token: string, sessionToken: string): boolean {
    try {
      // Generate expected token based on session
      const expectedToken = this.generateCsrfToken(sessionToken);
      return crypto.timingSafeEqual(
        Buffer.from(token, 'hex'),
        Buffer.from(expectedToken, 'hex')
      );
    } catch (error) {
      return false;
    }
  }

  generateCsrfToken(sessionToken: string): string {
    const hmac = crypto.createHmac('sha256', this.csrfSecret);
    hmac.update(sessionToken);
    return hmac.digest('hex');
  }
}
