"use client";

import { <PERSON>, But<PERSON>, Divider, Typography } from "@mui/material";
import React, { useState, useEffect, useRef } from "react";
import "@fontsource/urbanist";
import "@fontsource/inter";

const SlideImageCard = () => {
  const [showMore, setShowMore] = useState(false);
  const scrollRef = useRef(null);

  const images = [
    "/Ellipse 80.png",
    "/Ellipse 81.png",
    "/Ellipse 82.png",
    "/Ellipse 84.png",
    "/Ellipse 86.png",
    "/Ellipse 82.png",
    "/Ellipse 80.png",
    "/Ellipse 81.png",
  ];

  useEffect(() => {
    if (showMore && scrollRef.current) {
      const scrollContainer = scrollRef.current;
      const interval = setInterval(() => {
        scrollContainer.scrollLeft += 1;
        if (
          scrollContainer.scrollLeft + scrollContainer.clientWidth >=
          scrollContainer.scrollWidth
        ) {
          scrollContainer.scrollLeft = 0;
        }
      }, 30);

      return () => clearInterval(interval);
    }
  }, [showMore]);

  const handleShowMore = () => {
    setShowMore(true);
  };

  const visibleImages = showMore ? images : images.slice(0, 5);

  return (
    <Box
      sx={{
        width: "100%",
        maxWidth: "392px",
        height: "171px",
        backgroundColor: "white",
        boxShadow: "0 8px 15px rgba(0, 0, 0, 0.1)",
        px: "26px",
        py: "18px",
        borderRadius: "10px",
      }}
    >
      <Box
        sx={{
          maxWidth: "340px",
          height: "134px",
        }}
      >
        <Typography
          sx={{
            mb: "13px",
            fontFamily: "Urbanist",
            fontSize: "20px",
            fontWeight: 800,
            color: "#1b3745",
            lineHeight: 0.7,
          }}
        >
          Welcome Hassana New Joiners
        </Typography>
        <Typography
          sx={{
            fontFamily: "Inter",
            fontWeight: 400,
            fontSize: "12px",
            color: "#b0b0b0",
          }}
        >
          Welcome Hassana and our new team members! We`&apos;re excited to start this
          journey together
        </Typography>

        <Divider
          sx={{
            width: "90%",
            borderBottomStyle: "dashed",
            borderColor: "#1B37450F",
            borderBottomWidth: "1.5px",
            my: 2,
          }}
        />

        <Box
          ref={scrollRef}
          sx={{
            display: "flex",
            gap: 1,
            width: "285px",
            overflowX: "auto",
            whiteSpace: "nowrap",
            scrollbarWidth: "none",
            "&::-webkit-scrollbar": {
              display: "none",
            },
          }}
        >
          {visibleImages.map((src, index) => (
            <Box
              key={index}
              component="img"
              src={src}
              alt={`user-${index}`}
              sx={{
                width: "40px",
                height: "40px",
                borderRadius: "50%",
                flexShrink: 0,
              }}
            />
          ))}

          {!showMore && (
            <Button
              sx={{
                minWidth: "40px",
                height: "40px",
                borderRadius: "50%",
                boxShadow: "0 3px 5px rgba(0, 0, 0, 0.1)",
                color: "#1b3745",
                backgroundColor: "white",
                padding: 0,
                textAlign: "center",
              }}
              onClick={handleShowMore}
            >
              +{images.length - 5}
            </Button>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default SlideImageCard;
