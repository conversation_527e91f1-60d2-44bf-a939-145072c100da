import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as dotenv from 'dotenv';
import * as path from 'path';
import * as helmet from 'helmet';
import * as cookieParser from 'cookie-parser';
import * as session from 'express-session';
import { AppModule } from './app.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { SecurityConfig } from './security/security.config';

dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  app.useLogger(new Logger());

  // Security middleware
  app.use(helmet(SecurityConfig.getHelmetConfig()));
  app.use(cookieParser());
  app.use(session(SecurityConfig.getSessionConfig()));

  // Static assets
  app.useStaticAssets(path.join(__dirname, '../../resource'));
  app.useStaticAssets(path.join(__dirname, '../../library'));

  // Enhanced validation with security config
  app.useGlobalPipes(
    new ValidationPipe(SecurityConfig.getValidationConfig()),
  );
  const config = new DocumentBuilder()
    .setTitle('Hassana APIs Document')
    // .setDescription('The cats API description')
    .setVersion('1.0')
    // .addTag('cats')
    .build();
  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('/v1/docs', app, documentFactory);


  // Enable CORS with security configuration
  app.enableCors(SecurityConfig.getCorsConfig());

  await app.listen(process.env.SERVER_PORT, () => {
    Logger.log("Server is running on port: " + process.env.SERVER_PORT)
  });
}

bootstrap();
