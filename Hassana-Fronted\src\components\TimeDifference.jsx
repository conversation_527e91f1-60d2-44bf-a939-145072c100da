import React from "react";
import Typography from "@mui/material/Typography";

const TimeDifference = ({ startTime, endTime }) => {
  const calculateTimeDifference = () => {
    const start = new Date(startTime);
    const end = new Date(endTime);

    const timeDiffInMilliseconds = end - start;
    const timeDiffInMinutes = Math.floor(timeDiffInMilliseconds / (1000 * 60));

    return timeDiffInMinutes;
  };

  const timeDifference = calculateTimeDifference();

  return (
    <Typography variant="body2" color="textSecondary">
      {`${timeDifference} min`}
    </Typography>
  );
};

export default TimeDifference;