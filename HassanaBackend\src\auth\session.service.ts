import { Injectable, Logger } from '@nestjs/common';
import { redis } from '../../redis';
import * as jwt from 'jsonwebtoken';

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);
  private readonly redis = redis;

  /**
   * Create a new session for user and invalidate previous sessions
   * @param userId - User ID
   * @param tokenPayload - JWT payload
   * @param deviceInfo - Device/browser information
   * @returns JWT token
   */
  async createUserSession(userId: string, tokenPayload: any, deviceInfo?: any): Promise<string> {
    const JWT_KEY = process.env.JWT_KEY;
    const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

    if (!JWT_KEY) {
      throw new Error('JWT secret key not configured');
    }

    // Generate new session ID
    const sessionId = this.generateSessionId();
    
    // Add session ID to token payload
    const enhancedPayload = {
      ...tokenPayload,
      sessionId,
      loginTime: new Date().toISOString(),
      deviceInfo: deviceInfo || 'Unknown'
    };

    // Generate JWT token
    const token = jwt.sign(enhancedPayload, JWT_KEY, { expiresIn: JWT_EXPIRES_IN });

    // Store session in Redis
    const sessionKey = `user_session:${userId}`;
    const sessionData = {
      sessionId,
      token,
      userId,
      loginTime: enhancedPayload.loginTime,
      deviceInfo: enhancedPayload.deviceInfo,
      isActive: 'true'
    };

    // Check if user already has an active session
    const existingSession = await this.redis.hgetall(sessionKey);
    if (existingSession && existingSession.sessionId) {
      this.logger.warn(`User ${userId} already has an active session. Invalidating previous session.`);
      
      // Add previous session to blacklist
      await this.blacklistToken(existingSession.token);
    }

    // Store new session (this will overwrite any existing session)
    await this.redis.hmset(sessionKey, sessionData);
    
    // Set expiry for session (24 hours)
    await this.redis.expire(sessionKey, 24 * 60 * 60);

    this.logger.log(`New session created for user ${userId} with session ID: ${sessionId}`);
    
    return token;
  }

  /**
   * Validate if a session is active and valid
   * @param userId - User ID
   * @param sessionId - Session ID from JWT
   * @param token - JWT token
   * @returns boolean indicating if session is valid
   */
  async validateUserSession(userId: string, sessionId: string, token: string): Promise<boolean> {
    try {
      // Check if token is blacklisted
      const isBlacklisted = await this.isTokenBlacklisted(token);
      if (isBlacklisted) {
        this.logger.warn(`Blacklisted token used for user ${userId}`);
        return false;
      }

      // Get current active session
      const sessionKey = `user_session:${userId}`;
      const currentSession = await this.redis.hgetall(sessionKey);

      if (!currentSession || !currentSession.sessionId) {
        this.logger.warn(`No active session found for user ${userId}`);
        return false;
      }

      // Check if session IDs match
      if (currentSession.sessionId !== sessionId) {
        this.logger.warn(`Session ID mismatch for user ${userId}. Current: ${currentSession.sessionId}, Provided: ${sessionId}`);
        
        // Blacklist the old token
        await this.blacklistToken(token);
        return false;
      }

      // Check if session is marked as active
      if (currentSession.isActive !== 'true') {
        this.logger.warn(`Inactive session for user ${userId}`);
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Error validating session for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Logout user and invalidate session
   * @param userId - User ID
   * @param token - JWT token to blacklist
   */
  async logoutUser(userId: string, token: string): Promise<void> {
    try {
      // Remove user session
      const sessionKey = `user_session:${userId}`;
      await this.redis.del(sessionKey);

      // Blacklist the token
      await this.blacklistToken(token);

      this.logger.log(`User ${userId} logged out successfully`);
    } catch (error) {
      this.logger.error(`Error during logout for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Get active session info for user
   * @param userId - User ID
   * @returns Session information
   */
  async getUserSession(userId: string): Promise<any> {
    const sessionKey = `user_session:${userId}`;
    const session = await this.redis.hgetall(sessionKey);
    
    if (!session || !session.sessionId) {
      return null;
    }

    return {
      sessionId: session.sessionId,
      loginTime: session.loginTime,
      deviceInfo: session.deviceInfo,
      isActive: session.isActive === 'true'
    };
  }

  /**
   * Force logout user from all devices
   * @param userId - User ID
   */
  async forceLogoutUser(userId: string): Promise<void> {
    try {
      const sessionKey = `user_session:${userId}`;
      const session = await this.redis.hgetall(sessionKey);
      
      if (session && session.token) {
        await this.blacklistToken(session.token);
      }
      
      await this.redis.del(sessionKey);
      
      this.logger.log(`Force logout completed for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error during force logout for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Blacklist a JWT token
   * @param token - JWT token to blacklist
   */
  private async blacklistToken(token: string): Promise<void> {
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.exp) {
        const blacklistKey = `blacklist:${token}`;
        const ttl = decoded.exp - Math.floor(Date.now() / 1000);
        
        if (ttl > 0) {
          await this.redis.setex(blacklistKey, ttl, 'true');
          this.logger.log(`Token blacklisted with TTL: ${ttl} seconds`);
        }
      }
    } catch (error) {
      this.logger.error('Error blacklisting token:', error);
    }
  }

  /**
   * Check if a token is blacklisted
   * @param token - JWT token to check
   * @returns boolean indicating if token is blacklisted
   */
  private async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const blacklistKey = `blacklist:${token}`;
      const result = await this.redis.get(blacklistKey);
      return result === 'true';
    } catch (error) {
      this.logger.error('Error checking blacklist:', error);
      return false;
    }
  }

  /**
   * Generate unique session ID
   * @returns Session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get all active sessions (for admin purposes)
   * @returns Array of active sessions
   */
  async getAllActiveSessions(): Promise<any[]> {
    try {
      const keys = await this.redis.keys('user_session:*');
      const sessions = [];

      for (const key of keys) {
        const session = await this.redis.hgetall(key);
        if (session && session.isActive === 'true') {
          sessions.push({
            userId: key.replace('user_session:', ''),
            ...session
          });
        }
      }

      return sessions;
    } catch (error) {
      this.logger.error('Error getting active sessions:', error);
      return [];
    }
  }
}
