"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/Data/ApolloClient.js":
/*!**********************************!*\
  !*** ./src/Data/ApolloClient.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baseUrl: function() { return /* binding */ baseUrl; }\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _apollo_client_link_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @apollo/client/link/context */ \"./node_modules/@apollo/client/link/context/index.js\");\n/* harmony import */ var _apollo_client_link_error__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client/link/error */ \"./node_modules/@apollo/client/link/error/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n\n\n//export const baseUrl = \"http://*************:3001/v1\";\n//export const baseUrl = \"https://portal.hassana.com.sa/v1\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com/v1\";\nconst baseUrl = \"http://localhost:3001\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com\";\n//export const baseUrl = \"https://v2-portal.hassana.com.sa\";\n//export const base_url = \"https://v2-portal.hassana.com.sa/v1\";\n//export const base_url = \"https://localhost:3001/v1\";\nconst httpLink = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.createHttpLink)({\n    uri: baseUrl + \"/graphql\"\n});\n// Auth link to add JWT token to headers\nconst authLink = (0,_apollo_client_link_context__WEBPACK_IMPORTED_MODULE_2__.setContext)(async (_, param)=>{\n    let { headers } = param;\n    // Get token from session or localStorage\n    let token = null;\n    try {\n        const session = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.getSession)();\n        token = session === null || session === void 0 ? void 0 : session.accessToken;\n    } catch (error) {\n        console.warn(\"Could not get session:\", error);\n    }\n    // Fallback to localStorage if session token not available\n    if (!token && \"object\" !== \"undefined\") {\n        token = localStorage.getItem(\"jwtToken\");\n    }\n    return {\n        headers: {\n            ...headers,\n            ...token && {\n                authorization: \"Bearer \".concat(token)\n            }\n        }\n    };\n});\n// Error link to handle GraphQL and network errors\nconst errorLink = (0,_apollo_client_link_error__WEBPACK_IMPORTED_MODULE_3__.onError)((param)=>{\n    let { graphQLErrors, networkError } = param;\n    if (graphQLErrors) {\n        graphQLErrors.forEach((param)=>{\n            let { message, locations, path, extensions } = param;\n            console.error(\"[GraphQL error]: Message: \".concat(message, \", Location: \").concat(locations, \", Path: \").concat(path));\n            // Handle authentication errors\n            if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\")) {\n                console.warn(\"Authentication error detected, redirecting to login...\");\n                // Clear invalid token\n                if (true) {\n                    localStorage.removeItem(\"jwtToken\");\n                }\n                // Redirect to login page\n                if (true) {\n                    window.location.href = \"/login?login=false\";\n                }\n            }\n            // Handle authorization errors\n            if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\")) {\n                console.warn(\"Authorization error detected\");\n                // You can show a toast or redirect to unauthorized page\n                if (true) {\n                    // You can implement a toast notification here\n                    console.error(\"Access denied: Insufficient permissions\");\n                }\n            }\n        });\n    }\n    if (networkError) {\n        console.error(\"[Network error]: \".concat(networkError));\n        // Handle network errors that might indicate auth issues\n        if (networkError.statusCode === 401) {\n            console.warn(\"Network 401 error, clearing token and redirecting...\");\n            if (true) {\n                localStorage.removeItem(\"jwtToken\");\n                window.location.href = \"/login?login=false\";\n            }\n        }\n    }\n});\nconst client = new _apollo_client__WEBPACK_IMPORTED_MODULE_1__.ApolloClient({\n    link: (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.from)([\n        errorLink,\n        authLink,\n        httpLink\n    ]),\n    cache: new _apollo_client__WEBPACK_IMPORTED_MODULE_1__.InMemoryCache(),\n    defaultOptions: {\n        watchQuery: {\n            errorPolicy: \"all\"\n        },\n        query: {\n            errorPolicy: \"all\"\n        }\n    }\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (client);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvRGF0YS9BcG9sbG9DbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW1GO0FBQzFCO0FBQ0w7QUFDUDtBQUU1Qyx3REFBd0Q7QUFDekQsNERBQTREO0FBQzVELHdFQUF3RTtBQUNqRSxNQUFNTyxVQUFVLHdCQUF3QjtBQUMvQyxxRUFBcUU7QUFDckUsNERBQTREO0FBQzVELGdFQUFnRTtBQUNoRSxzREFBc0Q7QUFFdEQsTUFBTUMsV0FBV04sOERBQWNBLENBQUM7SUFDOUJPLEtBQUtGLFVBQVU7QUFDakI7QUFFQSx3Q0FBd0M7QUFDeEMsTUFBTUcsV0FBV04sdUVBQVVBLENBQUMsT0FBT087UUFBRyxFQUFFQyxPQUFPLEVBQUU7SUFDL0MseUNBQXlDO0lBQ3pDLElBQUlDLFFBQVE7SUFFWixJQUFJO1FBQ0YsTUFBTUMsVUFBVSxNQUFNUiwyREFBVUE7UUFDaENPLFFBQVFDLG9CQUFBQSw4QkFBQUEsUUFBU0MsV0FBVztJQUM5QixFQUFFLE9BQU9DLE9BQU87UUFDZEMsUUFBUUMsSUFBSSxDQUFDLDBCQUEwQkY7SUFDekM7SUFFQSwwREFBMEQ7SUFDMUQsSUFBSSxDQUFDSCxTQUFTLGFBQWtCLGFBQWE7UUFDM0NBLFFBQVFNLGFBQWFDLE9BQU8sQ0FBQztJQUMvQjtJQUVBLE9BQU87UUFDTFIsU0FBUztZQUNQLEdBQUdBLE9BQU87WUFDVixHQUFJQyxTQUFTO2dCQUFFUSxlQUFlLFVBQWdCLE9BQU5SO1lBQVEsQ0FBQztRQUNuRDtJQUNGO0FBQ0Y7QUFFQSxrREFBa0Q7QUFDbEQsTUFBTVMsWUFBWWpCLGtFQUFPQSxDQUFDO1FBQUMsRUFBRWtCLGFBQWEsRUFBRUMsWUFBWSxFQUFFO0lBQ3hELElBQUlELGVBQWU7UUFDakJBLGNBQWNFLE9BQU8sQ0FBQztnQkFBQyxFQUFFQyxPQUFPLEVBQUVDLFNBQVMsRUFBRUMsSUFBSSxFQUFFQyxVQUFVLEVBQUU7WUFDN0RaLFFBQVFELEtBQUssQ0FDWCw2QkFBbURXLE9BQXRCRCxTQUFRLGdCQUFrQ0UsT0FBcEJELFdBQVUsWUFBZSxPQUFMQztZQUd6RSwrQkFBK0I7WUFDL0IsSUFBSUMsQ0FBQUEsdUJBQUFBLGlDQUFBQSxXQUFZQyxJQUFJLE1BQUsscUJBQXFCSixRQUFRSyxRQUFRLENBQUMsbUJBQW1CTCxRQUFRSyxRQUFRLENBQUMsbUNBQW1DO2dCQUNwSWQsUUFBUUMsSUFBSSxDQUFDO2dCQUNiLHNCQUFzQjtnQkFDdEIsSUFBSSxJQUFrQixFQUFhO29CQUNqQ0MsYUFBYWEsVUFBVSxDQUFDO2dCQUMxQjtnQkFDQSx5QkFBeUI7Z0JBQ3pCLElBQUksSUFBa0IsRUFBYTtvQkFDakNDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO2dCQUN6QjtZQUNGO1lBRUEsOEJBQThCO1lBQzlCLElBQUlOLENBQUFBLHVCQUFBQSxpQ0FBQUEsV0FBWUMsSUFBSSxNQUFLLGVBQWVKLFFBQVFLLFFBQVEsQ0FBQyxvQkFBb0JMLFFBQVFLLFFBQVEsQ0FBQyxjQUFjO2dCQUMxR2QsUUFBUUMsSUFBSSxDQUFDO2dCQUNiLHdEQUF3RDtnQkFDeEQsSUFBSSxJQUFrQixFQUFhO29CQUNqQyw4Q0FBOEM7b0JBQzlDRCxRQUFRRCxLQUFLLENBQUM7Z0JBQ2hCO1lBQ0Y7UUFDRjtJQUNGO0lBRUEsSUFBSVEsY0FBYztRQUNoQlAsUUFBUUQsS0FBSyxDQUFDLG9CQUFpQyxPQUFiUTtRQUVsQyx3REFBd0Q7UUFDeEQsSUFBSUEsYUFBYVksVUFBVSxLQUFLLEtBQUs7WUFDbkNuQixRQUFRQyxJQUFJLENBQUM7WUFDYixJQUFJLElBQWtCLEVBQWE7Z0JBQ2pDQyxhQUFhYSxVQUFVLENBQUM7Z0JBQ3hCQyxPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBRztZQUN6QjtRQUNGO0lBQ0Y7QUFDRjtBQUVBLE1BQU1FLFNBQVMsSUFBSXJDLHdEQUFZQSxDQUFDO0lBQzlCc0MsTUFBTW5DLG9EQUFJQSxDQUFDO1FBQ1RtQjtRQUNBWjtRQUNBRjtLQUNEO0lBQ0QrQixPQUFPLElBQUl0Qyx5REFBYUE7SUFDeEJ1QyxnQkFBZ0I7UUFDZEMsWUFBWTtZQUNWQyxhQUFhO1FBQ2Y7UUFDQUMsT0FBTztZQUNMRCxhQUFhO1FBQ2Y7SUFDRjtBQUNGO0FBRUEsK0RBQWVMLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL0RhdGEvQXBvbGxvQ2xpZW50LmpzPzg0YjIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBvbGxvQ2xpZW50LCBJbk1lbW9yeUNhY2hlLCBjcmVhdGVIdHRwTGluaywgZnJvbSB9IGZyb20gXCJAYXBvbGxvL2NsaWVudFwiO1xyXG5pbXBvcnQgeyBzZXRDb250ZXh0IH0gZnJvbSAnQGFwb2xsby9jbGllbnQvbGluay9jb250ZXh0JztcclxuaW1wb3J0IHsgb25FcnJvciB9IGZyb20gJ0BhcG9sbG8vY2xpZW50L2xpbmsvZXJyb3InO1xyXG5pbXBvcnQgeyBnZXRTZXNzaW9uIH0gZnJvbSBcIm5leHQtYXV0aC9yZWFjdFwiO1xyXG5cclxuIC8vZXhwb3J0IGNvbnN0IGJhc2VVcmwgPSBcImh0dHA6Ly8yMTMuMTk5LjM0Ljg0OjMwMDEvdjFcIjtcclxuLy9leHBvcnQgY29uc3QgYmFzZVVybCA9IFwiaHR0cHM6Ly9wb3J0YWwuaGFzc2FuYS5jb20uc2EvdjFcIjtcclxuLy9leHBvcnQgY29uc3QgYmFzZVVybCA9IFwiaHR0cHM6Ly9oYXNzYW5hLWFwaS4zNjB4cGVydHNvbHV0aW9ucy5jb20vdjFcIjtcclxuZXhwb3J0IGNvbnN0IGJhc2VVcmwgPSBcImh0dHA6Ly9sb2NhbGhvc3Q6MzAwMVwiO1xyXG4vL2V4cG9ydCBjb25zdCBiYXNlVXJsID0gXCJodHRwczovL2hhc3NhbmEtYXBpLjM2MHhwZXJ0c29sdXRpb25zLmNvbVwiO1xyXG4vL2V4cG9ydCBjb25zdCBiYXNlVXJsID0gXCJodHRwczovL3YyLXBvcnRhbC5oYXNzYW5hLmNvbS5zYVwiO1xyXG4vL2V4cG9ydCBjb25zdCBiYXNlX3VybCA9IFwiaHR0cHM6Ly92Mi1wb3J0YWwuaGFzc2FuYS5jb20uc2EvdjFcIjtcclxuLy9leHBvcnQgY29uc3QgYmFzZV91cmwgPSBcImh0dHBzOi8vbG9jYWxob3N0OjMwMDEvdjFcIjtcclxuXHJcbmNvbnN0IGh0dHBMaW5rID0gY3JlYXRlSHR0cExpbmsoe1xyXG4gIHVyaTogYmFzZVVybCArIFwiL2dyYXBocWxcIixcclxufSk7XHJcblxyXG4vLyBBdXRoIGxpbmsgdG8gYWRkIEpXVCB0b2tlbiB0byBoZWFkZXJzXHJcbmNvbnN0IGF1dGhMaW5rID0gc2V0Q29udGV4dChhc3luYyAoXywgeyBoZWFkZXJzIH0pID0+IHtcclxuICAvLyBHZXQgdG9rZW4gZnJvbSBzZXNzaW9uIG9yIGxvY2FsU3RvcmFnZVxyXG4gIGxldCB0b2tlbiA9IG51bGw7XHJcblxyXG4gIHRyeSB7XHJcbiAgICBjb25zdCBzZXNzaW9uID0gYXdhaXQgZ2V0U2Vzc2lvbigpO1xyXG4gICAgdG9rZW4gPSBzZXNzaW9uPy5hY2Nlc3NUb2tlbjtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS53YXJuKFwiQ291bGQgbm90IGdldCBzZXNzaW9uOlwiLCBlcnJvcik7XHJcbiAgfVxyXG5cclxuICAvLyBGYWxsYmFjayB0byBsb2NhbFN0b3JhZ2UgaWYgc2Vzc2lvbiB0b2tlbiBub3QgYXZhaWxhYmxlXHJcbiAgaWYgKCF0b2tlbiAmJiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImp3dFRva2VuXCIpO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHtcclxuICAgIGhlYWRlcnM6IHtcclxuICAgICAgLi4uaGVhZGVycyxcclxuICAgICAgLi4uKHRva2VuICYmIHsgYXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAgfSksXHJcbiAgICB9XHJcbiAgfTtcclxufSk7XHJcblxyXG4vLyBFcnJvciBsaW5rIHRvIGhhbmRsZSBHcmFwaFFMIGFuZCBuZXR3b3JrIGVycm9yc1xyXG5jb25zdCBlcnJvckxpbmsgPSBvbkVycm9yKCh7IGdyYXBoUUxFcnJvcnMsIG5ldHdvcmtFcnJvciB9KSA9PiB7XHJcbiAgaWYgKGdyYXBoUUxFcnJvcnMpIHtcclxuICAgIGdyYXBoUUxFcnJvcnMuZm9yRWFjaCgoeyBtZXNzYWdlLCBsb2NhdGlvbnMsIHBhdGgsIGV4dGVuc2lvbnMgfSkgPT4ge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFxyXG4gICAgICAgIGBbR3JhcGhRTCBlcnJvcl06IE1lc3NhZ2U6ICR7bWVzc2FnZX0sIExvY2F0aW9uOiAke2xvY2F0aW9uc30sIFBhdGg6ICR7cGF0aH1gXHJcbiAgICAgICk7XHJcblxyXG4gICAgICAvLyBIYW5kbGUgYXV0aGVudGljYXRpb24gZXJyb3JzXHJcbiAgICAgIGlmIChleHRlbnNpb25zPy5jb2RlID09PSAnVU5BVVRIRU5USUNBVEVEJyB8fCBtZXNzYWdlLmluY2x1ZGVzKCdVbmF1dGhvcml6ZWQnKSB8fCBtZXNzYWdlLmluY2x1ZGVzKCdBdXRob3JpemF0aW9uIGhlYWRlciBub3QgZm91bmQnKSkge1xyXG4gICAgICAgIGNvbnNvbGUud2FybignQXV0aGVudGljYXRpb24gZXJyb3IgZGV0ZWN0ZWQsIHJlZGlyZWN0aW5nIHRvIGxvZ2luLi4uJyk7XHJcbiAgICAgICAgLy8gQ2xlYXIgaW52YWxpZCB0b2tlblxyXG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oXCJqd3RUb2tlblwiKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgLy8gUmVkaXJlY3QgdG8gbG9naW4gcGFnZVxyXG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSAnL2xvZ2luP2xvZ2luPWZhbHNlJztcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC8vIEhhbmRsZSBhdXRob3JpemF0aW9uIGVycm9yc1xyXG4gICAgICBpZiAoZXh0ZW5zaW9ucz8uY29kZSA9PT0gJ0ZPUkJJRERFTicgfHwgbWVzc2FnZS5pbmNsdWRlcygnQWNjZXNzIGRlbmllZCcpIHx8IG1lc3NhZ2UuaW5jbHVkZXMoJ0ZvcmJpZGRlbicpKSB7XHJcbiAgICAgICAgY29uc29sZS53YXJuKCdBdXRob3JpemF0aW9uIGVycm9yIGRldGVjdGVkJyk7XHJcbiAgICAgICAgLy8gWW91IGNhbiBzaG93IGEgdG9hc3Qgb3IgcmVkaXJlY3QgdG8gdW5hdXRob3JpemVkIHBhZ2VcclxuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcclxuICAgICAgICAgIC8vIFlvdSBjYW4gaW1wbGVtZW50IGEgdG9hc3Qgbm90aWZpY2F0aW9uIGhlcmVcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0FjY2VzcyBkZW5pZWQ6IEluc3VmZmljaWVudCBwZXJtaXNzaW9ucycpO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfSk7XHJcbiAgfVxyXG5cclxuICBpZiAobmV0d29ya0Vycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKGBbTmV0d29yayBlcnJvcl06ICR7bmV0d29ya0Vycm9yfWApO1xyXG5cclxuICAgIC8vIEhhbmRsZSBuZXR3b3JrIGVycm9ycyB0aGF0IG1pZ2h0IGluZGljYXRlIGF1dGggaXNzdWVzXHJcbiAgICBpZiAobmV0d29ya0Vycm9yLnN0YXR1c0NvZGUgPT09IDQwMSkge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ05ldHdvcmsgNDAxIGVycm9yLCBjbGVhcmluZyB0b2tlbiBhbmQgcmVkaXJlY3RpbmcuLi4nKTtcclxuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oXCJqd3RUb2tlblwiKTtcclxuICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4/bG9naW49ZmFsc2UnO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59KTtcclxuXHJcbmNvbnN0IGNsaWVudCA9IG5ldyBBcG9sbG9DbGllbnQoe1xyXG4gIGxpbms6IGZyb20oW1xyXG4gICAgZXJyb3JMaW5rLFxyXG4gICAgYXV0aExpbmssXHJcbiAgICBodHRwTGlua1xyXG4gIF0pLFxyXG4gIGNhY2hlOiBuZXcgSW5NZW1vcnlDYWNoZSgpLFxyXG4gIGRlZmF1bHRPcHRpb25zOiB7XHJcbiAgICB3YXRjaFF1ZXJ5OiB7XHJcbiAgICAgIGVycm9yUG9saWN5OiAnYWxsJ1xyXG4gICAgfSxcclxuICAgIHF1ZXJ5OiB7XHJcbiAgICAgIGVycm9yUG9saWN5OiAnYWxsJ1xyXG4gICAgfVxyXG4gIH1cclxufSk7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBjbGllbnQ7XHJcbiJdLCJuYW1lcyI6WyJBcG9sbG9DbGllbnQiLCJJbk1lbW9yeUNhY2hlIiwiY3JlYXRlSHR0cExpbmsiLCJmcm9tIiwic2V0Q29udGV4dCIsIm9uRXJyb3IiLCJnZXRTZXNzaW9uIiwiYmFzZVVybCIsImh0dHBMaW5rIiwidXJpIiwiYXV0aExpbmsiLCJfIiwiaGVhZGVycyIsInRva2VuIiwic2Vzc2lvbiIsImFjY2Vzc1Rva2VuIiwiZXJyb3IiLCJjb25zb2xlIiwid2FybiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJhdXRob3JpemF0aW9uIiwiZXJyb3JMaW5rIiwiZ3JhcGhRTEVycm9ycyIsIm5ldHdvcmtFcnJvciIsImZvckVhY2giLCJtZXNzYWdlIiwibG9jYXRpb25zIiwicGF0aCIsImV4dGVuc2lvbnMiLCJjb2RlIiwiaW5jbHVkZXMiLCJyZW1vdmVJdGVtIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwic3RhdHVzQ29kZSIsImNsaWVudCIsImxpbmsiLCJjYWNoZSIsImRlZmF1bHRPcHRpb25zIiwid2F0Y2hRdWVyeSIsImVycm9yUG9saWN5IiwicXVlcnkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/Data/ApolloClient.js\n"));

/***/ })

});