# Security Implementation - CSRF Protection & API Hardening

## Overview

This document outlines the comprehensive security measures implemented to address CSRF token leakage and enhance overall API security.

## Security Issues Addressed

### 1. CSRF Token Leakage
- **Problem**: GraphQL API endpoint was vulnerable to CSRF attacks
- **Solution**: Implemented custom CSRF protection with token validation

### 2. Permissive CORS Configuration
- **Problem**: CORS was set to allow all origins (`*`)
- **Solution**: Restricted CORS to specific allowed origins

### 3. Missing Security Headers
- **Problem**: No security headers were configured
- **Solution**: Added Helmet.js with comprehensive security headers

### 4. GraphQL Security
- **Problem**: Introspection and playground enabled in production
- **Solution**: Disabled in production, added query validation

## Implementation Details

### Backend Security Features

#### 1. CSRF Protection (`src/security/csrf.guard.ts`)
```typescript
@Injectable()
export class CsrfGuard implements CanActivate {
  // Validates CSRF tokens for all POST/mutation requests
  // Skips validation for GET requests and introspection queries
  // Uses HMAC-SHA256 for token generation and validation
}
```

#### 2. Security Configuration (`src/security/security.config.ts`)
- **CORS**: Restricted to specific domains
- **Helmet**: Security headers configuration
- **Rate Limiting**: Request throttling
- **Session Management**: Secure session configuration

#### 3. CSRF Service (`src/security/csrf.service.ts`)
- Token generation using HMAC-SHA256
- Token validation with timing-safe comparison
- Session token management

#### 4. CSRF Controller (`src/security/csrf.controller.ts`)
- `/security/csrf-token` - Get CSRF token
- `/security/verify-csrf` - Verify CSRF token (testing)

### Frontend Security Features

#### 1. CSRF Utilities (`src/utils/csrfUtils.js`)
- Automatic token fetching and management
- Token persistence in localStorage
- Token expiry handling
- Secure headers generation

#### 2. Enhanced GraphQL Authentication (`src/utils/graphqlAuth.js`)
- Integrated CSRF protection with existing JWT authentication
- Automatic CSRF header injection
- Backward compatibility with sync operations

#### 3. CSRF Provider (`src/components/security/CsrfProvider.jsx`)
- React context for CSRF token management
- Automatic token initialization
- Token refresh capabilities
- HOC for component protection

## Security Headers Implemented

### Helmet.js Configuration
- **Content Security Policy**: Prevents XSS attacks
- **HSTS**: Forces HTTPS connections
- **X-Frame-Options**: Prevents clickjacking
- **X-Content-Type-Options**: Prevents MIME sniffing
- **Referrer Policy**: Controls referrer information
- **XSS Protection**: Browser XSS filtering

## CORS Configuration

### Allowed Origins
```javascript
const allowedOrigins = [
  'http://localhost:3000',
  'http://localhost:3001',
  'https://portal.hassana.com.sa',
  'https://v2-portal.hassana.com.sa'
];
```

### Security Features
- Origin validation
- Credentials support
- Specific allowed headers including CSRF tokens
- Preflight handling

## Rate Limiting

### Configuration
- **Development**: 1000 requests per minute
- **Production**: 100 requests per minute
- **TTL**: 60 seconds
- **Skip**: Health check endpoints

## GraphQL Security

### Production Hardening
- Introspection disabled
- Playground disabled
- Query complexity limiting (configurable)
- Context security with CSRF token validation

## Usage Examples

### Backend - Protecting Resolvers
```typescript
@UseGuards(JwtGuard, CsrfGuard)
@Mutation(() => String)
secureOperation() {
  return 'This operation is protected by JWT and CSRF';
}
```

### Frontend - Using CSRF Protection
```javascript
import { createAuthOptions } from '@/utils/graphqlAuth';
import { useCsrf } from '@/components/security/CsrfProvider';

// In component
const { data } = useQuery(MY_QUERY, await createAuthOptions(session, {
  variables: { id: 1 }
}));
```

## Environment Variables

### Required Environment Variables
```bash
# CSRF Protection
CSRF_SECRET=your-csrf-secret-key-here
SESSION_SECRET=your-session-secret-key-here

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://portal.hassana.com.sa

# JWT Configuration (existing)
JWT_KEY=your-jwt-secret-key
JWT_EXPIRES_IN=24h

# Environment
NODE_ENV=production
```

## Security Best Practices Implemented

### 1. Defense in Depth
- Multiple layers of security (CSRF, JWT, CORS, Headers)
- Input validation and sanitization
- Rate limiting and throttling

### 2. Secure Token Management
- HMAC-based token generation
- Timing-safe token comparison
- Automatic token expiry and refresh

### 3. Secure Headers
- Comprehensive security headers via Helmet.js
- CSP to prevent XSS attacks
- HSTS for HTTPS enforcement

### 4. CORS Hardening
- Specific origin allowlist
- Credential handling
- Preflight request validation

## Testing Security

### CSRF Token Testing
```bash
# Get CSRF token
curl -X GET http://localhost:3001/security/csrf-token

# Verify CSRF token
curl -X POST http://localhost:3001/security/verify-csrf \
  -H "X-CSRF-Token: your-csrf-token" \
  -H "X-Session-Token: your-session-token"
```

### GraphQL Security Testing
```bash
# Test protected mutation (should fail without CSRF)
curl -X POST http://localhost:3001/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{"query":"mutation { secureOperation }"}'

# Test with CSRF protection (should succeed)
curl -X POST http://localhost:3001/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "X-CSRF-Token: your-csrf-token" \
  -H "X-Session-Token: your-session-token" \
  -d '{"query":"mutation { secureOperation }"}'
```

## Migration Guide

### 1. Update Environment Variables
Add required security environment variables to your `.env` file.

### 2. Update Frontend App
Wrap your app with CsrfProvider:
```javascript
import { CsrfProvider } from '@/components/security/CsrfProvider';

function MyApp({ Component, pageProps }) {
  return (
    <SessionProvider session={pageProps.session}>
      <CsrfProvider>
        <ApolloProvider client={client}>
          <Component {...pageProps} />
        </ApolloProvider>
      </CsrfProvider>
    </SessionProvider>
  );
}
```

### 3. Update GraphQL Operations
Use the enhanced `createAuthOptions` for mutations:
```javascript
const [createItem] = useMutation(CREATE_ITEM, 
  await createAuthOptions(session, {
    onCompleted: () => console.log('Success'),
    onError: (error) => console.error('Error:', error)
  })
);
```

## Monitoring and Logging

### Security Events Logged
- CSRF token generation and validation
- CORS violations
- Rate limit violations
- Authentication failures
- GraphQL introspection attempts in production

### Recommended Monitoring
- Monitor CSRF token validation failures
- Track rate limiting violations
- Alert on repeated authentication failures
- Monitor for suspicious GraphQL queries

## Conclusion

This implementation provides comprehensive protection against:
- CSRF attacks
- XSS attacks
- Clickjacking
- MIME sniffing
- Information disclosure
- Rate limiting abuse
- CORS violations

The security measures are designed to be transparent to legitimate users while providing robust protection against various attack vectors.
