import { BaseEntity } from '@app/BaseEntity';
import { UUID } from 'crypto';
export declare class Offers extends BaseEntity {
    name: string;
    contact_information?: string;
    code: string;
    expiry_date?: Date;
    description: string;
    status: boolean;
    created_by: UUID;
    updated_by?: UUID;
}
export declare class OffersViewEntity extends BaseEntity {
    user_id: UUID;
    offer_id: UUID;
}
