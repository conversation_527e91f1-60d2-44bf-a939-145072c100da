import { Repository } from 'typeorm';
import { CreateAnnouncementInput } from './dto/create-announcement.input';
import { UpdateAnnouncementInput } from './dto/update-announcement.input';
import { Announcement, AnnouncementViewEntity } from './entities/announcement.entity';
import { UUID } from 'crypto';
export declare class AnnouncementService {
    private readonly announcementRepository;
    private readonly announcementViewRepository;
    constructor(announcementRepository: Repository<Announcement>, announcementViewRepository: Repository<AnnouncementViewEntity>);
    create(createAnnouncementInput: CreateAnnouncementInput): Promise<Announcement>;
    findAll(userId: UUID): Promise<Announcement[]>;
    findOne(id: UUID): Promise<Announcement>;
    update(id: UUID, updateAnnouncementInput: UpdateAnnouncementInput): Promise<UpdateAnnouncementInput>;
    remove(id: UUID): Promise<Announcement | null>;
    createAnnouncementView(announcementId: UUID, userId: UUID): Promise<void>;
}
