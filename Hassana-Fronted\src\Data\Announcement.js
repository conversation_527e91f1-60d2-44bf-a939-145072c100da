import { gql } from "@apollo/client";
import axios from "axios";
import { baseUrl } from "./ApolloClient";

let base_url = baseUrl;
let announcement = 'v1/our-announcement';



export const createAnnouncement = async (formDataInput, token) => {
  try {
    const data = new FormData(); 
    console.log("formDataInput:", formDataInput);

    if (formDataInput.image instanceof File) {
      data.append('image', formDataInput.image);
    }
    data.append('title', formDataInput.title || '');
    data.append('details', formDataInput.details || '');
    data.append('category', formDataInput.category || '');
    data.append('status', formDataInput.status ? 'true' : 'false');
    data.append('visibility', formDataInput.visibility || new Date().toISOString());
    // data.append('publication', new Date().toISOString());
    // data.append('created_on', new Date().toISOString());
    // data.append('created_by', formDataInput.created_by || 'Admin');
    // data.append('updated_on', new Date().toISOString());
    // data.append('updated_by', formDataInput.updated_by || 'Admin');

    const config = {
      method: 'post',
      url: `${base_url}/${announcement}`,
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      data: data,
    };

    const res = await axios.request(config);
    console.log("createAnnouncement response:", res.data);
    return res.data;
  } catch (error) {
    console.error("createAnnouncement error:", error.response?.data || error.message);
    return { error: error.message };
  }
};

export const getAllAnnouncements = async (token) => {
  try {
    console.log("Token being sent:", token);

    const response = await axios.get(`${base_url}/${announcement}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
    console.log("getAllAnnouncements response:", response.data);
    return response.data;
  } catch (error) {
    console.error("getAllAnnouncements error:", error.response?.data || error.message);
    return { error: error.message };
  }
};

export const updateAnnouncement = async (formDataInput, isImageChanged, token) => {
  try {
    const data = new FormData(); 
    if (isImageChanged && formDataInput.image instanceof File) {
      data.append('image', formDataInput.image);
    }
    data.append('title', formDataInput.title || '');
    data.append('details', formDataInput.details || '');
    data.append('category', formDataInput.category || '');
    data.append('status', formDataInput.status ? 'true' : 'false');
    data.append('visibility', formDataInput.visibility || new Date().toISOString());
    // data.append('updated_on', new Date().toISOString());
    // data.append('updated_by', formDataInput.updated_by || 'Admin');

    const config = {
      method: 'patch',
      url: `${baseUrl}/${announcement}/${formDataInput.id}`,
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      data: data,
    };

    const res = await axios.request(config);
    console.log("updateAnnouncement response:", res.data);
    return res.data;
  } catch (error) {
    console.error("updateAnnouncement error:", error.response?.data || error.message);
    return { error: error.message };
  }
};

export const deleteAnnouncement = async (id, token) => {
  try {
    const config = {
      method: 'delete',
      url: `${base_url}/${announcement}/${id}`,
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    };

    const res = await axios.request(config);
    console.log("deleteAnnouncement response:", res.data);
    return res.data;
  } catch (error) {
    console.error("deleteAnnouncement error:", error.response?.data || error.message);
    return { error: error.message };
  }
};

export const mutationCreateAnnouncement = gql`
mutation CreateAnnouncement(
  $title: String!
  $details: String!
  $category: String!
  $status: Boolean!
  $visibility: DateTime!
) {
  createAnnouncement(createAnnouncementInput: {
    title: $title
    details: $details
    category: $category
    status: $status
    visibility: $visibility
  }) {
    id,
    title,
    details,
    category,
    status,
    visibility
  }
}
`;

export const mutationAddNotificationView = gql`
mutation AddNotificationView(
  $notificationId: Int!
  $user_id: Int!
) {
  addNotificationView(
    notificationId: $notificationId,
    user_id: $user_id
  ) {
    id
  }
}
`;

export const mutationUpdateAnnouncement = gql`
mutation UpdateAnnouncement(
  $id: Int!
  $title: String!
  $details: String!
  $category: String!
  $status: Boolean!
  $visibility: DateTime!
) {
  updateAnnouncement(updateAnnouncementInput: {
    id: $id
    title: $title
    details: $details
    category: $category
    status: $status
    visibility: $visibility
  }) {
    id
    title,
    details,
    category,
    status,
    visibility
  }
}
`;

export const getAnnouncementById = gql`
query GetAnnouncementById($id: Int!) {
  announcement(id: $id) {
    id
    title
    details
    category
    status
    visibility
  }
}
`;

export const mutationRemoveAnnouncement = gql`
mutation RemoveAnnouncement($id: Int!) {
  removeAnnouncement(id: $id) {
    title
  }
}
`;