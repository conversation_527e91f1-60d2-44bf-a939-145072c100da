import axios from 'axios';
import { baseUrl } from '@/Data/ApolloClient';

/**
 * CSRF Token Management Utility
 * Handles fetching, storing, and using CSRF tokens for secure requests
 */
class CsrfManager {
  constructor() {
    this.csrfToken = null;
    this.sessionToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Fetch CSRF token from backend
   * @returns {Promise<{csrfToken: string, sessionToken: string}>}
   */
  async fetchCsrfToken() {
    try {
      const response = await axios.get(`${baseUrl}/security/csrf-token`, {
        withCredentials: true,
      });

      const { csrfToken, sessionToken } = response.data;
      
      // Store tokens
      this.csrfToken = csrfToken;
      this.sessionToken = sessionToken;
      this.tokenExpiry = Date.now() + (23 * 60 * 60 * 1000); // 23 hours

      // Store in localStorage for persistence
      localStorage.setItem('csrf-token', csrfToken);
      localStorage.setItem('session-token', sessionToken);
      localStorage.setItem('csrf-expiry', this.tokenExpiry.toString());

      console.log('CSRF token fetched successfully');
      return { csrfToken, sessionToken };
    } catch (error) {
      console.error('Failed to fetch CSRF token:', error);
      throw new Error('Failed to fetch CSRF token');
    }
  }

  /**
   * Get current CSRF token, fetch new one if expired
   * @returns {Promise<string>}
   */
  async getCsrfToken() {
    // Check if we have a valid token
    if (this.isTokenValid()) {
      return this.csrfToken;
    }

    // Try to load from localStorage
    const storedToken = localStorage.getItem('csrf-token');
    const storedSession = localStorage.getItem('session-token');
    const storedExpiry = localStorage.getItem('csrf-expiry');

    if (storedToken && storedSession && storedExpiry) {
      const expiry = parseInt(storedExpiry);
      if (Date.now() < expiry) {
        this.csrfToken = storedToken;
        this.sessionToken = storedSession;
        this.tokenExpiry = expiry;
        return this.csrfToken;
      }
    }

    // Fetch new token
    const { csrfToken } = await this.fetchCsrfToken();
    return csrfToken;
  }

  /**
   * Get current session token
   * @returns {Promise<string>}
   */
  async getSessionToken() {
    if (!this.isTokenValid()) {
      await this.getCsrfToken(); // This will fetch both tokens
    }
    return this.sessionToken;
  }

  /**
   * Check if current token is valid
   * @returns {boolean}
   */
  isTokenValid() {
    return this.csrfToken && 
           this.sessionToken && 
           this.tokenExpiry && 
           Date.now() < this.tokenExpiry;
  }

  /**
   * Clear stored tokens
   */
  clearTokens() {
    this.csrfToken = null;
    this.sessionToken = null;
    this.tokenExpiry = null;
    
    localStorage.removeItem('csrf-token');
    localStorage.removeItem('session-token');
    localStorage.removeItem('csrf-expiry');
  }

  /**
   * Get headers for secure requests
   * @returns {Promise<Object>}
   */
  async getSecureHeaders() {
    const csrfToken = await this.getCsrfToken();
    const sessionToken = await this.getSessionToken();

    return {
      'X-CSRF-Token': csrfToken,
      'X-Session-Token': sessionToken,
    };
  }

  /**
   * Verify CSRF token with backend (for testing)
   * @returns {Promise<boolean>}
   */
  async verifyCsrfToken() {
    try {
      const headers = await this.getSecureHeaders();
      const response = await axios.post(
        `${baseUrl}/security/verify-csrf`,
        {},
        { 
          headers,
          withCredentials: true 
        }
      );

      return response.data.valid;
    } catch (error) {
      console.error('CSRF token verification failed:', error);
      return false;
    }
  }
}

// Create singleton instance
const csrfManager = new CsrfManager();

/**
 * Get CSRF token for use in requests
 * @returns {Promise<string>}
 */
export const getCsrfToken = () => csrfManager.getCsrfToken();

/**
 * Get session token for use in requests
 * @returns {Promise<string>}
 */
export const getSessionToken = () => csrfManager.getSessionToken();

/**
 * Get secure headers for GraphQL requests
 * @returns {Promise<Object>}
 */
export const getSecureHeaders = () => csrfManager.getSecureHeaders();

/**
 * Clear CSRF tokens (on logout)
 */
export const clearCsrfTokens = () => csrfManager.clearTokens();

/**
 * Initialize CSRF tokens (call on app startup)
 * @returns {Promise<void>}
 */
export const initializeCsrfTokens = async () => {
  try {
    await csrfManager.getCsrfToken();
    console.log('CSRF tokens initialized');
  } catch (error) {
    console.warn('Failed to initialize CSRF tokens:', error);
  }
};

/**
 * Verify CSRF token (for testing)
 * @returns {Promise<boolean>}
 */
export const verifyCsrfToken = () => csrfManager.verifyCsrfToken();

export default csrfManager;
