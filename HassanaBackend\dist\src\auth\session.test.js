"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const session_service_1 = require("./session.service");
const jwt_guard_1 = require("./jwt.guard");
const graphql_1 = require("@nestjs/graphql");
const jwt = require("jsonwebtoken");
const mockRedis = {
    hgetall: jest.fn(),
    hmset: jest.fn(),
    expire: jest.fn(),
    del: jest.fn(),
    setex: jest.fn(),
    get: jest.fn(),
    keys: jest.fn(),
};
describe('Single Session Implementation Test', () => {
    let sessionService;
    let jwtGuard;
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                session_service_1.SessionService,
                jwt_guard_1.JwtGuard,
                {
                    provide: 'default_IORedisModuleConnectionToken',
                    useValue: mockRedis,
                },
            ],
        }).compile();
        sessionService = module.get(session_service_1.SessionService);
        jwtGuard = module.get(jwt_guard_1.JwtGuard);
        jest.clearAllMocks();
    });
    describe('Single Session Enforcement', () => {
        it('should invalidate previous session when user logs in from new device', async () => {
            const userId = 'test-user-123';
            const tokenPayload = { id: userId, username: 'testuser', role: 'USER' };
            const deviceInfo1 = { userAgent: 'Browser 1', ip: '***********' };
            const deviceInfo2 = { userAgent: 'Browser 2', ip: '***********' };
            mockRedis.hgetall.mockResolvedValueOnce({
                sessionId: 'old-session-123',
                token: 'old-jwt-token',
                userId: userId,
                isActive: 'true'
            });
            mockRedis.hmset.mockResolvedValue('OK');
            mockRedis.expire.mockResolvedValue(1);
            mockRedis.setex.mockResolvedValue('OK');
            const token1 = await sessionService.createUserSession(userId, tokenPayload, deviceInfo1);
            expect(token1).toBeDefined();
            mockRedis.hgetall.mockResolvedValueOnce({
                sessionId: 'session-from-token1',
                token: token1,
                userId: userId,
                isActive: 'true'
            });
            const token2 = await sessionService.createUserSession(userId, tokenPayload, deviceInfo2);
            expect(token2).toBeDefined();
            expect(token2).not.toBe(token1);
            expect(mockRedis.setex).toHaveBeenCalled();
        });
        it('should reject requests with invalidated session', async () => {
            const userId = 'test-user-123';
            const sessionId = 'old-session-123';
            const token = 'old-jwt-token';
            mockRedis.hgetall.mockResolvedValue({});
            mockRedis.get.mockResolvedValue(null);
            const isValid = await sessionService.validateUserSession(userId, sessionId, token);
            expect(isValid).toBe(false);
        });
        it('should reject requests with mismatched session ID', async () => {
            const userId = 'test-user-123';
            const sessionId = 'old-session-123';
            const currentSessionId = 'new-session-456';
            const token = 'jwt-token';
            mockRedis.hgetall.mockResolvedValue({
                sessionId: currentSessionId,
                token: 'current-token',
                userId: userId,
                isActive: 'true'
            });
            mockRedis.get.mockResolvedValue(null);
            mockRedis.setex.mockResolvedValue('OK');
            const isValid = await sessionService.validateUserSession(userId, sessionId, token);
            expect(isValid).toBe(false);
            expect(mockRedis.setex).toHaveBeenCalled();
        });
        it('should accept requests with valid session', async () => {
            const userId = 'test-user-123';
            const sessionId = 'current-session-123';
            const token = 'current-jwt-token';
            mockRedis.hgetall.mockResolvedValue({
                sessionId: sessionId,
                token: token,
                userId: userId,
                isActive: 'true'
            });
            mockRedis.get.mockResolvedValue(null);
            const isValid = await sessionService.validateUserSession(userId, sessionId, token);
            expect(isValid).toBe(true);
        });
        it('should reject blacklisted tokens', async () => {
            const userId = 'test-user-123';
            const sessionId = 'session-123';
            const token = 'blacklisted-token';
            mockRedis.get.mockResolvedValue('true');
            const isValid = await sessionService.validateUserSession(userId, sessionId, token);
            expect(isValid).toBe(false);
        });
    });
    describe('JWT Guard Session Validation', () => {
        it('should validate session in JWT guard', async () => {
            const mockContext = {
                switchToHttp: () => ({
                    getRequest: () => ({
                        headers: {
                            authorization: 'Bearer valid-jwt-token'
                        }
                    })
                })
            };
            const mockUser = {
                id: 'test-user-123',
                sessionId: 'session-123',
                username: 'testuser',
                role: 'USER'
            };
            jest.spyOn(jwt, 'verify').mockReturnValue(mockUser);
            jest.spyOn(graphql_1.GqlExecutionContext, 'create').mockReturnValue({
                getContext: () => ({
                    req: {
                        headers: { authorization: 'Bearer valid-jwt-token' }
                    }
                })
            });
            jest.spyOn(sessionService, 'validateUserSession').mockResolvedValue(true);
            const result = await jwtGuard.canActivate(mockContext);
            expect(result).toBe(true);
            expect(sessionService.validateUserSession).toHaveBeenCalledWith('test-user-123', 'session-123', 'valid-jwt-token');
        });
        it('should reject request with invalid session', async () => {
            const mockContext = {
                switchToHttp: () => ({
                    getRequest: () => ({
                        headers: {
                            authorization: 'Bearer invalid-session-token'
                        }
                    })
                })
            };
            const mockUser = {
                id: 'test-user-123',
                sessionId: 'old-session-123',
                username: 'testuser',
                role: 'USER'
            };
            jest.spyOn(jwt, 'verify').mockReturnValue(mockUser);
            jest.spyOn(graphql_1.GqlExecutionContext, 'create').mockReturnValue({
                getContext: () => ({
                    req: {
                        headers: { authorization: 'Bearer invalid-session-token' }
                    }
                })
            });
            jest.spyOn(sessionService, 'validateUserSession').mockResolvedValue(false);
            await expect(jwtGuard.canActivate(mockContext)).rejects.toThrow('Session is no longer valid. Please log in again.');
        });
    });
    describe('Session Cleanup', () => {
        it('should properly logout user and blacklist token', async () => {
            const userId = 'test-user-123';
            const token = 'jwt-token-to-blacklist';
            mockRedis.del.mockResolvedValue(1);
            mockRedis.setex.mockResolvedValue('OK');
            jest.spyOn(jwt, 'decode').mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 3600
            });
            await sessionService.logoutUser(userId, token);
            expect(mockRedis.del).toHaveBeenCalledWith(`user_session:${userId}`);
            expect(mockRedis.setex).toHaveBeenCalled();
        });
        it('should force logout user', async () => {
            const userId = 'test-user-123';
            mockRedis.hgetall.mockResolvedValue({
                token: 'user-token',
                sessionId: 'session-123'
            });
            mockRedis.del.mockResolvedValue(1);
            mockRedis.setex.mockResolvedValue('OK');
            jest.spyOn(jwt, 'decode').mockReturnValue({
                exp: Math.floor(Date.now() / 1000) + 3600
            });
            await sessionService.forceLogoutUser(userId);
            expect(mockRedis.del).toHaveBeenCalledWith(`user_session:${userId}`);
            expect(mockRedis.setex).toHaveBeenCalled();
        });
    });
});
//# sourceMappingURL=session.test.js.map