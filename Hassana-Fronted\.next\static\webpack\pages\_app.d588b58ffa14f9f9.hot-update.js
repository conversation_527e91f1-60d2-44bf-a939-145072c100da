"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/Data/ApolloClient.js":
/*!**********************************!*\
  !*** ./src/Data/ApolloClient.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _apollo_client_link_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @apollo/client/link/context */ \"./node_modules/@apollo/client/link/context/index.js\");\n/* harmony import */ var _apollo_client_link_error__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client/link/error */ \"./node_modules/@apollo/client/link/error/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n\n\n//export const baseUrl = \"http://*************:3001/v1\";\n//export const baseUrl = \"https://portal.hassana.com.sa/v1\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com/v1\";\n////export const baseUrl = \"http://localhost:3001\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com\";\n//export const baseUrl = \"https://v2-portal.hassana.com.sa\";\n//export const base_url = \"https://v2-portal.hassana.com.sa/v1\";\n//export const base_url = \"https://localhost:3001/v1\";\nconst httpLink = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.createHttpLink)({\n    uri: baseUrl + \"/graphql\"\n});\n// Auth link to add JWT token to headers\nconst authLink = (0,_apollo_client_link_context__WEBPACK_IMPORTED_MODULE_2__.setContext)(async (_, param)=>{\n    let { headers } = param;\n    // Get token from session or localStorage\n    let token = null;\n    try {\n        const session = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.getSession)();\n        token = session === null || session === void 0 ? void 0 : session.accessToken;\n    } catch (error) {\n        console.warn(\"Could not get session:\", error);\n    }\n    // Fallback to localStorage if session token not available\n    if (!token && \"object\" !== \"undefined\") {\n        token = localStorage.getItem(\"jwtToken\");\n    }\n    return {\n        headers: {\n            ...headers,\n            ...token && {\n                authorization: \"Bearer \".concat(token)\n            }\n        }\n    };\n});\n// Error link to handle GraphQL and network errors\nconst errorLink = (0,_apollo_client_link_error__WEBPACK_IMPORTED_MODULE_3__.onError)((param)=>{\n    let { graphQLErrors, networkError } = param;\n    if (graphQLErrors) {\n        graphQLErrors.forEach((param)=>{\n            let { message, locations, path, extensions } = param;\n            console.error(\"[GraphQL error]: Message: \".concat(message, \", Location: \").concat(locations, \", Path: \").concat(path));\n            // Handle authentication errors\n            if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\")) {\n                console.warn(\"Authentication error detected, redirecting to login...\");\n                // Clear invalid token\n                if (true) {\n                    localStorage.removeItem(\"jwtToken\");\n                }\n                // Redirect to login page\n                if (true) {\n                    window.location.href = \"/login?login=false\";\n                }\n            }\n            // Handle authorization errors\n            if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\")) {\n                console.warn(\"Authorization error detected\");\n                // You can show a toast or redirect to unauthorized page\n                if (true) {\n                    // You can implement a toast notification here\n                    console.error(\"Access denied: Insufficient permissions\");\n                }\n            }\n        });\n    }\n    if (networkError) {\n        console.error(\"[Network error]: \".concat(networkError));\n        // Handle network errors that might indicate auth issues\n        if (networkError.statusCode === 401) {\n            console.warn(\"Network 401 error, clearing token and redirecting...\");\n            if (true) {\n                localStorage.removeItem(\"jwtToken\");\n                window.location.href = \"/login?login=false\";\n            }\n        }\n    }\n});\nconst client = new _apollo_client__WEBPACK_IMPORTED_MODULE_1__.ApolloClient({\n    link: (0,_apollo_client__WEBPACK_IMPORTED_MODULE_1__.from)([\n        errorLink,\n        authLink,\n        httpLink\n    ]),\n    cache: new _apollo_client__WEBPACK_IMPORTED_MODULE_1__.InMemoryCache(),\n    defaultOptions: {\n        watchQuery: {\n            errorPolicy: \"all\"\n        },\n        query: {\n            errorPolicy: \"all\"\n        }\n    }\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (client);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./src/Data/ApolloClient.js\n"));

/***/ })

});