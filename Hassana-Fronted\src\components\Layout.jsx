import {
  Box,
  useMediaQuery,
  useTheme,
  Slide,
  SwipeableDrawer,
  Button,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import LaunchPad from "./LaunchPad";
import { SvgIcon } from "@mui/material";
import { useColor } from "./ColorContext";
import { getSelectedColor } from "./HelperFunctions";
import WeatherComponent from "../components/WeatherComponent";
import QuickLinks from "./QuickLink";

const Layout = (props) => {
  const theme = useTheme();

  const { children } = props;
  const isExtraSmallScreen = useMediaQuery("(max-width:599px)");
  const isSmallScreen = useMediaQuery(
    "(min-width:600px) and (max-width:999px)"
  );
  const isMediumScreen = useMediaQuery(
    "(min-width:1000px) and (max-width:1279px)"
  );
  const isLargeScreen = useMediaQuery(
    "(min-width:1280px) and (max-width:1919px)"
  );
  const isExtraLargeScreen = useMediaQuery("(min-width:1920px)");

  const [isSidebarOpen, setSidebarOpen] = useState(
    isMediumScreen || isLargeScreen || isExtraLargeScreen
  );

  const toggleSidebar = () => {
    setSidebarOpen(!isSidebarOpen);
  };

  console.log();

  const ArrowForwardPurpleIcon = (props) => (
    <SvgIcon {...props} fontSize="large">
      <path d="M18 2a10 8 0 0 0 0 20z" fill="#F0EFFB" />
      <path d="M12 8l4 4-4 4" stroke="purple" strokeWidth="1" fill="none" />
    </SvgIcon>
  );

  const ArrowBackPurpleIcon = (props) => (
    <SvgIcon {...props} fontSize="large">
      {/* Draw the semi-circle on the right side */}
      <path d="M14 2a14 10 0 0 0 0 20z" fill="#F0EFFB" />

      {/* Draw the arrow with the curve on the left and the straight line on the right */}
      <path d="M12 16l-4-4 4-4" stroke="purple" strokeWidth="1" fill="none" />
    </SvgIcon>
  );
  useEffect(() => {
    setSidebarOpen(isLargeScreen || isExtraLargeScreen);
  }, [isLargeScreen, isExtraLargeScreen]);

  return (
    <Box sx={{ display: "flex", position: "relative" , overflowY: 'scroll' }}>
      <Box sx={{ flexGrow: "1", marginX: "auto" }}>{children}</Box>
      {(isExtraSmallScreen || isSmallScreen || isMediumScreen) && (
        <Box
          sx={{
            position: isSmallScreen ? "absolute" : "absolute",
            top: "50vh",
            right: isSidebarOpen ? "15.4rem" : "0",
            cursor: "pointer",
            zIndex: isSidebarOpen ? 9998 : "1",
          }}
          onClick={toggleSidebar}
        >
          {isSidebarOpen ? <ArrowForwardPurpleIcon /> : <ArrowBackPurpleIcon />}
        </Box>
      )}
      <Slide direction="left" in={isSidebarOpen} mountOnEnter unmountOnExit>
        <Box
           sx={{
            width: isExtraLargeScreen || isLargeScreen ? "18rem" : "16rem",
            height: "90vh", // Set the height to 90vh
            minWidth: "19rem",
            background: theme.palette.background.launchpad,
            overflowY: "auto", // Make content scrollable if it exceeds 90vh
            padding: "12px",
            zIndex: 9,
            position: isExtraLargeScreen || isLargeScreen ? "relative" : "absolute",
            right: 0,
            top: isLargeScreen || isMediumScreen ? "0" : "0",
            "&::-webkit-scrollbar": { display: "none" }, // Hide scrollbar for Chrome, Safari, and Edge
            "-ms-overflow-style": "none", // Hide scrollbar for Internet Explorer and Edge
            "scrollbar-width": "none", // Hide scrollbar for Firefox
          }}
        >
          <QuickLinks/>
          <LaunchPad />
          {/* <WeatherComponent /> */}
        </Box>
      </Slide>
    </Box>
  );
};

export default Layout;
