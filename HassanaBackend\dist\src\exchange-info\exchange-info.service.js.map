{"version": 3, "file": "exchange-info.service.js", "sourceRoot": "", "sources": ["../../../src/exchange-info/exchange-info.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAE5C,2CAAwC;AAExC,iCAAiC;AAEjC,uCAAoC;AAK7B,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IAAzB;QA4BG,QAAG,GAAW,+CAA+C,CAAC;IAwHxE,CAAC;IA/IC,OAAO;QACL,OAAO,sCAAsC,CAAC;IAChD,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACtB,IAAI,UAAU,GAAG,MAAM,aAAK,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACxB,IAAI,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAC3C,UAAU,CAAC,QAAQ,EACnB,UAAU,CAAC,QAAQ,CACpB,CAAC;QACF,OAAO,WAAW,CAAC;IAErB,CAAC;IAED,MAAM,CAAC,EAAU,EAAE,qBAA4C;QAC7D,OAAO,0BAA0B,EAAE,eAAe,CAAC;IACrD,CAAC;IAED,MAAM,CAAC,EAAU;QACf,OAAO,0BAA0B,EAAE,eAAe,CAAC;IACrD,CAAC;IAID,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,QAAgB;QACvD,MAAM,SAAS,GAAG,IAAA,uBAAU,EAAC;YAC3B,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE,QAAQ;YAClB,MAAM,EAAE,EAAE;YACV,WAAW,EAAE,EAAE;SAChB,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG;YACd,MAAM,EAAE,MAAM;YACd,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO,EAAE;gBACP,MAAM,EAAE,mCAAmC;gBAC3C,cAAc,EAAE,yBAAyB;gBACzC,UAAU,EAAE,uEAAuE;aACpF;YACD,IAAI,EAAE,IAAI,CAAC,iBAAiB,EAAE;SAC/B,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC;YACrC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACzD,IAAI,UAAU,EAAE,CAAC;gBACf,MAAM,UAAU,GACd,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CACtD,oBAAoB,CACrB,CAAC,2BAA2B,CAAC,CAAC,gBAAgB,CAAC,CAAC;gBACnD,OAAO,CAAC,GAAG,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC;gBAEvC,IAAI,IAAI,GAAG,EAAE,CAAC;gBACd,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC;oBAC5B,IAAI,QAAQ,GACV,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAAC,CACtD,oBAAoB,CACrB,CAAC,2BAA2B,CAAC,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,CACvD,gBAAgB,CACjB,CAAC;oBAGJ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC7B,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACxB,CAAC;oBAED,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE;wBACzB,MAAM,YAAY,GAAG;4BACnB,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC;4BACxB,MAAM,EAAE,IAAI,CAAC,wBAAwB,CAAC;4BACtC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC;4BAC3B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;4BACxB,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC;4BACpB,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC;4BAClB,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC;yBAC7B,CAAC;wBACF,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC1B,CAAC,CAAC,CAAC;oBAGH,OAAO,IAAI,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,CAAC,kCAAkC,QAAQ,WAAW,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YAC7B,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IACO,iBAAiB;QACvB,OAAO;;;;;;;;;;;;;;;;;;;;;;;+CAuBoC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;6CAC1B,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;;;;;;uBAM9C,CAAC;IACtB,CAAC;IAAA,CAAC;IAEM,gBAAgB,CAAC,OAAY;QACnC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC;gBAC/B,aAAa,EAAE,KAAK;gBACpB,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;YACH,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;gBAC1C,IAAI,GAAG,EAAE,CAAC;oBACR,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;oBACzC,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;gBACrB,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAAA,CAAC;CACH,CAAA;AApJY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;GACA,mBAAmB,CAoJ/B"}