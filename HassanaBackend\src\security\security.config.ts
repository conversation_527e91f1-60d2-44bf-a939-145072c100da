import { Injectable } from '@nestjs/common';

@Injectable()
export class SecurityConfig {
  // CORS Configuration
  static getCorsConfig() {
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://portal.hassana.com.sa',
      'https://v2-portal.hassana.com.sa'
    ];

    return {
      origin: (origin: string, callback: (err: Error | null, allow?: boolean) => void) => {
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) return callback(null, true);
        
        if (allowedOrigins.includes(origin)) {
          return callback(null, true);
        } else {
          return callback(new Error('Not allowed by CORS'), false);
        }
      },
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-CSRF-Token',
        'X-Session-Token'
      ],
      credentials: true,
      preflightContinue: false,
      optionsSuccessStatus: 204,
    };
  }

  // Helmet Security Headers Configuration
  static getHelmetConfig() {
    return {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      },
      crossOriginEmbedderPolicy: false, // Disable for GraphQL playground
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
      referrerPolicy: { policy: 'same-origin' },
    };
  }

  // Rate Limiting Configuration
  static getRateLimitConfig() {
    return {
      ttl: 60, // 1 minute
      limit: process.env.NODE_ENV === 'production' ? 100 : 1000, // requests per minute
      skipIf: (context) => {
        // Skip rate limiting for health checks
        const request = context.switchToHttp().getRequest();
        return request.url === '/health' || request.url === '/';
      },
    };
  }

  // GraphQL Security Configuration
  static getGraphQLSecurityConfig() {
    return {
      // Disable introspection and playground in production
      introspection: process.env.NODE_ENV !== 'production',
      playground: process.env.NODE_ENV !== 'production',
      
      // Query complexity and depth limiting
      validationRules: process.env.NODE_ENV === 'production' ? [
        // Add query complexity rules here if needed
      ] : [],
      
      // Context security
      context: ({ req, res }) => {
        // Add security context here
        return { 
          req, 
          res,
          // Add CSRF token to context for easy access
          csrfToken: req.headers['x-csrf-token'],
          sessionToken: req.headers['x-session-token'],
        };
      },
    };
  }

  // Session Configuration
  static getSessionConfig() {
    return {
      secret: process.env.SESSION_SECRET || 'change-this-in-production',
      resave: false,
      saveUninitialized: false,
      cookie: {
        secure: process.env.NODE_ENV === 'production', // HTTPS only in production
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        sameSite: 'strict' as const,
      },
    };
  }

  // Validation Configuration
  static getValidationConfig() {
    return {
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      forbidUnknownValues: true,
      transformOptions: { 
        enableImplicitConversion: true 
      },
      // Add validation error messages
      exceptionFactory: (errors) => {
        const messages = errors.map(error => 
          Object.values(error.constraints || {}).join(', ')
        );
        return new Error(`Validation failed: ${messages.join('; ')}`);
      },
    };
  }
}
