import { Repository } from 'typeorm';
import { CreateNewsInput } from './dto/create-news.dto';
import { UpdateNewsInput } from './dto/update-news.dto';
import { News as NewsEntity } from './entities/news.entity';
export declare class NewsService {
    private readonly newsRepository;
    constructor(newsRepository: Repository<NewsEntity>);
    private validateUUID;
    createNews(createNewsInput: CreateNewsInput): Promise<any>;
    allNews(): Promise<NewsEntity[]>;
    allExternalNews(): Promise<NewsEntity[]>;
    allInternalNews(): Promise<NewsEntity[]>;
    findOne(id: string): Promise<NewsEntity>;
    update(id: string, updateNewsInput: UpdateNewsInput): Promise<NewsEntity>;
    remove(id: string): Promise<NewsEntity | null>;
}
