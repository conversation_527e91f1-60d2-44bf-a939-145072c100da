// import React, { createContext, useContext, useState } from "react";

// // Create a context with a default color
// const ColorContext = createContext("white"); // Replace with your preferred color code

// // Create a ColorProvider component to wrap your app
// export const ColorProvider = ({ children }) => {
  
//   const [color, setColor] = useState("white"); // Replace with your preferred color code

//   const setGlobalColor = (newColor) => {
//     setColor(newColor);
//   };

//   return (
//     <ColorContext.Provider value={{ color, setGlobalColor }}>
//       {children}
//     </ColorContext.Provider>
//   );
// };

// // Custom hook to easily access the color and setter
// export const useColor = () => {
//   const context = useContext(ColorContext);
//   if (!context) {
//     throw new Error("useColor must be used within a ColorProvider");
//   }
//   return context;
// };
import React, { createContext, useContext, useState, useEffect } from "react";

const ColorContext = createContext({ color: "white", setColor: () => {} });

export const ColorProvider = ({ children }) => {
  const [color, setGlobalColor] = useState(() => {
    // Check if localStorage is available on the client
    if (typeof window !== "undefined") {
      const storedColor = localStorage.getItem("color");
      return storedColor !== null ? storedColor : "white"; // Replace with your preferred color code
    }
    return "white"; // Default color for server-side rendering
  });

  useEffect(() => {
    // Only write to localStorage on the client
    if (typeof window !== "undefined") {
      localStorage.setItem("color", color);
    }
  }, [color]);

  return (
    <ColorContext.Provider value={{ color, setGlobalColor }}>
      {children}
    </ColorContext.Provider>
  );
};

export const useColor = () => {
  const context = useContext(ColorContext);
  if (!context) {
    throw new Error("useColor must be used within a ColorProvider");
  }
  return context;
};