import { LeaveService } from './leave.service';
import { CreateLeaveInput } from './dto/create-leave.input';
import { UpdateLeaveInput } from './dto/update-leave.input';
import { UUID } from 'crypto';
export declare class LeaveResolver {
    private readonly leaveService;
    constructor(leaveService: LeaveService);
    createLeave(createLeaveInput: CreateLeaveInput): Promise<import("./entities/leave.entity").Leave>;
    findAll(): Promise<import("./entities/leave.entity").Leave[]>;
    getUserLeaves(id: number): Promise<{
        medical: number;
        casual: number;
    }>;
    findOne(id: UUID): Promise<import("./entities/leave.entity").Leave>;
    updateLeave(updateLeaveInput: UpdateLeaveInput): Promise<import("./entities/leave.entity").Leave>;
    removeLeave(id: UUID): Promise<import("./entities/leave.entity").Leave>;
}
