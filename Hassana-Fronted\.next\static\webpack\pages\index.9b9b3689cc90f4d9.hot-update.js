"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/utils/graphqlAuth.js":
/*!**********************************!*\
  !*** ./src/utils/graphqlAuth.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAuthOptions: function() { return /* binding */ createAuthOptions; },\n/* harmony export */   createAuthOptionsSync: function() { return /* binding */ createAuthOptionsSync; },\n/* harmony export */   createGraphQLErrorHandler: function() { return /* binding */ createGraphQLErrorHandler; },\n/* harmony export */   getErrorMessage: function() { return /* binding */ getErrorMessage; },\n/* harmony export */   isAuthError: function() { return /* binding */ isAuthError; },\n/* harmony export */   isForbiddenError: function() { return /* binding */ isForbiddenError; }\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _csrfUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./csrfUtils */ \"./src/utils/csrfUtils.js\");\n\n\n/**\n * Creates GraphQL query/mutation options with authentication and CSRF headers\n * @param {Object} session - NextAuth session object\n * @param {Object} additionalOptions - Additional Apollo options\n * @returns {Object} Apollo query/mutation options with auth and CSRF headers\n */ const createAuthOptions = async function(session) {\n    let additionalOptions = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _additionalOptions_context;\n    const token = (session === null || session === void 0 ? void 0 : session.accessToken) || ( true ? localStorage.getItem(\"jwtToken\") : 0);\n    // Get CSRF headers for secure requests\n    let csrfHeaders = {};\n    try {\n        csrfHeaders = await (0,_csrfUtils__WEBPACK_IMPORTED_MODULE_1__.getSecureHeaders)();\n    } catch (error) {\n        console.warn(\"Failed to get CSRF headers:\", error);\n    }\n    return {\n        ...additionalOptions,\n        context: {\n            ...additionalOptions.context,\n            headers: {\n                ...(_additionalOptions_context = additionalOptions.context) === null || _additionalOptions_context === void 0 ? void 0 : _additionalOptions_context.headers,\n                ...token && {\n                    authorization: \"Bearer \".concat(token)\n                },\n                ...csrfHeaders\n            }\n        },\n        errorPolicy: \"all\"\n    };\n};\n/**\n * Synchronous version of createAuthOptions (without CSRF protection)\n * Use this only when CSRF protection is not required\n * @param {Object} session - NextAuth session object\n * @param {Object} additionalOptions - Additional Apollo options\n * @returns {Object} Apollo query/mutation options with auth headers only\n */ const createAuthOptionsSync = function(session) {\n    let additionalOptions = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _additionalOptions_context;\n    const token = (session === null || session === void 0 ? void 0 : session.accessToken) || ( true ? localStorage.getItem(\"jwtToken\") : 0);\n    return {\n        ...additionalOptions,\n        context: {\n            ...additionalOptions.context,\n            headers: {\n                ...(_additionalOptions_context = additionalOptions.context) === null || _additionalOptions_context === void 0 ? void 0 : _additionalOptions_context.headers,\n                ...token && {\n                    authorization: \"Bearer \".concat(token)\n                }\n            }\n        },\n        errorPolicy: \"all\"\n    };\n};\n/**\n * Creates error handler for GraphQL operations\n * @param {Function} onAuthError - Callback for authentication errors\n * @param {Function} onForbiddenError - Callback for authorization errors\n * @param {Function} onOtherError - Callback for other errors\n * @returns {Function} Error handler function\n */ const createGraphQLErrorHandler = (onAuthError, onForbiddenError, onOtherError)=>{\n    return (error)=>{\n        console.error(\"GraphQL Error:\", error);\n        // Handle GraphQL errors\n        if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n            error.graphQLErrors.forEach((gqlError)=>{\n                const { message, extensions } = gqlError;\n                // Authentication errors\n                if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\") || message.includes(\"Token has expired\") || message.includes(\"Invalid token\")) {\n                    if (onAuthError) {\n                        onAuthError(gqlError);\n                    } else {\n                        console.warn(\"Authentication error:\", message);\n                        // Clear invalid token\n                        if (true) {\n                            localStorage.removeItem(\"jwtToken\");\n                            window.location.href = \"/login?login=false&reason=auth_error\";\n                        }\n                    }\n                } else if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\") || message.includes(\"Required role\")) {\n                    if (onForbiddenError) {\n                        onForbiddenError(gqlError);\n                    } else {\n                        console.warn(\"Authorization error:\", message);\n                    }\n                } else {\n                    if (onOtherError) {\n                        onOtherError(gqlError);\n                    } else {\n                        console.error(\"GraphQL error:\", message);\n                    }\n                }\n            });\n        }\n        // Handle network errors\n        if (error.networkError) {\n            const { statusCode } = error.networkError;\n            if (statusCode === 401) {\n                if (onAuthError) {\n                    onAuthError(error.networkError);\n                } else {\n                    console.warn(\"Network authentication error\");\n                    if (true) {\n                        localStorage.removeItem(\"jwtToken\");\n                        window.location.href = \"/login?login=false&reason=network_auth\";\n                    }\n                }\n            } else if (statusCode === 403) {\n                if (onForbiddenError) {\n                    onForbiddenError(error.networkError);\n                } else {\n                    console.warn(\"Network authorization error\");\n                }\n            } else {\n                if (onOtherError) {\n                    onOtherError(error.networkError);\n                } else {\n                    console.error(\"Network error:\", error.networkError);\n                }\n            }\n        }\n    };\n};\n/**\n * Utility function to check if an error is an authentication error\n * @param {Object} error - Apollo error object\n * @returns {boolean} Whether the error is an authentication error\n */ const isAuthError = (error)=>{\n    if (!error) return false;\n    // Check GraphQL errors\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        return error.graphQLErrors.some((gqlError)=>{\n            const { message, extensions } = gqlError;\n            return (extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\") || message.includes(\"Token has expired\") || message.includes(\"Invalid token\");\n        });\n    }\n    // Check network errors\n    if (error.networkError && error.networkError.statusCode === 401) {\n        return true;\n    }\n    return false;\n};\n/**\n * Utility function to check if an error is an authorization error\n * @param {Object} error - Apollo error object\n * @returns {boolean} Whether the error is an authorization error\n */ const isForbiddenError = (error)=>{\n    if (!error) return false;\n    // Check GraphQL errors\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        return error.graphQLErrors.some((gqlError)=>{\n            const { message, extensions } = gqlError;\n            return (extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\") || message.includes(\"Required role\");\n        });\n    }\n    // Check network errors\n    if (error.networkError && error.networkError.statusCode === 403) {\n        return true;\n    }\n    return false;\n};\n/**\n * Extracts user-friendly error message from GraphQL error\n * @param {Object} error - Apollo error object\n * @returns {string} User-friendly error message\n */ const getErrorMessage = (error)=>{\n    if (!error) return \"An unknown error occurred\";\n    // Check for GraphQL errors first\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        const gqlError = error.graphQLErrors[0];\n        // Return custom messages for common errors\n        if (isAuthError(error)) {\n            return \"Authentication required. Please log in again.\";\n        }\n        if (isForbiddenError(error)) {\n            return \"Access denied. You do not have permission to perform this action.\";\n        }\n        return gqlError.message || \"A GraphQL error occurred\";\n    }\n    // Check for network errors\n    if (error.networkError) {\n        if (error.networkError.statusCode === 401) {\n            return \"Authentication required. Please log in again.\";\n        }\n        if (error.networkError.statusCode === 403) {\n            return \"Access denied. You do not have permission to perform this action.\";\n        }\n        return error.networkError.message || \"A network error occurred\";\n    }\n    return error.message || \"An error occurred\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/graphqlAuth.js\n"));

/***/ })

});