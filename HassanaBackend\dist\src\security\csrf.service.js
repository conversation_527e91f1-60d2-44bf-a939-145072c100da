"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CsrfService = void 0;
const common_1 = require("@nestjs/common");
const crypto = require("crypto");
let CsrfService = class CsrfService {
    constructor() {
        this.csrfSecret = process.env.CSRF_SECRET || 'default-csrf-secret-change-in-production';
    }
    generateCsrfToken(sessionToken) {
        const hmac = crypto.createHmac('sha256', this.csrfSecret);
        hmac.update(sessionToken);
        return hmac.digest('hex');
    }
    verifyCsrfToken(csrfToken, sessionToken) {
        try {
            const expectedToken = this.generateCsrfToken(sessionToken);
            return crypto.timingSafeEqual(Buffer.from(csrfToken, 'hex'), Buffer.from(expectedToken, 'hex'));
        }
        catch (error) {
            return false;
        }
    }
    generateSessionToken() {
        return crypto.randomBytes(32).toString('hex');
    }
    createCsrfResponse(sessionToken) {
        const session = sessionToken || this.generateSessionToken();
        const csrf = this.generateCsrfToken(session);
        return {
            csrfToken: csrf,
            sessionToken: session
        };
    }
};
exports.CsrfService = CsrfService;
exports.CsrfService = CsrfService = __decorate([
    (0, common_1.Injectable)()
], CsrfService);
//# sourceMappingURL=csrf.service.js.map