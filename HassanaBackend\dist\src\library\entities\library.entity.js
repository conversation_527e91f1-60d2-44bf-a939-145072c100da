"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Library = void 0;
const BaseEntity_1 = require("../../BaseEntity");
const typeorm_1 = require("typeorm");
let Library = class Library extends BaseEntity_1.BaseEntity {
};
exports.Library = Library;
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Library.prototype, "file_name", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Library.prototype, "file_path", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Library.prototype, "file_type", void 0);
exports.Library = Library = __decorate([
    (0, typeorm_1.Entity)({ name: "library" }),
    (0, typeorm_1.Unique)("file_name_unique", ["file_name"])
], Library);
//# sourceMappingURL=library.entity.js.map