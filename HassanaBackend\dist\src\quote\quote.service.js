"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuoteService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const quote_entity_1 = require("./entities/quote.entity");
const typeorm_2 = require("typeorm");
let QuoteService = class QuoteService {
    constructor(quoteRepository) {
        this.quoteRepository = quoteRepository;
    }
    async create(createQuoteInput) {
        try {
            const existingQuote = await this.quoteRepository.findOne({
                where: { visibilityStart: (0, typeorm_2.Equal)(createQuoteInput.visibilityStart) },
            });
            if (!existingQuote) {
                const response = await this.quoteRepository.save(createQuoteInput);
                return response;
            }
            throw new Error('quote alreay exist on this visibility');
        }
        catch (error) {
            console.log('error in service', error);
            return error;
        }
    }
    async findAll() {
        try {
            let response = await this.quoteRepository.find();
            return response;
        }
        catch (error) {
            console.log(error.message);
            throw Error(error);
        }
    }
    async findOne(id) {
        return `This action returns a #${id} quote`;
    }
    async update(id, updateQuoteInput) {
        try {
            let oldData = await this.quoteRepository.findOne({ where: { id: id } });
            console.log(oldData);
            if (!oldData)
                throw new common_1.NotFoundException('Quote not found');
            if (oldData) {
                this.quoteRepository.merge(oldData, updateQuoteInput);
                let newData = await this.quoteRepository.save(updateQuoteInput);
                return newData;
            }
        }
        catch (error) {
            throw new Error(error.message);
        }
    }
    async remove(id) {
        try {
            let data = await this.quoteRepository.findOne({ where: { id: id } });
            if (!data)
                throw new common_1.NotFoundException('Quote not found');
            if (data) {
                return await this.quoteRepository.remove(data);
            }
        }
        catch (error) {
            throw new Error(error.message);
        }
    }
    async findByVisibility() {
        const todaysDate = new Date();
        const dateStringify = todaysDate.toISOString();
        const splitting = dateStringify.split('T');
        const today = `${splitting[0]}`;
        const result = await this.quoteRepository.findOne({
            where: {
                visibilityStart: (0, typeorm_2.LessThanOrEqual)(today),
                visibilityEnd: (0, typeorm_2.MoreThanOrEqual)(today),
                status: true
            },
            order: {
                visibilityStart: 'DESC',
            },
        });
        console.log(result);
        return result;
    }
};
exports.QuoteService = QuoteService;
exports.QuoteService = QuoteService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(quote_entity_1.QuoteEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], QuoteService);
//# sourceMappingURL=quote.service.js.map