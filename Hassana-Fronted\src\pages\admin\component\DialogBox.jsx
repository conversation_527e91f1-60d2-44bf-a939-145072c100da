import * as React from "react";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogContentText from "@mui/material/DialogContentText";
import DialogTitle from "@mui/material/DialogTitle";
import { Cancel, Close } from "@mui/icons-material";
import { IconButton, useTheme } from "@mui/material";
import Add from "@mui/icons-material/Add";

export default function ScrollDialog({ title, comp, btn, open, setOpen }) {
  // const [open, setOpen] = React.useState(false);
  const theme = useTheme();

  const handleClickOpen = () => {
    setOpen(true);
    // modalHandler()
  };

  const handleClose = () => {
    setOpen(false);
    // modalHandler()
  };
  return (
    <React.Fragment>
      {btn && (
        <Button
          color="white"
          size="small"
          style={{
            backgroundColor: theme.palette.text.purple,
            height: "40px",
          }}
          onClick={handleClickOpen}
        >
          {" "}
          {title}
        </Button>
      )}
      <Dialog
        open={open}
        onClose={handleClose}
        // scroll={scroll}
        aria-labelledby="scroll-dialog-title"
        aria-describedby="scroll-dialog-description"
      >
        <DialogTitle
          id="scroll-dialog-title"
          sx={{
            display: "flex",
            justifyContent: "space-between",
            background: theme.palette.background.primary,
          }}
        >
          {title}
          <IconButton
            onClick={handleClose}
            sx={{ color: theme.palette.text.primary }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent
          dividers={true}
          sx={{ background: theme.palette.background.secondary }}
        >
          {comp}
        </DialogContent>
        {/* <DialogActions>
                    <Button onClick={handleClose}>Cancel</Button>
                    <Button onClick={handleClose}>Submit</Button>
                </DialogActions> */}
      </Dialog>
    </React.Fragment>
  );
}
export function UpdateModal({ title, comp, btn, openUpdate, setOpenUpdate }) {
  const theme = useTheme();
  const handleClose = () => {
    setOpenUpdate(false);
  };

  return (
    <React.Fragment>
      <Dialog
        open={openUpdate}
        onClose={handleClose}
        // scroll={"paper"}
        aria-labelledby="scroll-dialog-title"
        aria-describedby="scroll-dialog-description"
      >
        <DialogTitle
          id="scroll-dialog-title"
          sx={{
            display: "flex",
            justifyContent: "space-between",
            background: theme.palette.background.primary,
          }}
        >
          {title}
          <IconButton
            onClick={handleClose}
            sx={{ color: theme.palette.text.primary }}
          >
            <Close />
          </IconButton>
        </DialogTitle>
        <DialogContent
          dividers={true}
          sx={{ background: theme.palette.background.secondary }}
        >
          {comp}
        </DialogContent>
        {/* <DialogActions>
                    <Button onClick={handleClose}>Cancel</Button>
                    <Button onClick={handleClose}>Submit</Button>
                </DialogActions> */}
      </Dialog>
    </React.Fragment>
  );
}
