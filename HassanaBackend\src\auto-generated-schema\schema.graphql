# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type UserSchema {
  id: String!
  profile: String
  email: String
  name: String
  name_arabic: String
  designation: String
  designation_arabic: String
  department: String
  department_arabic: String
  bio_link: String
  new_joiner: String
  status: String!
  is_cultural_ambassador: String
  dn: String
  gender: String
  account_expires: String
  user_principal_name: String
  role: String!
  monthly_rating: Float!
  yearly_rating: Float!
  createdAt: DateTime
  updatedAt: DateTime
  activity: String
  extesion: String
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type LoginUser {
  id: String!
  username: String!
  role: String!
  token: String!
}

type UserMeta {
  totalCount: Int!
  currentPage: Int!
  pageSize: Int!
  totalPages: Int!
}

type UserPaginationSchema {
  users: [UserSchema!]!
  meta: UserMeta!
}

type BookingSchema {
  id: ID!
  title: String!
  details: String
  uid: String!
  registrationDoc: String
  location: String
  userId: ID!
  start: DateTime!
  end: DateTime!
  teaBoy: String!
  parking: String!
  itTechnician: String!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Event {
  id: ID!
  title: String!
  details: String!
  category: String!
  status: Boolean!
  date: DateTime!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type ResourceSchema {
  id: ID!
  name: String!
  type: String!
}

type QuoteSchema {
  id: ID
  quote: String!
  author: String!
  status: Boolean!
  visibilityStart: String!
  visibilityEnd: String!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type NotificationSchema {
  id: ID!
  notification: String!
  createdAt: DateTime!
  views: [UserSchema!]
}

type Leave {
  id: ID!
  userid: ID!
  username: String!
  remarks: String!
  date: DateTime!
  numberOfDays: Int!
  typeOfLeave: String!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type LeaveCount {
  medical: Int!
  casual: Int!
}

type OffersSchema {
  id: String
  name: String!
  contact_information: String
  code: String!
  expiry_date: DateTime
  description: String
  status: Boolean
  is_read: Boolean
  createdAt: DateTime
  created_by: String
  updatedAt: DateTime
  updated_by: String
}

type Query {
  users(page: Float!, pageSize: Float!): UserPaginationSchema!
  getNewUsers(days: Float!): [UserSchema!]!
  getCulturalAmbassadors: [UserSchema!]!
  loginUser(username: String!, password: String!): LoginUser!
  findUserById(id: String!): UserSchema!
  bookings: [BookingSchema!]!
  bookingsOfTeaBoy: [BookingSchema!]!
  bookingsOfUser(id: ID!): [BookingSchema!]!
  allResources: [ResourceSchema!]!
  singleResource(id: ID!): ResourceSchema!
  events: [Event!]!
  event(id: ID!): Event!
  todaysEvents(date: DateTime!, category: String!): [Event!]!
  findAllQuote: [QuoteSchema!]!
  findOneQuote(id: ID!): QuoteSchema!
  todaysQuote: QuoteSchema!
  notifications: [NotificationSchema!]!
  notificationViews: [NotificationSchema!]!
  newNotificationsForUser(id: ID!): [NotificationSchema!]!
  unseenNotificationsCount(userId: ID!): ID!
  notification(id: ID!): NotificationSchema!
  leaves: [Leave!]!
  getUserLeaves(id: ID!): LeaveCount!
  leave(id: ID!): Leave!
  offers(user_id: String!): [OffersSchema!]!
  validOffers(user_id: String!): [OffersSchema!]!
}

type Mutation {
  createBooking(CreateBookingInput: UpdateBookingInput!): BookingSchema!
  createResource(createResourceInput: CreateResourceInput!): ResourceSchema!
  updateResource(updateResourceInput: UpdateResourceInput!): ResourceSchema!
  removeResource(id: ID!): ResourceSchema!
  createEvent(createEventInput: CreateEventInput!): Event!
  updateEvent(id: ID!, updateEventInput: UpdateEventInput!): Event!
  removeEvent(id: ID!): Event!
  createQuote(createQuoteInput: CreateQuoteInput!): QuoteSchema!
  updateQuote(id: ID!, updateQuoteInput: UpdateQuoteInput!): QuoteSchema!
  removeQuote(id: ID!): QuoteSchema!
  createNotification(createNotificationInput: CreateNotificationInput!): NotificationSchema!
  addNotificationView(notificationId: ID!, userId: ID!): NotificationSchema!
  markAllNotificationsAsSeen(userId: ID!): Boolean!
  updateNotification(id: ID!, updateNotificationInput: UpdateNotificationInput!): NotificationSchema!
  removeNotification(id: ID!): NotificationSchema!
  createLeave(createLeaveInput: CreateLeaveInput!): Leave!
  updateLeave(updateLeaveInput: UpdateLeaveInput!): Leave!
  removeLeave(id: ID!): Leave!
  createOffer(createOffersInput: CreateOffersInput!): OffersSchema!
  offerView(offer_id: String!, user_id: String!): OffersSchema!
  updateOffer(id: ID!, updateOffersInput: UpdateOffersInput!): OffersSchema!
  removeOffer(id: ID!): OffersSchema!
}

input UpdateBookingInput {
  title: String
  details: String
  uid: String
  registrationDoc: String
  location: String
  userId: ID
  teaBoy: String
  parking: String
  itTechnician: String
  start: DateTime
  end: DateTime
}

input CreateResourceInput {
  name: String!
  type: String!
}

input UpdateResourceInput {
  name: String
  type: String
  id: ID!
}

input CreateEventInput {
  title: String!
  details: String!
  category: String!
  status: Boolean!
  date: DateTime!
}

input UpdateEventInput {
  title: String
  details: String
  category: String
  status: Boolean
  date: DateTime
}

input CreateQuoteInput {
  quote: String!
  author: String!
  status: Boolean!
  visibilityStart: String!
  visibilityEnd: String!
}

input UpdateQuoteInput {
  quote: String
  author: String
  status: Boolean
  visibilityStart: String
  visibilityEnd: String
}

input CreateNotificationInput {
  notification: String!
}

input UpdateNotificationInput {
  notification: String!
}

input CreateLeaveInput {
  userId: ID!
  username: String!
  remarks: String!
  date: DateTime!
  numberOfDays: Int!
  typeOfLeave: String!
}

input UpdateLeaveInput {
  userId: ID
  username: String
  remarks: String
  date: DateTime
  numberOfDays: Int
  typeOfLeave: String
  id: ID!
}

input CreateOffersInput {
  name: String!
  contact_information: String!
  code: String!
  expiry_date: DateTime!
  description: String
  status: Boolean
  created_by: String
  updated_by: String
}

input UpdateOffersInput {
  name: String
  contact_information: String
  code: String
  expiry_date: DateTime
  description: String
  status: Boolean
  created_by: String
  updated_by: String
}