import { BookingService } from './booking.service';
export declare class BookingController {
    private readonly bookingService;
    constructor(bookingService: BookingService);
    createBooking(file: Express.Multer.File, body: any): Promise<{
        code: number;
        message: string;
        data: import("./entities/booking.entity").Booking;
    } | {
        code: any;
        message: any;
        error: any;
    }>;
}
