"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUser = void 0;
const graphql_1 = require("@nestjs/graphql");
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
let CreateUser = class CreateUser {
};
exports.CreateUser = CreateUser;
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CreateUser.prototype, "profile", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CreateUser.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CreateUser.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CreateUser.prototype, "name_arabic", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CreateUser.prototype, "designation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CreateUser.prototype, "designation_arabic", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CreateUser.prototype, "department", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CreateUser.prototype, "department_arabic", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CreateUser.prototype, "bio_link", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)({ nullable: true }),
    __metadata("design:type", String)
], CreateUser.prototype, "new_joiner", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)({ nullable: false }),
    __metadata("design:type", String)
], CreateUser.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CreateUser.prototype, "dn", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CreateUser.prototype, "gender", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CreateUser.prototype, "account_expires", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CreateUser.prototype, "is_cultural_ambassador", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CreateUser.prototype, "user_principal_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)(),
    (0, graphql_1.Field)(),
    __metadata("design:type", String)
], CreateUser.prototype, "role", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateUser.prototype, "activity", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateUser.prototype, "extension", void 0);
exports.CreateUser = CreateUser = __decorate([
    (0, graphql_1.InputType)()
], CreateUser);
//# sourceMappingURL=create-user.input.js.map