import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Box, Button, Typography } from '@mui/material';
import { useColor } from "@/components/ColorContext";
import { useSelectedColor } from "@/components/HelperFunctions";
import { useTheme } from "@mui/material/styles";

const QuickLinks = () => {
  const [activeButton, setActiveButton] = useState(null);
   const router = useRouter()
  const handleClick = (index) => {
    setActiveButton(index);
    performAction(index);
  };
  const theme = useTheme();
  const { color } = useColor();

  const performAction = (index) => {
    switch (index) {
      case 0:
        console.log("Home button clicked");
        router.push('/')
       
        break;
      case 1:
        console.log("News & Announcement button clicked");
        router.push('/news')
        break;
      case 2:
        console.log("Hassana Library button clicked");
        router.push('/hassanalibrary')

        break;
      case 3:
        console.log("Notifications button clicked");
        router.push('/notifications')
        
        break;
      case 4:
        console.log("Employees button clicked");
        router.push('/')

        break;
      case 5:
        console.log("My Calendar button clicked");
        router.push('/allocator')

        break;
      default:
        console.log("Unknown button clicked");
        router.push('/')

    }
  };
    //   #1B3745
    const selectedColor = useSelectedColor(color);

  return (
    
    <Box sx={{ textAlign: 'center', paddingBottom:3, mb: 5,
        borderRadius: 2,
                      color:
                        selectedColor == theme.palette.background.primary
                          ? theme.palette.text.primary
                          : theme.palette.text.black,

     }}>
      <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2 }}>Quick Links</Typography>
      <Box sx={{ display: 'flex', fontSize: '20px', flexWrap: 'wrap', gap: 1, justifyContent: 'center' }}>
        {["Home", "News & Announcement", "Hassana Library", "Notifications", "Employees", "My Calendar"].map((label, index) => (
          <Button
            key={label}
            variant="outlined"
            onClick={() => handleClick(index)}
            sx={{
              ...buttonStyle,
              background: selectedColor === '#00BC82'? '#00BC82 !important': selectedColor === '#62B6F3'? '#62B6F3 !important': selectedColor === '#A665E1'? '#A665E1 !important': '#FCFAFF',
              color: activeButton === index ? '#FCFAFF' : '#A665E1' ,
              color:selectedColor==='#FCFAFF'? '#A665E1 !important': '#FCFAFF' ,
              borderColor: selectedColor==='#FCFAFF'?'#A665E1 !important':selectedColor,
            }}
          >
            {label}
          </Button>
        ))}
      </Box>
    </Box>
  );
};

const buttonStyle = {
  borderRadius: 2,
  paddingX: 1,
  paddingY: 1,
};

export default QuickLinks;
