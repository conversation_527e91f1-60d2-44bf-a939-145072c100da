
import { gql } from "@apollo/client";
import { baseUrl } from "./ApolloClient";

import axios from "axios";
import formData from 'form-data';

export const MutationCreateBooking = gql`
mutation CreateBooking(
    # $id: ID!
    $title: String!
    $userId: ID!
    # $resourceId: ID!
    $details: String!
    $location: String
    $teaBoy: String!
    $parking: String!
    $itTechnician: String!
    $start: DateTime!
    $uid:String!
    # $status:String!
    $end: DateTime!
  ) {
    createBooking(CreateBookingInput: {
      # id: $id
      title: $title
      userId: $userId
      teaBoy: $teaBoy
      location: $location
      parking: $parking
      itTechnician: $itTechnician
      details: $details
      uid: $uid
      start: $start
      end: $end
    }) {
      id,
      title,
      userId,
      details,
      teaBoy,
      location,
      parking,
      uid,
      itTechnician,
      start,
      end
    }
  }
  `;

export const createBooking = async (FormData, isImageChanged) => {
  try {
    let data = new formData();
    // Wrap all booking fields in a CreateBookingInput object as string
    const bookingInput = {
      title: FormData.title,
      userId: FormData.userId,
      teaBoy: FormData.teaBoy,
      location: FormData.location,
      parking: FormData.parking,
      itTechnician: FormData.itTechnician,
      details: FormData.details,
      uid: FormData.uid,
      start: FormData.start,
      end: FormData.end
    };
console.log("?????????????????????????????",bookingInput)
    data.append('CreateBookingInput', JSON.stringify(bookingInput));
    if (isImageChanged) {
      data.append('registrationDoc', FormData.registrationDoc);
    }

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${baseUrl}/${'v1/our-booking'}`,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      data: data
    };

    let res = await axios.request(config);
    console.log(res.data);
    return res.data;
  } catch (error) {
    console.log(error);
    return error.message;
  }
}


export const getBookings = gql`
  query {
    bookings {
        id,
        title,
        details,
        status,
        user{
          id,
          name
        }
        resource{
          id,
          name,
          type
        }
        startTime,
        endTime,
        updatedAt
    }
  }
`;
// export const getBookings = gql`
//   query {
//     bookings {
//         id,
//         title,
//         user_id,
//         resourceId,
//         startTime,
//         endTime
//     }
//   }
// `;

export const GET_BOOKINGS_OF_USER = gql`
  query BookingsOfUser($userId: ID!) {
    bookingsOfUser(id: $userId) {
      id
      title
      start
      end
      details
      parking
      teaBoy
      location
      itTechnician
      registrationDoc
      uid
      userId
    }
  }
`;

export const GET_BOOKINGS_OF_TEA_BOY = gql`
  query {
    bookingsOfTeaBoy {
      id
      title
      start
      end
      details
      registrationDoc
      parking
      teaBoy
      location
      itTechnician
      uid
      userId
    }
  }
`;

export const mutationUpdateBookingStatus = gql`
  mutation UpdateBookingStatus(
    $id: ID!
    $status: String!
  ) {
    updateBookingStatus(updateBookingInput: {
        id: $id
        status: $status
    }) {
        id,
        title,
        details,
        status,
        user{
          id,
          name
        }
        resource{
          id,
          name,
          type
        }
        startTime,
        endTime,
        updatedAt
    }
}
`;
