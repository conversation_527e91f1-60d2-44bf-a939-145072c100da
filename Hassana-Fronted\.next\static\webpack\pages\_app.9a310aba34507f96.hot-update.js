"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/Data/ApolloClient.js":
/*!**********************************!*\
  !*** ./src/Data/ApolloClient.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baseUrl: function() { return /* binding */ baseUrl; },\n/* harmony export */   base_url: function() { return /* binding */ base_url; }\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _apollo_client_link_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client/link/context */ \"./node_modules/@apollo/client/link/context/index.js\");\n/* harmony import */ var _apollo_client_link_error__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @apollo/client/link/error */ \"./node_modules/@apollo/client/link/error/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_csrfUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/csrfUtils */ \"./src/utils/csrfUtils.js\");\n\n\n\n\n\n//export const baseUrl = \"http://*************:3001/v1\";\n//export const baseUrl = \"https://portal.hassana.com.sa/v1\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com/v1\";\n////export const baseUrl = \"http://localhost:3001\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com\";\nconst baseUrl = \"https://v2-portal.hassana.com.sa\";\nconst base_url = \"https://v2-portal.hassana.com.sa/v1\";\n//export const base_url = \"https://localhost:3001/v1\";\nconst httpLink = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_2__.createHttpLink)({\n    uri: baseUrl + \"/graphql\"\n});\n// Auth link to add JWT token and CSRF headers\nconst authLink = (0,_apollo_client_link_context__WEBPACK_IMPORTED_MODULE_3__.setContext)(async (_, param)=>{\n    let { headers } = param;\n    // Get token from session or localStorage\n    let token = null;\n    try {\n        const session = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.getSession)();\n        token = session === null || session === void 0 ? void 0 : session.accessToken;\n    } catch (error) {\n        console.warn(\"Could not get session:\", error);\n    }\n    // Fallback to localStorage if session token not available\n    if (!token && \"object\" !== \"undefined\") {\n        token = localStorage.getItem(\"jwtToken\");\n    }\n    // Get CSRF headers for secure requests\n    let csrfHeaders = {};\n    try {\n        csrfHeaders = await (0,_utils_csrfUtils__WEBPACK_IMPORTED_MODULE_1__.getSecureHeaders)();\n    } catch (error) {\n        console.warn(\"Could not get CSRF headers:\", error);\n    }\n    return {\n        headers: {\n            ...headers,\n            ...token && {\n                authorization: \"Bearer \".concat(token)\n            },\n            ...csrfHeaders\n        }\n    };\n});\n// Error link to handle GraphQL and network errors\nconst errorLink = (0,_apollo_client_link_error__WEBPACK_IMPORTED_MODULE_4__.onError)((param)=>{\n    let { graphQLErrors, networkError } = param;\n    if (graphQLErrors) {\n        graphQLErrors.forEach((param)=>{\n            let { message, locations, path, extensions } = param;\n            console.error(\"[GraphQL error]: Message: \".concat(message, \", Location: \").concat(locations, \", Path: \").concat(path));\n            // Handle authentication errors\n            if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\")) {\n                console.warn(\"Authentication error detected, redirecting to login...\");\n                // Clear invalid token\n                if (true) {\n                    localStorage.removeItem(\"jwtToken\");\n                }\n                // Redirect to login page\n                if (true) {\n                    window.location.href = \"/login?login=false\";\n                }\n            }\n            // Handle authorization errors\n            if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\")) {\n                console.warn(\"Authorization error detected\");\n                // You can show a toast or redirect to unauthorized page\n                if (true) {\n                    // You can implement a toast notification here\n                    console.error(\"Access denied: Insufficient permissions\");\n                }\n            }\n        });\n    }\n    if (networkError) {\n        console.error(\"[Network error]: \".concat(networkError));\n        // Handle network errors that might indicate auth issues\n        if (networkError.statusCode === 401) {\n            console.warn(\"Network 401 error, clearing token and redirecting...\");\n            if (true) {\n                localStorage.removeItem(\"jwtToken\");\n                window.location.href = \"/login?login=false\";\n            }\n        }\n    }\n});\nconst client = new _apollo_client__WEBPACK_IMPORTED_MODULE_2__.ApolloClient({\n    link: (0,_apollo_client__WEBPACK_IMPORTED_MODULE_2__.from)([\n        errorLink,\n        authLink,\n        httpLink\n    ]),\n    cache: new _apollo_client__WEBPACK_IMPORTED_MODULE_2__.InMemoryCache(),\n    defaultOptions: {\n        watchQuery: {\n            errorPolicy: \"all\"\n        },\n        query: {\n            errorPolicy: \"all\"\n        }\n    }\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (client);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/Data/ApolloClient.js\n"));

/***/ })

});