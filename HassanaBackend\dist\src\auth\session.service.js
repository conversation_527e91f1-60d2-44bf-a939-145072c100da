"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var SessionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionService = void 0;
const common_1 = require("@nestjs/common");
const redis_1 = require("../../redis");
const jwt = require("jsonwebtoken");
let SessionService = SessionService_1 = class SessionService {
    constructor() {
        this.logger = new common_1.Logger(SessionService_1.name);
        this.redis = redis_1.redis;
    }
    async createUserSession(userId, tokenPayload, deviceInfo) {
        const JWT_KEY = process.env.JWT_KEY;
        const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
        if (!JWT_KEY) {
            throw new Error('JWT secret key not configured');
        }
        const sessionId = this.generateSessionId();
        const enhancedPayload = {
            ...tokenPayload,
            sessionId,
            loginTime: new Date().toISOString(),
            deviceInfo: deviceInfo || 'Unknown'
        };
        const token = jwt.sign(enhancedPayload, JWT_KEY, { expiresIn: JWT_EXPIRES_IN });
        const sessionKey = `user_session:${userId}`;
        const sessionData = {
            sessionId,
            token,
            userId,
            loginTime: enhancedPayload.loginTime,
            deviceInfo: enhancedPayload.deviceInfo,
            isActive: 'true'
        };
        const existingSession = await this.redis.hgetall(sessionKey);
        if (existingSession && existingSession.sessionId) {
            this.logger.warn(`User ${userId} already has an active session. Invalidating previous session.`);
            await this.blacklistToken(existingSession.token);
        }
        await this.redis.hmset(sessionKey, sessionData);
        await this.redis.expire(sessionKey, 24 * 60 * 60);
        this.logger.log(`New session created for user ${userId} with session ID: ${sessionId}`);
        return token;
    }
    async validateUserSession(userId, sessionId, token) {
        try {
            const isBlacklisted = await this.isTokenBlacklisted(token);
            if (isBlacklisted) {
                this.logger.warn(`Blacklisted token used for user ${userId}`);
                return false;
            }
            const sessionKey = `user_session:${userId}`;
            const currentSession = await this.redis.hgetall(sessionKey);
            if (!currentSession || !currentSession.sessionId) {
                this.logger.warn(`No active session found for user ${userId}`);
                return false;
            }
            if (currentSession.sessionId !== sessionId) {
                this.logger.warn(`Session ID mismatch for user ${userId}. Current: ${currentSession.sessionId}, Provided: ${sessionId}`);
                await this.blacklistToken(token);
                return false;
            }
            if (currentSession.isActive !== 'true') {
                this.logger.warn(`Inactive session for user ${userId}`);
                return false;
            }
            return true;
        }
        catch (error) {
            this.logger.error(`Error validating session for user ${userId}:`, error);
            return false;
        }
    }
    async logoutUser(userId, token) {
        try {
            const sessionKey = `user_session:${userId}`;
            await this.redis.del(sessionKey);
            await this.blacklistToken(token);
            this.logger.log(`User ${userId} logged out successfully`);
        }
        catch (error) {
            this.logger.error(`Error during logout for user ${userId}:`, error);
            throw error;
        }
    }
    async getUserSession(userId) {
        const sessionKey = `user_session:${userId}`;
        const session = await this.redis.hgetall(sessionKey);
        if (!session || !session.sessionId) {
            return null;
        }
        return {
            sessionId: session.sessionId,
            loginTime: session.loginTime,
            deviceInfo: session.deviceInfo,
            isActive: session.isActive === 'true'
        };
    }
    async forceLogoutUser(userId) {
        try {
            const sessionKey = `user_session:${userId}`;
            const session = await this.redis.hgetall(sessionKey);
            if (session && session.token) {
                await this.blacklistToken(session.token);
            }
            await this.redis.del(sessionKey);
            this.logger.log(`Force logout completed for user ${userId}`);
        }
        catch (error) {
            this.logger.error(`Error during force logout for user ${userId}:`, error);
            throw error;
        }
    }
    async blacklistToken(token) {
        try {
            const decoded = jwt.decode(token);
            if (decoded && decoded.exp) {
                const blacklistKey = `blacklist:${token}`;
                const ttl = decoded.exp - Math.floor(Date.now() / 1000);
                if (ttl > 0) {
                    await this.redis.setex(blacklistKey, ttl, 'true');
                    this.logger.log(`Token blacklisted with TTL: ${ttl} seconds`);
                }
            }
        }
        catch (error) {
            this.logger.error('Error blacklisting token:', error);
        }
    }
    async isTokenBlacklisted(token) {
        try {
            const blacklistKey = `blacklist:${token}`;
            const result = await this.redis.get(blacklistKey);
            return result === 'true';
        }
        catch (error) {
            this.logger.error('Error checking blacklist:', error);
            return false;
        }
    }
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    async getAllActiveSessions() {
        try {
            const keys = await this.redis.keys('user_session:*');
            const sessions = [];
            for (const key of keys) {
                const session = await this.redis.hgetall(key);
                if (session && session.isActive === 'true') {
                    sessions.push({
                        userId: key.replace('user_session:', ''),
                        ...session
                    });
                }
            }
            return sessions;
        }
        catch (error) {
            this.logger.error('Error getting active sessions:', error);
            return [];
        }
    }
};
exports.SessionService = SessionService;
exports.SessionService = SessionService = SessionService_1 = __decorate([
    (0, common_1.Injectable)()
], SessionService);
//# sourceMappingURL=session.service.js.map