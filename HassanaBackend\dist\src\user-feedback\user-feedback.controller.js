"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserFeedbackController = void 0;
const common_1 = require("@nestjs/common");
const user_feedback_service_1 = require("./user-feedback.service");
const create_user_feedback_dto_1 = require("./dto/create-user-feedback.dto");
const jwt_guard_1 = require("../auth/jwt.guard");
const swagger_1 = require("@nestjs/swagger");
let UserFeedbackController = class UserFeedbackController {
    constructor(userFeedbackService) {
        this.userFeedbackService = userFeedbackService;
    }
    async create(createUserFeedbackDto, req) {
        try {
            const { id } = req['user'];
            console.log(id);
            let data = await this.userFeedbackService.create(createUserFeedbackDto, id);
            return {
                status: true,
                message: "Success",
                data: data
            };
        }
        catch (error) {
            return {
                status: false,
                message: "Internal Server Error",
                errorMessage: error.message
            };
        }
        ;
    }
    async findAll() {
        try {
            let data = await this.userFeedbackService.findAll();
            return {
                status: true,
                message: "Success",
                data: data
            };
        }
        catch (error) {
            return {
                status: false,
                message: "Internal Server Error",
                errorMessage: error.message
            };
        }
    }
    async remove(id) {
        try {
            let data = await this.userFeedbackService.remove(id);
            return {
                status: true,
                message: "Success",
                data: data
            };
        }
        catch (error) {
            return {
                status: false,
                message: "Internal Server Error",
                errorMessage: error.message
            };
        }
    }
};
exports.UserFeedbackController = UserFeedbackController;
__decorate([
    (0, common_1.UseGuards)(jwt_guard_1.JwtGuard),
    (0, common_1.Post)(),
    (0, swagger_1.ApiBody)({ type: create_user_feedback_dto_1.CreateUserFeedbackDto }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_feedback_dto_1.CreateUserFeedbackDto, Request]),
    __metadata("design:returntype", Promise)
], UserFeedbackController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UserFeedbackController.prototype, "findAll", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserFeedbackController.prototype, "remove", null);
exports.UserFeedbackController = UserFeedbackController = __decorate([
    (0, common_1.Controller)('v1/user-feedback'),
    __metadata("design:paramtypes", [user_feedback_service_1.UserFeedbackService])
], UserFeedbackController);
//# sourceMappingURL=user-feedback.controller.js.map