import { gql } from "@apollo/client";

export const getResources = gql`
  query {
    allResources{
        id
        name
        type
    }
  }
`;

// mutation{
//   createResource(createResourceInput:{
//     name:"guest parking",
//     type:"parking"
//   }){
//     id
//   }
// }

export const mutationCreateResource = gql`
mutation CreateResource(
  $name: String!
  $type: String!
) {
  createResource(createResourceInput: {
    name: $name
    type: $type
  }) {
    id,
    name,
    type
  }
}
`;

export const mutationUpdateResource = gql`
  mutation UpdateResource(
    $id: Int!
    $name: String!
    $type: String!
  ) {
    updateResource(updateResourceInput: {
      id: $id
      name: $name
        type: $type
    }) {
        id
        name
        type
    }
}
`;
