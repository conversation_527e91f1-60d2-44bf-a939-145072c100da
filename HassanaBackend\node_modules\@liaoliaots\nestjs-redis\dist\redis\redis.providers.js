"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mergedOptionsProvider = exports.redisClientsProvider = exports.createAsyncOptionsProvider = exports.createAsyncOptions = exports.createAsyncProviders = exports.createOptionsProvider = void 0;
const redis_constants_1 = require("./redis.constants");
const common_1 = require("./common");
const default_options_1 = require("./default-options");
const createOptionsProvider = (options) => ({
    provide: redis_constants_1.REDIS_OPTIONS,
    useValue: options
});
exports.createOptionsProvider = createOptionsProvider;
const createAsyncProviders = (options) => {
    if (options.useClass) {
        return [
            {
                provide: options.useClass,
                useClass: options.useClass
            },
            (0, exports.createAsyncOptionsProvider)(options)
        ];
    }
    if (options.useExisting || options.useFactory)
        return [(0, exports.createAsyncOptionsProvider)(options)];
    return [];
};
exports.createAsyncProviders = createAsyncProviders;
const createAsyncOptions = async (optionsFactory) => {
    return await optionsFactory.createRedisOptions();
};
exports.createAsyncOptions = createAsyncOptions;
const createAsyncOptionsProvider = (options) => {
    if (options.useFactory) {
        return {
            provide: redis_constants_1.REDIS_OPTIONS,
            useFactory: options.useFactory,
            inject: options.inject
        };
    }
    if (options.useClass) {
        return {
            provide: redis_constants_1.REDIS_OPTIONS,
            useFactory: exports.createAsyncOptions,
            inject: [options.useClass]
        };
    }
    if (options.useExisting) {
        return {
            provide: redis_constants_1.REDIS_OPTIONS,
            useFactory: exports.createAsyncOptions,
            inject: [options.useExisting]
        };
    }
    return {
        provide: redis_constants_1.REDIS_OPTIONS,
        useValue: {}
    };
};
exports.createAsyncOptionsProvider = createAsyncOptionsProvider;
exports.redisClientsProvider = {
    provide: redis_constants_1.REDIS_CLIENTS,
    useFactory: (options) => {
        const clients = new Map();
        if (Array.isArray(options.config)) {
            options.config.forEach(item => clients.set(item.namespace ?? redis_constants_1.DEFAULT_REDIS, (0, common_1.createClient)({ ...options.commonOptions, ...item }, { readyLog: options.readyLog, errorLog: options.errorLog })));
        }
        else if (options.config) {
            clients.set(options.config.namespace ?? redis_constants_1.DEFAULT_REDIS, (0, common_1.createClient)(options.config, { readyLog: options.readyLog, errorLog: options.errorLog }));
        }
        return clients;
    },
    inject: [redis_constants_1.REDIS_MERGED_OPTIONS]
};
exports.mergedOptionsProvider = {
    provide: redis_constants_1.REDIS_MERGED_OPTIONS,
    useFactory: (options) => ({ ...default_options_1.defaultRedisModuleOptions, ...options }),
    inject: [redis_constants_1.REDIS_OPTIONS]
};
