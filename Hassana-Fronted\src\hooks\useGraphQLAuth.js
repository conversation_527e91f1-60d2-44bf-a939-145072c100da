import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';

/**
 * Custom hook to handle GraphQL authentication errors
 * @param {Object} error - Apollo GraphQL error object
 * @param {Function} onAuthError - Optional callback for auth errors
 * @param {Function} onForbiddenError - Optional callback for forbidden errors
 */
export const useGraphQLAuth = (error, onAuthError, onForbiddenError) => {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (error) {
      // Check for GraphQL errors
      if (error.graphQLErrors && error.graphQLErrors.length > 0) {
        error.graphQLErrors.forEach((gqlError) => {
          const { message, extensions } = gqlError;
          
          // Handle authentication errors
          if (
            extensions?.code === 'UNAUTHENTICATED' ||
            message.includes('Unauthorized') ||
            message.includes('Authorization header not found') ||
            message.includes('Token has expired') ||
            message.includes('Invalid token')
          ) {
            console.warn('GraphQL Authentication error:', message);
            
            // Clear invalid token
            if (typeof window !== 'undefined') {
              localStorage.removeItem("jwtToken");
            }
            
            // Call custom auth error handler if provided
            if (onAuthError) {
              onAuthError(gqlError);
            } else {
              // Default behavior: redirect to login
              router.push('/login?login=false&reason=auth_expired');
            }
          }
          
          // Handle authorization/forbidden errors
          else if (
            extensions?.code === 'FORBIDDEN' ||
            message.includes('Access denied') ||
            message.includes('Forbidden') ||
            message.includes('Required role')
          ) {
            console.warn('GraphQL Authorization error:', message);
            
            // Call custom forbidden error handler if provided
            if (onForbiddenError) {
              onForbiddenError(gqlError);
            } else {
              // Default behavior: show error or redirect to unauthorized page
              console.error('Access denied: Insufficient permissions');
              // You could redirect to an unauthorized page here
              // router.push('/unauthorized');
            }
          }
        });
      }
      
      // Check for network errors
      if (error.networkError) {
        const { statusCode } = error.networkError;
        
        if (statusCode === 401) {
          console.warn('Network 401 error - Authentication required');
          
          // Clear invalid token
          if (typeof window !== 'undefined') {
            localStorage.removeItem("jwtToken");
          }
          
          if (onAuthError) {
            onAuthError(error.networkError);
          } else {
            router.push('/login?login=false&reason=network_auth');
          }
        } else if (statusCode === 403) {
          console.warn('Network 403 error - Access forbidden');
          
          if (onForbiddenError) {
            onForbiddenError(error.networkError);
          } else {
            console.error('Access forbidden');
          }
        }
      }
    }
  }, [error, router, onAuthError, onForbiddenError]);

  return {
    isAuthenticated: status === 'authenticated',
    isLoading: status === 'loading',
    session
  };
};

/**
 * Helper function to get authentication headers for GraphQL requests
 * @param {Object} session - NextAuth session object
 * @returns {Object} Headers object with authorization
 */
export const getAuthHeaders = (session) => {
  const token = session?.accessToken || (typeof window !== 'undefined' ? localStorage.getItem("jwtToken") : null);
  
  return token ? {
    authorization: `Bearer ${token}`
  } : {};
};

/**
 * Helper function to check if user has required role
 * @param {Object} session - NextAuth session object
 * @param {string} requiredRole - Required role (ADMIN, USER)
 * @returns {boolean} Whether user has the required role
 */
export const hasRequiredRole = (session, requiredRole) => {
  if (!session?.user?.role) return false;
  
  const userRole = session.user.role.toUpperCase();
  const required = requiredRole.toUpperCase();
  
  return userRole === required;
};

/**
 * Helper function to create GraphQL context with auth headers
 * @param {Object} session - NextAuth session object
 * @returns {Object} Context object for Apollo queries/mutations
 */
export const createAuthContext = (session) => ({
  headers: getAuthHeaders(session)
});
