import Dashboard from "@/components/Dashboard";
import Layout from "@/components/Layout";
import {
  Box,
  Button,
  CircularProgress,
  Grid,
  IconButton,
  Input,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import DataTable from "./component/DataTable";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import BasicModal, { UpdateModal } from "./component/DialogBox";
import { useEffect, useState } from "react";
import { PhotoCamera, Cancel } from "@mui/icons-material";
import { DatePicker } from "@mui/lab";
import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";
import { DateTimeField } from "@mui/x-date-pickers/DateTimeField";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
// import { DesktopDatePicker } from "@mui/x-date-pickers";
import { useMutation, useQuery } from "@apollo/client";
import {
  getNotifications,
  mutationCreateNotification,
  mutationRemoveNotification,
  mutationUpdateNotification,
} from "@/Data/Notification";
import { getDateFromPicker } from "@/components/HelperFunctions";
import { Snackbar } from "@mui/material";
import SnackbarComponent from "../../components/SnackBar";
import withAuth from "@/components/auth/withAuth";
import withAdminAuth from "@/components/auth/withAdminAuth";

const columns = [
  { id: "notification", label: "Notification", minWidth: 170 },
  // { id: "status", label: "Status", minWidth: 100, align: "center" },
];

const AddNotification = (props) => {
  const {
    data,
    opt,
    setNotifications,
    notifications,
    setOpen,
    setOpenUpdate,
    setSnackbarOpen,
    setSnackbarSeverity,
    setSnackbarMessage,
  } = props;
  
  const [errors, setErrors] = useState({});
  const [id, setId] = useState(data && opt === "update" ? data.id : "");
  const [notification, setNotification] = useState(
    data && opt === "update" ? data.notification : ""
  );
  const [status, setStatus] = useState(
    data && opt === "update" ? data.status : "true"
  );
  
  const [createNotification] = useMutation(mutationCreateNotification);
  const [updateNotification] = useMutation(mutationUpdateNotification);
  const theme = useTheme();

  // Update state when data prop changes (for update modal)
  useEffect(() => {
    if (data && opt === "update") {
      setId(data.id);
      setNotification(data.notification);
      setStatus(data.status || "true");
    }
  }, [data, opt]);

  const validateForm = () => {
    let tempErrors = {};
    if (!notification.trim()) tempErrors.notification = "Notification is required.";
    if (opt === "update" && !id) tempErrors.id = "ID is required for update.";
    setErrors(tempErrors);
    return Object.keys(tempErrors).length === 0;
  };

  const submitHandler = async () => {
    if (!validateForm()) return;

    try {
      if (opt !== "update") {
        // Create new notification
        const response = await createNotification({ 
          variables: { notification: notification.trim() } 
        });
        
        setNotifications([...notifications, response.data.createNotification]);
        setOpen(false);
        setSnackbarMessage("Notification added successfully");
        setSnackbarSeverity("success");
        setSnackbarOpen(true);
        
        // Reset form
        setNotification("");
        setErrors({});
        
      } else {
        // Update existing notification
        const response = await updateNotification({
          variables: {
            id,
            updateNotificationInput: { 
              notification: notification.trim() 
            },
          },
        });
        
        // Update local state
        const updatedNotifications = notifications.map(item => 
          item.id === id 
            ? { ...item, ...response.data.updateNotification }
            : item
        );
        
        setNotifications(updatedNotifications);
                setOpenUpdate(false);
        setSnackbarMessage("Notification updated successfully");
                setSnackbarSeverity("success");
                setSnackbarOpen(true);
              }
    } catch (error) {
      console.error("Error:", error);
      const errorMessage = opt !== "update" 
        ? "Failed to add notification" 
        : "Failed to update notification";
      
      setSnackbarMessage(errorMessage);
              setSnackbarSeverity("error");
              setSnackbarOpen(true);
      }
  };

  return (
    <>
      <TextField
        margin="normal"
        size="small"
        id="notification"
        label="Notification"
        multiline
        rows={6}
        placeholder="Enter notification message..."
        error={!!errors.notification}
        helperText={errors.notification}
        fullWidth
        value={notification}
        onChange={(e) => {
          setNotification(e.target.value);
          // Clear error when user starts typing
          if (errors.notification) {
            setErrors(prev => ({ ...prev, notification: "" }));
          }
        }}
      />
      
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
      <Button
        onClick={submitHandler}
          variant="contained"
        style={{
          color: theme.palette.text.white,
          background: theme.palette.text.purple,
        }}
      >
        {opt !== "update" ? "Submit" : "Update"}
      </Button>
      </Box>
    </>
  );
};

const Notifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [index, setIndex] = useState(null);
  const [open, setOpen] = useState(false);
  const [openUpdate, setOpenUpdate] = useState(false);
  const [data, setData] = useState(null);
  
  const { loading, error, data: queryData } = useQuery(getNotifications);
  const [removeNotification] = useMutation(mutationRemoveNotification);
  console.log("yyyyyyyyy", getNotifications);
  
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");

  const handleCloseSnackbar = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setSnackbarOpen(false);
  };

  useEffect(() => {
    if (!loading && !error && queryData?.notifications) {
      console.log("Notifications data:", queryData.notifications);
      setNotifications(queryData.notifications);
    }
  }, [loading, error, queryData]);

  const deleteHandler = async (deletedId) => {
    try {
      await removeNotification({ variables: { id: deletedId } });

      const updatedNotifications = notifications.filter(
        (notification) => notification.id !== deletedId
      );
      setNotifications(updatedNotifications);

      setSnackbarMessage("Notification deleted successfully");
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
    } catch (error) {
      console.error("Delete error:", error);
      setSnackbarMessage("Failed to delete notification");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    }
  };

  if (error) {
    return (
      <Dashboard>
        <Box sx={{ margin: "25px", textAlign: "center" }}>
          <Typography variant="h6" color="error">
            Error loading notifications: {error.message}
          </Typography>
        </Box>
      </Dashboard>
    );
  }

  return (
    <Dashboard>
      <Box sx={{ margin: "25px" }}>
        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography variant="h5" sx={{ marginY: "10px" }}>
            Notifications
          </Typography>
          <BasicModal
            title="Add Notification"
            comp={
              <AddNotification
                data={null}
                setData={setData}
                opt="add"
                notifications={notifications}
                setNotifications={setNotifications}
                setOpen={setOpen}
                setOpenUpdate={setOpenUpdate}
                setSnackbarMessage={setSnackbarMessage}
                setSnackbarSeverity={setSnackbarSeverity}
                setSnackbarOpen={setSnackbarOpen}
              />
            }
            btn={true}
            open={open}
            setOpen={setOpen}
          />
        </Box>
        
        <UpdateModal
          title="Update Notification"
          comp={
            <AddNotification
              data={data}
              setData={setData}
              opt="update"
              notifications={notifications}
              setNotifications={setNotifications}
              setOpen={setOpen}
              setOpenUpdate={setOpenUpdate}
              setSnackbarMessage={setSnackbarMessage}
              setSnackbarSeverity={setSnackbarSeverity}
              setSnackbarOpen={setSnackbarOpen}
            />
          }
          btn={false}
          openUpdate={openUpdate}
          setOpenUpdate={setOpenUpdate}
        />
        
        <Box sx={{ overflow: "auto", mt: 2 }}>
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <CircularProgress color="secondary" />
            </Box>
          ) : (
            <>
              {notifications.length > 0 ? (
                <DataTable
                  columns={columns}
                  rows={notifications}
                  setIndex={setIndex}
                  setOpen={setOpenUpdate}
                  setData={setData}
                  deleteHandler={deleteHandler}
                  action={true}
                  updateKey="notification"
                />
              ) : (
                <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
                  <Typography variant="h6" color="textSecondary">
                    No notifications found
                  </Typography>
                </Box>
              )}
            </>
          )}
        </Box>
      </Box>
      
      <SnackbarComponent
        open={snackbarOpen}
        handleClose={handleCloseSnackbar}
        severity={snackbarSeverity}
        message={snackbarMessage}
      />
    </Dashboard>
  );
};

export default withAdminAuth(Notifications);