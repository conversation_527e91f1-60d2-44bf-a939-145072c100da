"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityModule = void 0;
const common_1 = require("@nestjs/common");
const throttler_1 = require("@nestjs/throttler");
const csrf_guard_1 = require("./csrf.guard");
const csrf_service_1 = require("./csrf.service");
const csrf_controller_1 = require("./csrf.controller");
const security_config_1 = require("./security.config");
let SecurityModule = class SecurityModule {
};
exports.SecurityModule = SecurityModule;
exports.SecurityModule = SecurityModule = __decorate([
    (0, common_1.Module)({
        imports: [
            throttler_1.ThrottlerModule.forRoot([security_config_1.SecurityConfig.getRateLimitConfig()]),
        ],
        controllers: [csrf_controller_1.CsrfController],
        providers: [csrf_guard_1.CsrfGuard, csrf_service_1.CsrfService, security_config_1.SecurityConfig],
        exports: [csrf_guard_1.CsrfGuard, csrf_service_1.CsrfService, security_config_1.SecurityConfig],
    })
], SecurityModule);
//# sourceMappingURL=security.module.js.map