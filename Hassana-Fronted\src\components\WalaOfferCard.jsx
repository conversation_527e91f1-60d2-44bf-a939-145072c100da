import { Box, Typography, Link } from '@mui/material';
import React from 'react';

const WalaOfferCard = () => {
  return (
    <Link href="https://www.walaplus.com/en-gb" underline="none">
    <Box sx={{
        // width: '100%',
        padding: 3,
        // maxWidth: 479, 
       // backgroundColor: '#fff',
       // borderRadius: 2, // Optional: Adds some rounded corners
       // boxShadow: 2, // Optional: Adds a subtle shadow
      }}
    >
      <Typography sx={{ fontSize: '22px', color: '#1b3745', mb: 2 }}>
        <Box
          component="span"
          sx={{
            borderBottom: '2px solid #00ce8a', 
            display: 'inline-block',
            pb: '2px',
          }}
        >
          Enhance Employee <span style={{ color: '#2c3e50', fontWeight: 'bold' }}>Financial</span> 
        </Box>
        <span style={{ color: '#2c3e50', fontWeight: 'bold' }}> Wellness</span> 
      </Typography>
      
      <Typography variant="body2" sx={{
        color: '#b0b0b0',
        width: '100%',
        maxWidth: 450,
      }}>
        Empower your team with exclusive savings, personalized offers, and valuable perks through {' '}
        <Box component="span" sx={{ color: '#2c3e50', fontWeight: 'bold' }}>WalaOffer</Box>
      </Typography>
      
      <Box sx={{
        display: 'flex',
        justifyContent: 'flex-end',
        pt: 2
      }}>
        <Box 
          component="img"
          src="images\wala.png"
          alt="Wala Offer promotional image"
          sx={{
            width: 300,
            height: 100,
            objectFit: 'cover',
            borderRadius: 2
          }}
        />
      </Box>  
    </Box>
    </Link>
  );
};

export default WalaOfferCard;
