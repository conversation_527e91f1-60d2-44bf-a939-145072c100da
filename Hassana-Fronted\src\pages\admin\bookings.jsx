import Dashboard from "@/components/Dashboard";
import {
  Box,
  Button,
  CircularProgress,
  MenuItem,
  Tab,
  Tabs,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import DataTable from "./component/DataTable";
import { useEffect, useState } from "react";
import { useMutation, useQuery } from "@apollo/client";
import { getBookings, mutationUpdateBookingStatus } from "@/Data/Booking";
import { TabContext, TabList, TabPanel } from "@mui/lab";
import { padding } from "@mui/system";
import {
  getResources,
  mutationCreateResource,
  mutationUpdateResource,
} from "@/Data/Resource";
import BasicModal, { UpdateModal } from "./component/DialogBox";
import withAuth from "@/components/auth/withAuth";
import withAdminAuth from "@/components/auth/withAdminAuth";

const columns = [
  { id: "title", label: "Title", minWidth: 170 },
  { id: "user", label: "User", minWidth: 100, align: "center" },
  { id: "resource", label: "Resource", minWidth: 100, align: "center" },
  { id: "details", label: "Details", minWidth: 370, align: "left" },
  { id: "start", label: "From", minWidth: 100, align: "left" },
  { id: "end", label: "To", minWidth: 100, align: "left" },
  { id: "status", label: "Status", minWidth: 100, align: "left" },
  { id: "updatedAt", label: "Updated At", minWidth: 100, align: "center" },
];
const resourceColumns = [
  { id: "name", label: "Name", minWidth: 170 },
  { id: "type", label: "Type", minWidth: 100, align: "center" },
];

const Booking = () => {
  const theme = useTheme();
  const [bookings, setBookings] = useState([]);
  const [Resources, setResources] = useState([]);

  const [open, setOpen] = useState(false);
  const [openUpdate, setOpenUpdate] = useState(false);
  const [openUpdateResource, setOpenUpdateResource] = useState(false);
  const [Data, setData] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");

  const {
    loading: queryLoading,
    error: queryError,
    data: queryData,
  } = useQuery(getBookings);
  // console.log(resourceData.allResources);
  const {
    loading: resourceLoading,
    error: resourceError,
    data: resourceData,
  } = useQuery(getResources);

  useEffect(() => {
    // setResources(resourceData.allResources);
    if (!queryLoading && !queryError && queryData && queryData.bookings) {
      // console.log("response.data", queryData.bookings)
      const bookingData = queryData.bookings.map((item) => ({
        id: item.id,
        title: item.title,
        details: item.details,
        status: item.status,
        start: item.startTime,
        end: item.endTime,
        resource: item.resource.name,
        user: item.user.name,
        updatedAt: item.updatedAt,
      }));
      setBookings(bookingData); // corrected line
      console.log(bookingData);
    }
  }, [queryLoading, queryError, queryData]);
  console.log(bookings);

  const [value, setValue] = useState("1");

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  return (
    <>
      <Dashboard>
        <Box sx={{ margin: "25px" }}>
          {/* <Typography variant="h5" sx={{ marginY: "10px" }}>
            Bookings
          </Typography> */}

          {/* <UpdateModal title={"Update Announcement"} comp={<AddAnnouncement data={Data} setData={setData} opt={"update"} />} btn={false} open={open} setOpen={setOpen} /> */}

          <Box sx={{ maxWidth: "100%", overflow: "auto" }}>
            <TabContext value={value}>
              <Box
                sx={{
                  borderBottom: 1,
                  borderColor: "divider",
                  display: "flex",
                  justifyContent: "space-between",
                }}
              >
                <Typography variant="h5" sx={{ marginY: "10px" }}>
                  {value == 1 ? "Bookings" : "Resource"}
                </Typography>
                <TabList
                  onChange={handleChange}
                  aria-label="lab API tabs example"
                >
                  <Tab label="Booking" value="1" />
                  {/* style={{ background: "#A665E1" }} */}
                  <Tab label="Resource" value="2" />
                </TabList>
              </Box>
              <TabPanel value="1" sx={{ padding: "0px" }}>
                {/* <DataTable columns={columns} rows={bookings} /> */}
                <UpdateModal
                  title={"Update Booking"}
                  comp={
                    <AddBooking
                      data={Data}
                      setData={setData}
                      opt={"update"}
                      bookings={bookings}
                      setBookings={setBookings}
                      // modalHandler={modalHandler}
                      setOpenUpdate={setOpenUpdate}
                      setOpen={setOpen}
                      setSnackbarMessage={setSnackbarMessage}
                      setSnackbarSeverity={setSnackbarSeverity}
                      setSnackbarOpen={setSnackbarOpen}
                    />
                  }
                  btn={false}
                  openUpdate={openUpdate}
                  setOpenUpdate={setOpenUpdate}
                />
                {queryLoading ? (
                  <Box sx={{ display: "flex", justifyContent: "center" }}>
                    <CircularProgress color="secondary" />
                  </Box>
                ) : bookings.length > 0 ? (
                  <DataTable
                    columns={columns}
                    rows={bookings}
                    setOpen={setOpenUpdate}
                    setData={setData}
                    updateKey={"title"}
                  />
                ) : bookings.length > 0 ? (
                  <DataTable
                    columns={columns}
                    rows={bookings}
                    setOpen={setOpenUpdate}
                    setData={setData}
                    updateKey={"title"}
                  />
                ) : (
                  <Box sx={{ display: "flex", justifyContent: "center" }}>
                    <Typography variant="h5" sx={{ marginY: "10px" }}>
                      No data found
                    </Typography>
                  </Box>
                )}
              </TabPanel>
              <TabPanel value="2" sx={{ padding: "0px" }}>
                <BasicModal
                  title={"Add Resource"}
                  comp={
                    <AddResource
                      data={Data}
                      setData={setData}
                      opt={"add"}
                      open={open}
                      setOpen={setOpen}
                      Resources={Resources}
                      setResources={setResources}
                      setSnackbarMessage={setSnackbarMessage}
                      setSnackbarSeverity={setSnackbarSeverity}
                      setSnackbarOpen={setSnackbarOpen}
                    />
                  }
                  btn={true}
                  open={open}
                  setOpen={setOpen}
                />
                <UpdateModal
                  title={"Update Resource"}
                  comp={
                    <AddResource
                      data={Data}
                      setData={setData}
                      opt={"update"}
                      Resources={Resources}
                      setResources={setResources}
                      setOpenUpdate={setOpenUpdateResource}
                      setOpen={setOpen}
                      setSnackbarMessage={setSnackbarMessage}
                      setSnackbarSeverity={setSnackbarSeverity}
                      setSnackbarOpen={setSnackbarOpen}
                    />
                  }
                  btn={false}
                  openUpdate={openUpdateResource}
                  setOpenUpdate={setOpenUpdateResource}
                />
                {resourceLoading ? (
                  <Box sx={{ display: "flex", justifyContent: "center" }}>
                    <CircularProgress color="secondary" />
                  </Box>
                ) : resourceData.allResources.length > 0 ? (
                  <DataTable
                    columns={resourceColumns}
                    rows={resourceData.allResources}
                    setData={setData}
                    setOpen={setOpenUpdateResource}
                    updateKey={"name"}
                  />
                ) : (
                  <Box sx={{ display: "flex", justifyContent: "center" }}>
                    <Typography variant="h5" sx={{ marginY: "10px" }}>
                      No data found
                    </Typography>
                  </Box>
                )}
              </TabPanel>
            </TabContext>
          </Box>
        </Box>
      </Dashboard>
    </>
  );
};
export default withAdminAuth(Booking);

const AddResource = (props) => {
  const {
    opt,
    data,
    modalHandler,
    setOpen,
    setOpenUpdate,
    Resources,
    setResources,
    setSnackbarOpen,
    setSnackbarSeverity,
    setSnackbarMessage,
  } = props;

  const theme = useTheme();
  const [id, setId] = useState(data && opt == "update" ? data.id : "");
  const [title, setTitle] = useState(data && opt == "update" ? data.name : "");
  const [type, setType] = useState(data && opt == "update" ? data.type : "");
  const [createResource] = useMutation(mutationCreateResource);
  const [updateResource] = useMutation(mutationUpdateResource);

  const submitHandler = () => {
    return new Promise(async (res, rej) => {
      let Data = {
        variables: {
          id: id,
          name: title,
          type: type,
        },
      };
      console.log("date", Data);
      opt != "update"
        ? await createResource(Data)
            .then((response) => {
              console.log(response);
              setResources([...Resources, response.data.createResource]);
              setOpen(false);
            })
            .catch((error) => {
              console.log(error);
              rej(error);
            })
        : await updateResource(Data)
            .then((response) => {
              const itemIndex = Resources.findIndex(
                (item) => item.id === Data.variables.id
              );
              const resource = response.data.updateResource;
              if (itemIndex !== -1) {
                const updatedData = [...Resources];
                // updatedData[itemIndex] = response.data.updateEvent;
                updatedData[itemIndex] = {
                  id,
                  name,
                  type,
                };
                setResources(updatedData);
                setOpenUpdate(false);
              }
              // modalHandler();
            })
            .catch((error) => {
              console.log(error);
              rej(error);
            });
      res(Data);
      setSnackbarMessage(
        `Event ${opt != "update" ? "added" : "updated"} successfully`
      );
      setSnackbarSeverity("success");
      setSnackbarOpen(true);
    });
    // setOpen(false);
  };

  return (
    <>
      <TextField
        margin="normal"
        size="small"
        id="name"
        fullWidth
        label="Title"
        value={title}
        onChange={(e) => setTitle(e.target.value)}
      />
      <TextField
        id="type"
        select
        label="Type"
        fullWidth
        helperText="Please select Type"
        value={type}
        onChange={(e) => setType(e.target.value)}
        SelectProps={{
          MenuProps: {
            PaperProps: {
              style: {
                backgroundColor: theme.palette.background.primary,
              },
            },
          },
        }}
        sx={{ width: "100%", marginY: "10px" }}
      >
        <MenuItem value="tea-boy">Tea Boy</MenuItem>
        <MenuItem value="parking">Parking</MenuItem>
        <MenuItem value="room">Room</MenuItem>
        <MenuItem value="it-technician">IT Technician</MenuItem>
      </TextField>
      <Button
        onClick={submitHandler}
        style={{
          color: theme.palette.text.white,
          background: theme.palette.text.purple,
        }}
      >
        {opt != "update" ? "Submit" : "Update"}
      </Button>
    </>
  );
};

const AddBooking = (props) => {
  const {
    opt,
    data,
    bookings,
    setBookings,
    modalHandler,
    setOpen,
    setOpenUpdate,
    setSnackbarOpen,
    setSnackbarSeverity,
    setSnackbarMessage,
  } = props;
  const [status, setStatus] = useState(
    data && opt == "update" ? data.status : ""
  );
  const [id, setId] = useState(data && opt == "update" ? data.id : "");
  const [updateBookingStatus] = useMutation(mutationUpdateBookingStatus);
  const theme = useTheme();

  const submitHandler = () => {
    return new Promise(async (res, rej) => {
      let Data = {
        variables: {
          id: id,
          status: status,
        },
      };
      console.log("date", Data);
      await updateBookingStatus(Data)
        .then((response) => {
          const itemIndex = bookings.findIndex(
            (item) => item.id === Data.variables.id
          );
          const booking = response.data.updateBookingStatus;
          console.log("booking", booking);
          if (itemIndex !== -1) {
            const updatedData = [...bookings];
            updatedData[itemIndex] = response.data.updateBookingStatus;
            // updatedData[itemIndex] = {
            //   status: status,
            // };
            // setBookings(updatedData);
            setOpenUpdate(false);
          }
        })
        .catch((error) => {
          console.log(error);
          rej(error);
        });
      res(Data);
    });
  };

  return (
    <>
      <TextField
        id="status"
        select
        label="Status"
        fullWidth
        helperText="Please select Type"
        value={status}
        onChange={(e) => setStatus(e.target.value)}
        SelectProps={{
          MenuProps: {
            PaperProps: {
              style: {
                backgroundColor: theme.palette.background.primary,
              },
            },
          },
        }}
        sx={{ width: "100%", marginY: "10px" }}
      >
        <MenuItem value="pending">Pending</MenuItem>
        <MenuItem value="confirmed">Confirmed</MenuItem>
      </TextField>
      <Button
        onClick={submitHandler}
        style={{
          color: theme.palette.text.white,
          background: theme.palette.text.purple,
        }}
      >
        {opt != "update" ? "Submit" : "Update"}
      </Button>
    </>
  );
};
