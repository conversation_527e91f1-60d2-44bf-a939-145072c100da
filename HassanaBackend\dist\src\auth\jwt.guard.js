"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JwtGuard = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const session_service_1 = require("./session.service");
const jwt = require("jsonwebtoken");
let JwtGuard = class JwtGuard {
    constructor(sessionService) {
        this.sessionService = sessionService;
    }
    async canActivate(context) {
        const ctx = graphql_1.GqlExecutionContext.create(context).getContext();
        const authorizationHeader = ctx.req.headers.authorization;
        if (!authorizationHeader) {
            throw new common_1.HttpException('Authorization header not found', common_1.HttpStatus.UNAUTHORIZED);
        }
        if (!authorizationHeader.startsWith('Bearer ')) {
            throw new common_1.HttpException('Invalid authorization header format. Expected: Bearer <token>', common_1.HttpStatus.UNAUTHORIZED);
        }
        const token = authorizationHeader.split(' ')[1];
        if (!token) {
            throw new common_1.HttpException('Token not found in authorization header', common_1.HttpStatus.UNAUTHORIZED);
        }
        try {
            const JWT_KEY = process.env.JWT_KEY;
            if (!JWT_KEY) {
                throw new common_1.HttpException('JWT secret key not configured', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
            const user = jwt.verify(token, JWT_KEY);
            if (user.sessionId && user.id) {
                const isValidSession = await this.sessionService.validateUserSession(user.id.toString(), user.sessionId, token);
                if (!isValidSession) {
                    throw new common_1.HttpException('Session is no longer valid. Please log in again.', common_1.HttpStatus.UNAUTHORIZED);
                }
            }
            ctx.req.user = user;
            ctx.user = user;
            return true;
        }
        catch (error) {
            if (error.name === 'TokenExpiredError') {
                throw new common_1.HttpException('Token has expired', common_1.HttpStatus.UNAUTHORIZED);
            }
            else if (error.name === 'JsonWebTokenError') {
                throw new common_1.HttpException('Invalid token', common_1.HttpStatus.UNAUTHORIZED);
            }
            else {
                throw new common_1.HttpException('Token verification failed: ' + error.message, common_1.HttpStatus.UNAUTHORIZED);
            }
        }
    }
};
exports.JwtGuard = JwtGuard;
exports.JwtGuard = JwtGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [session_service_1.SessionService])
], JwtGuard);
//# sourceMappingURL=jwt.guard.js.map