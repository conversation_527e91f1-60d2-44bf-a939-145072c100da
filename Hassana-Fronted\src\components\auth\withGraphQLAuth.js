import React, { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { CircularProgress, Box, Alert } from '@mui/material';

/**
 * Higher-order component that provides GraphQL authentication protection
 * @param {React.Component} WrappedComponent - Component to wrap
 * @param {Object} options - Configuration options
 * @param {string} options.requiredRole - Required role (ADMIN, USER)
 * @param {boolean} options.redirectOnError - Whether to redirect on auth errors
 * @param {string} options.redirectPath - Path to redirect to on auth errors
 */
const withGraphQLAuth = (WrappedComponent, options = {}) => {
  const {
    requiredRole = null,
    redirectOnError = true,
    redirectPath = '/login?login=false'
  } = options;

  return function GraphQLAuthComponent(props) {
    const { data: session, status } = useSession();
    const router = useRouter();
    const [authError, setAuthError] = useState(null);
    const [isReady, setIsReady] = useState(false);

    useEffect(() => {
      if (status === "loading") return;

      // Check if user is authenticated
      if (status === "unauthenticated") {
        if (redirectOnError) {
          router.push(redirectPath);
        } else {
          setAuthError("Authentication required");
        }
        return;
      }

      // Check if user has required role
      if (requiredRole && session?.user?.role) {
        const userRole = session.user.role.toUpperCase();
        const required = requiredRole.toUpperCase();
        
        if (userRole !== required) {
          if (redirectOnError) {
            router.push('/unauthorized');
          } else {
            setAuthError(`Access denied. Required role: ${required}, Your role: ${userRole}`);
          }
          return;
        }
      }

      // All checks passed
      setAuthError(null);
      setIsReady(true);
    }, [status, session, router]);

    // Show loading spinner while checking authentication
    if (status === "loading" || !isReady) {
      return (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="100vh"
        >
          <CircularProgress color="secondary" />
        </Box>
      );
    }

    // Show error message if not redirecting
    if (authError && !redirectOnError) {
      return (
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          minHeight="100vh"
          p={3}
        >
          <Alert severity="error" sx={{ maxWidth: 400 }}>
            {authError}
          </Alert>
        </Box>
      );
    }

    // Render the wrapped component with additional props
    return (
      <WrappedComponent
        {...props}
        session={session}
        authError={authError}
        setAuthError={setAuthError}
      />
    );
  };
};

/**
 * Specific HOC for admin-only GraphQL operations
 */
export const withGraphQLAdminAuth = (WrappedComponent, options = {}) => {
  return withGraphQLAuth(WrappedComponent, {
    requiredRole: 'ADMIN',
    ...options
  });
};

/**
 * Specific HOC for user-level GraphQL operations
 */
export const withGraphQLUserAuth = (WrappedComponent, options = {}) => {
  return withGraphQLAuth(WrappedComponent, {
    requiredRole: 'USER',
    ...options
  });
};

export default withGraphQLAuth;
