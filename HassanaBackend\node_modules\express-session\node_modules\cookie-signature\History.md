1.0.7 / 2023-04-12
==================

* backport the buffer support from the 1.2.x release branch (thanks @FadhiliNjagi!)

1.0.6 / 2015-02-03
==================

* use `npm test` instead of `make test` to run tests
* clearer assertion messages when checking input

1.0.5 / 2014-09-05
==================

* add license to package.json

1.0.4 / 2014-06-25
==================

 * corrected avoidance of timing attacks (thanks @tenbits!)

1.0.3 / 2014-01-28
==================

 * [incorrect] fix for timing attacks

1.0.2 / 2014-01-28
==================

 * fix missing repository warning
 * fix typo in test

1.0.1 / 2013-04-15
==================

  * Revert "Changed underlying HMAC algo. to sha512."
  * Revert "Fix for timing attacks on MAC verification."

0.0.1 / 2010-01-03
==================

  * Initial release
