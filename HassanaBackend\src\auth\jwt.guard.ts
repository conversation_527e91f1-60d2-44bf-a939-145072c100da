import {
  CanActivate,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { Observable } from 'rxjs';
import { User } from 'src/users/entities/user.entity';
import { UserService } from 'src/users/user.service';
import { SessionService } from './session.service';
import * as jwt from 'jsonwebtoken';

@Injectable()
export class JwtGuard implements CanActivate {
  constructor(private readonly sessionService: SessionService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context).getContext();

    const authorizationHeader = ctx.req.headers.authorization;
    if (!authorizationHeader) {
      throw new HttpException(
        'Authorization header not found',
        HttpStatus.UNAUTHORIZED,
      );
    }

    if (!authorizationHeader.startsWith('Bearer ')) {
      throw new HttpException(
        'Invalid authorization header format. Expected: Bearer <token>',
        HttpStatus.UNAUTHORIZED,
      );
    }

    const token = authorizationHeader.split(' ')[1];
    if (!token) {
      throw new HttpException(
        'Token not found in authorization header',
        HttpStatus.UNAUTHORIZED,
      );
    }

    try {
      const JWT_KEY = process.env.JWT_KEY;
      if (!JWT_KEY) {
        throw new HttpException(
          'JWT secret key not configured',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      const user = jwt.verify(token, JWT_KEY) as any;

      // Validate session if user has sessionId
      if (user.sessionId && user.id) {
        const isValidSession = await this.sessionService.validateUserSession(
          user.id.toString(),
          user.sessionId,
          token
        );

        if (!isValidSession) {
          throw new HttpException(
            'Session is no longer valid. Please log in again.',
            HttpStatus.UNAUTHORIZED,
          );
        }
      }

      ctx.req.user = user;
      ctx.user = user; // Also set in GraphQL context for easier access
      return true;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new HttpException(
          'Token has expired',
          HttpStatus.UNAUTHORIZED,
        );
      } else if (error.name === 'JsonWebTokenError') {
        throw new HttpException(
          'Invalid token',
          HttpStatus.UNAUTHORIZED,
        );
      } else {
        throw new HttpException(
          'Token verification failed: ' + error.message,
          HttpStatus.UNAUTHORIZED,
        );
      }
    }
  }
}
