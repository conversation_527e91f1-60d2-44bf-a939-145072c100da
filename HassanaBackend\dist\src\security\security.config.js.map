{"version": 3, "file": "security.config.js", "sourceRoot": "", "sources": ["../../../src/security/security.config.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAGrC,IAAM,cAAc,GAApB,MAAM,cAAc;IAEzB,MAAM,CAAC,aAAa;QAClB,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI;YAChE,uBAAuB;YACvB,uBAAuB;YACvB,+BAA+B;YAC/B,kCAAkC;SACnC,CAAC;QAEF,OAAO;YACL,MAAM,EAAE,CAAC,MAAc,EAAE,QAAsD,EAAE,EAAE;gBAEjF,IAAI,CAAC,MAAM;oBAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAEzC,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBACpC,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC9B,CAAC;qBAAM,CAAC;oBACN,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC3D,CAAC;YACH,CAAC;YACD,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC;YACpD,cAAc,EAAE;gBACd,QAAQ;gBACR,kBAAkB;gBAClB,cAAc;gBACd,QAAQ;gBACR,eAAe;gBACf,cAAc;gBACd,iBAAiB;aAClB;YACD,WAAW,EAAE,IAAI;YACjB,iBAAiB,EAAE,KAAK;YACxB,oBAAoB,EAAE,GAAG;SAC1B,CAAC;IACJ,CAAC;IAGD,MAAM,CAAC,eAAe;QACpB,OAAO;YACL,qBAAqB,EAAE;gBACrB,UAAU,EAAE;oBACV,UAAU,EAAE,CAAC,QAAQ,CAAC;oBACtB,QAAQ,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;oBACvC,SAAS,EAAE,CAAC,QAAQ,CAAC;oBACrB,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;oBACrC,UAAU,EAAE,CAAC,QAAQ,CAAC;oBACtB,OAAO,EAAE,CAAC,QAAQ,CAAC;oBACnB,SAAS,EAAE,CAAC,QAAQ,CAAC;oBACrB,QAAQ,EAAE,CAAC,QAAQ,CAAC;oBACpB,QAAQ,EAAE,CAAC,QAAQ,CAAC;iBACrB;aACF;YACD,yBAAyB,EAAE,KAAK;YAChC,IAAI,EAAE;gBACJ,MAAM,EAAE,QAAQ;gBAChB,iBAAiB,EAAE,IAAI;gBACvB,OAAO,EAAE,IAAI;aACd;YACD,cAAc,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE;SAC1C,CAAC;IACJ,CAAC;IAGD,MAAM,CAAC,kBAAkB;QACvB,OAAO;YACL,GAAG,EAAE,EAAE;YACP,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI;YACzD,MAAM,EAAE,CAAC,OAAO,EAAE,EAAE;gBAElB,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;gBACpD,OAAO,OAAO,CAAC,GAAG,KAAK,SAAS,IAAI,OAAO,CAAC,GAAG,KAAK,GAAG,CAAC;YAC1D,CAAC;SACF,CAAC;IACJ,CAAC;IAGD,MAAM,CAAC,wBAAwB;QAC7B,OAAO;YAEL,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;YACpD,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;YAGjD,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,EAExD,CAAC,CAAC,CAAC,EAAE;YAGN,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE;gBAExB,OAAO;oBACL,GAAG;oBACH,GAAG;oBAEH,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC;oBACtC,YAAY,EAAE,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC;iBAC7C,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;IAGD,MAAM,CAAC,gBAAgB;QACrB,OAAO;YACL,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,2BAA2B;YACjE,MAAM,EAAE,KAAK;YACb,iBAAiB,EAAE,KAAK;YACxB,MAAM,EAAE;gBACN,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;gBAC7C,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;gBAC3B,QAAQ,EAAE,QAAiB;aAC5B;SACF,CAAC;IACJ,CAAC;IAGD,MAAM,CAAC,mBAAmB;QACxB,OAAO;YACL,SAAS,EAAE,IAAI;YACf,oBAAoB,EAAE,IAAI;YAC1B,SAAS,EAAE,IAAI;YACf,mBAAmB,EAAE,IAAI;YACzB,gBAAgB,EAAE;gBAChB,wBAAwB,EAAE,IAAI;aAC/B;YAED,gBAAgB,EAAE,CAAC,MAAM,EAAE,EAAE;gBAC3B,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAClC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAClD,CAAC;gBACF,OAAO,IAAI,KAAK,CAAC,sBAAsB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAChE,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAA;AAxIY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;GACA,cAAc,CAwI1B"}