import { <PERSON>, Get, Post, Req, Res, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { CsrfService } from './csrf.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Security')
@Controller('security')
export class CsrfController {
  constructor(private readonly csrfService: CsrfService) {}

  @Get('csrf-token')
  @ApiOperation({ summary: 'Get CSRF token for secure requests' })
  @ApiResponse({ 
    status: 200, 
    description: 'CSRF token generated successfully',
    schema: {
      type: 'object',
      properties: {
        csrfToken: { type: 'string' },
        sessionToken: { type: 'string' },
        message: { type: 'string' }
      }
    }
  })
  getCsrfToken(@Req() req: Request, @Res() res: Response) {
    try {
      // Generate CSRF token
      const { csrfToken, sessionToken } = this.csrfService.createCsrfResponse();

      // Set session token in httpOnly cookie for additional security
      res.cookie('session-token', sessionToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      });

      return res.status(HttpStatus.OK).json({
        csrfToken,
        sessionToken, // Also return in response for client-side storage
        message: 'CSRF token generated successfully'
      });
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        message: 'Failed to generate CSRF token',
        error: error.message
      });
    }
  }

  @Post('verify-csrf')
  @ApiOperation({ summary: 'Verify CSRF token (for testing)' })
  @ApiResponse({ 
    status: 200, 
    description: 'CSRF token verification result',
    schema: {
      type: 'object',
      properties: {
        valid: { type: 'boolean' },
        message: { type: 'string' }
      }
    }
  })
  verifyCsrfToken(@Req() req: Request, @Res() res: Response) {
    try {
      const csrfToken = req.headers['x-csrf-token'] as string;
      const sessionToken = req.headers['x-session-token'] as string;

      if (!csrfToken || !sessionToken) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          valid: false,
          message: 'CSRF token and session token are required'
        });
      }

      const isValid = this.csrfService.verifyCsrfToken(csrfToken, sessionToken);

      return res.status(HttpStatus.OK).json({
        valid: isValid,
        message: isValid ? 'CSRF token is valid' : 'CSRF token is invalid'
      });
    } catch (error) {
      return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        valid: false,
        message: 'Failed to verify CSRF token',
        error: error.message
      });
    }
  }
}
