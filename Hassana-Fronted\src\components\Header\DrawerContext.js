import React, { createContext, useState, useEffect } from "react";

export const DrawerContext = createContext();

export const DrawerProvider = ({ children }) => {
  const [open, setOpen] = useState(false);

  // Load saved state from localStorage when component mounts
  useEffect(() => {
    const savedState = localStorage.getItem("drawerOpen");
    // console.log("savedState:", savedState); // Add this
    if (savedState !== null) {
      setOpen(JSON.parse(savedState));
    }
  }, []);

  useEffect(() => {
    // console.log("open:", open);
    localStorage.setItem("drawerOpen", JSON.stringify(open));
  }, [open]);

  return (
    <DrawerContext.Provider value={{ open, setOpen }}>
      {children}
    </DrawerContext.Provider>
  );
};
