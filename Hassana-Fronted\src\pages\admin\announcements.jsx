'use client'; // Add this to force client-side rendering

import Dashboard from "@/components/Dashboard";
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  Input,
  InputAdornment,
  MenuItem,
  TextField,
  Typography,
  useTheme,
} from "@mui/material";
import { DateTimePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import DataTable from "./component/DataTable";
import BasicModal, { UpdateModal } from "./component/DialogBox";
import {
  createAnnouncement,
  deleteAnnouncement,
  getAllAnnouncements,
  mutationRemoveAnnouncement,
  updateAnnouncement,
} from "@/Data/Announcement";
import { getDateFromPicker } from "@/components/HelperFunctions";
import withAuth from "@/components/auth/withAuth";
import { useMutation } from "@apollo/client";
import { Cancel, PhotoCamera } from "@mui/icons-material";
import SnackbarComponent from "../../components/SnackBar";
import withAdminAuth from "@/components/auth/withAdminAuth";
import { useSession } from "next-auth/react";

const columns = [
  { id: "title", label: "Title", minWidth: 170 },
  { id: "details", label: "Details", minWidth: 370, align: "left" },
  { id: "category", label: "Category", minWidth: 100, align: "left" },
  { id: "visibility", label: "Visibility", minWidth: 100, align: "center" },
  { id: "status", label: "Status", minWidth: 100, align: "center" },
];

const AddAnnouncement = (props) => {
  const {
    data,
    opt,
    setAnnouncements,
    announcements,
    setOpen,
    setOpenUpdate,
    setSnackbarOpen,
    setSnackbarSeverity,
    setSnackbarMessage,
    token,
  } = props;

  const [id, setId] = useState(data && opt == "update" ? data.id : "");
  const [title, setTitle] = useState(data && opt == "update" ? data.title : "");
  const [category, setCategory] = useState(
    data && opt == "update" ? data.category : ""
  );
  const [visibility, setVisibility] = useState(
    data && opt == "update" ? data.visibility : dayjs()
  );
  console.log("Visibility: " + visibility);
  const [status, setStatus] = useState(
    data && opt == "update" ? data.status : "true"
  );
  const [excerpt, setExcerpt] = useState(
    data && opt == "update" ? data.details : ""
  );
  const [featuredImage, setFeaturedImage] = useState(
    data && opt == "update" ? data.image : ""
  );
  const [image, setImage] = useState(
    data && opt == "update" ? data.image : ""
  );
  const [isImageChanged, setIsImageChanged] = useState(false);

  // Function to get proper image URL for display
  const getImageDisplayUrl = () => {
    if (!image) return null;

    // If it's a new file (base64), return as is
    if (typeof image === 'string' && image.startsWith('data:')) {
      return image;
    }

    // If it's an existing image URL, construct proper URL
    // if (typeof image === 'string') {
    //   const cleanImagePath = image.replace('http://localhost:3009', '');
    //   return 'https://v2-portal.hassana.com.sa' + cleanImagePath;
    // }

    return null;
  };
  const theme = useTheme();
  const [errors, setErrors] = useState({});

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      console.log(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setImage(reader.result);
        setFeaturedImage(file);
      };
      reader.readAsDataURL(file);
    }
    setIsImageChanged(true);
    console.log("isimagechange:", isImageChanged);
  };

  const handleImageRemove = () => {
    setImage(null);
    setFeaturedImage(null);
    setIsImageChanged(true);
    // Clear the file input
    const fileInput = document.getElementById("imageInput");
    if (fileInput) {
      fileInput.value = "";
    }
  };

  const validateForm = () => {
    let tempErrors = {};
    if (!title) tempErrors.title = "Title is required.";
    if (!category) tempErrors.category = "Category is required.";
    if (!visibility) tempErrors.visibility = "Visibility is required.";
    if (!status) tempErrors.status = "Status is required.";
    if (!excerpt) tempErrors.excerpt = "Excerpt is required.";
    setErrors(tempErrors);
    return Object.keys(tempErrors).length === 0;
  };

  const submitHandler = () => {
    return new Promise(async (res, rej) => {
      if (validateForm()) {
        let fieldsData = {
          id: id,
          title: title,
          category: category,
          visibility: getDateFromPicker(visibility),
          status: status === "true" ? true : false,
          details: excerpt,
          image: featuredImage,
          created_by: props.userEmail || "Admin",
          updated_by: props.userEmail || "Admin", // Added for update
        };
        console.log(fieldsData);

        if (opt != "update") {
          let response = await createAnnouncement(fieldsData, token);
          console.log(response);
          if (response?.code == 200) {
            setAnnouncements((prevAnnounc) => [response.data, ...prevAnnounc]);
            setSnackbarMessage(`Announcement added successfully`);
            setSnackbarSeverity("success");
            setSnackbarOpen(true);
          } else {
            console.log(response?.error);
            rej(response?.error);
            setSnackbarMessage(`Failed to add Announcement: ${response?.error}`);
            setSnackbarSeverity("error");
            setSnackbarOpen(true);
          }
          setOpen(false);
        } else {
          let response = await updateAnnouncement(fieldsData, isImageChanged, token);
          console.log(response);
          if (response?.code == 200) {
            setAnnouncements((prevAnnounc) =>
              prevAnnounc.map((announcement) =>
                announcement.id === data.id
                  ? { ...announcement, ...response.data }
                  : announcement
              )
            );
            setSnackbarMessage(`Announcement updated successfully`);
            setSnackbarSeverity("success");
            setSnackbarOpen(true);
          } else {
            console.log(response?.error);
            rej(response?.error);
            setSnackbarMessage(`Failed to update Announcement: ${response?.error}`);
            setSnackbarSeverity("error");
            setSnackbarOpen(true);
          }
          setOpenUpdate(false);
        }
        res(fieldsData);
      }
    });
  };

  return (
    <>
      <TextField
        error={!!errors.title}
        helperText={errors.title}
        margin="normal"
        size="small"
        id="Title"
        fullWidth
        label="Title"
        value={title}
        onChange={(e) => setTitle(e.target.value)}
      />
      <TextField
        error={!!errors.category}
        helperText={errors.category}
        id="category"
        select
        label="Category/Topic"
        defaultValue="internal"
        fullWidth
        value={category}
        onChange={(e) => setCategory(e.target.value)}
        sx={{ width: "100%", marginY: "10px" }}
        SelectProps={{
          MenuProps: {
            PaperProps: {
              style: {
                backgroundColor: theme.palette.background.primary,
              },
            },
          },
        }}
      >
        <MenuItem value="internal">Internal</MenuItem>
        <MenuItem value="external">External</MenuItem>
      </TextField>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DateTimePicker
          label="Visibility"
          value={visibility ? dayjs(visibility) : null}
          onChange={setVisibility}
          sx={{ width: "100%", marginY: "20px" }}
        />
      </LocalizationProvider>
      <TextField
        error={!!errors.status}
        helperText={errors.status}
        id="status"
        select
        label="Status"
        defaultValue="true"
        fullWidth
        value={status}
        onChange={(e) => setStatus(e.target.value)}
        SelectProps={{
          MenuProps: {
            PaperProps: {
              style: {
                backgroundColor: theme.palette.background.primary,
              },
            },
          },
        }}
      >
        <MenuItem value="true">Active</MenuItem>
        <MenuItem value="false">Inactive</MenuItem>
      </TextField>
      <TextField
        error={!!errors.excerpt}
        helperText={errors.excerpt}
        margin="normal"
        size="small"
        id="Excerpt/Summary"
        label="Excerpt/Summary"
        multiline
        rows={6}
        placeholder="..."
        fullWidth
        value={excerpt}
        onChange={(e) => setExcerpt(e.target.value)}
      />
      <Box>
        {!image && (
          <Input
            type="file"
            accept="image/*"
            onChange={handleImageChange}
            disableUnderline
            endAdornment={
              <InputAdornment position="end">
                <IconButton component="label" htmlFor="imageInput">
                  <PhotoCamera />
                </IconButton>
              </InputAdornment>
            }
            inputProps={{ id: "imageInput", style: { display: "none" } }}
          />
        )}
        {image && (
          <Box sx={{ margin: "auto", position: "relative" }}>
            <img
              src={getImageDisplayUrl()}
              alt="Selected"
              style={{ maxWidth: "100%", maxHeight: "200px" }}
            />
            <IconButton
              onClick={handleImageRemove}
              sx={{
                position: "absolute",
                top: 0,
                right: 0,
                backgroundColor: "rgba(255,255,255,0.8)",
                "&:hover": {
                  backgroundColor: "rgba(255,255,255,0.9)"
                }
              }}
            >
              <Cancel />
            </IconButton>
          </Box>
        )}
      </Box>
      <Button
        onClick={submitHandler}
        style={{
          color: theme.palette.text.white,
          background: theme.palette.text.purple,
        }}
      >
        {opt != "update" ? "Submit" : "Update"}
      </Button>
    </>
  );
};

const Announcements = () => {
  const { data: session, status } = useSession();
  const [announcements, setAnnouncements] = useState([]);
  const [index, setIndex] = useState(null);
  const [open, setOpen] = useState(false);
  const [openUpdate, setOpenUpdate] = useState(false);
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [removeAnnouncement] = useMutation(mutationRemoveAnnouncement);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [snackbarSeverity, setSnackbarSeverity] = useState("success");

  const handleCloseSnackbar = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    setSnackbarOpen(false);
  };

  useEffect(() => {
    if (status === "authenticated" && !loading && !error) {
      const fetchNews = async () => {
        try {
          setLoading(true);
          let announcementsData = await getAllAnnouncements(session.accessToken);
          console.log("yyyyyyy", announcementsData.data);
          
          
          if (announcementsData.data.length > 1) {
            announcementsData.data = announcementsData.data.sort(
              (a, b) => new Date(b.visibility) - new Date(a.visibility)
            );
          }
          setAnnouncements(announcementsData.data);
        } catch (error) {
          console.log(error);
          setError(error);
          setSnackbarMessage(`Failed to fetch announcements: ${error.message}`);
          setSnackbarSeverity("error");
          setSnackbarOpen(true);
        } finally {
          setLoading(false);
        }
      };
      fetchNews();
    }
  }, [status, session]);

  const deleteHandler = async (deletedId) => {
    try {
      return new Promise(async (res, rej) => {
        let response = await deleteAnnouncement(deletedId, session.accessToken);
        console.log(response);
        if (response?.code == 200) {
          setAnnouncements((prevAnnounc) =>
            prevAnnounc.filter((announcement) => announcement.id !== deletedId)
          );
          setSnackbarMessage("Deleted successfully");
          setSnackbarSeverity("success");
          setSnackbarOpen(true);
          res(response);
        } else {
          setSnackbarMessage(`Deletion failed: ${response?.error}`);
          setSnackbarSeverity("error");
          setSnackbarOpen(true);
          rej(response);
        }
      });
    } catch (error) {
      console.error(error);
      setSnackbarMessage(`Deletion failed: ${error.message}`);
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
      throw error;
    }
  };

  if (status === "loading") {
    return (
      <Dashboard>
        <Box sx={{ display: "flex", justifyContent: "center", margin: "25px" }}>
          <CircularProgress color="secondary" />
        </Box>
      </Dashboard>
    );
  }

  if (!session?.accessToken) {
    return (
      <Dashboard>
        <Box sx={{ margin: "25px" }}>
          <Typography variant="h5">Please log in to view announcements</Typography>
        </Box>
      </Dashboard>
    );
  }

  return (
    <Dashboard>
      <Box sx={{ margin: "25px" }}>
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Typography variant="h5" sx={{ marginY: "10px" }}>
            Announcements
          </Typography>
          <BasicModal
            title={"Add Announcement"}
            comp={
              <AddAnnouncement
                data={data}
                setData={setData}
                opt={"add"}
                announcements={announcements}
                setAnnouncements={setAnnouncements}
                setOpen={setOpen}
                setOpenUpdate={setOpenUpdate}
                setSnackbarMessage={setSnackbarMessage}
                setSnackbarSeverity={setSnackbarSeverity}
                setSnackbarOpen={setSnackbarOpen}
                token={session.accessToken}
                userEmail={session.user.email}
              />
            }
            btn={true}
            open={open}
            setOpen={setOpen}
          />
        </Box>
        <UpdateModal
          title={"Update Announcement"}
          comp={
            <AddAnnouncement
              data={data}
              setData={setData}
              opt={"update"}
              announcements={announcements}
              setAnnouncements={setAnnouncements}
              setOpen={setOpen}
              setOpenUpdate={setOpenUpdate}
              setSnackbarMessage={setSnackbarMessage}
              setSnackbarSeverity={setSnackbarSeverity}
              setSnackbarOpen={setSnackbarOpen}
              token={session.accessToken}
              userEmail={session.user.email}
            />
          }
          btn={false}
          openUpdate={openUpdate}
          setOpenUpdate={setOpenUpdate}
        />
        <SnackbarComponent
          open={snackbarOpen}
          handleClose={handleCloseSnackbar}
          severity={snackbarSeverity}
          message={snackbarMessage}
        />
        <Box
          sx={{
            overflow: "auto",
          }}
        >
          {loading ? (
            <Box sx={{ display: "flex", justifyContent: "center" }}>
              <CircularProgress color="secondary" />
            </Box>
          ) : (
            <>
              {announcements.length > 0 ? (
                <DataTable
                  columns={columns}
                  rows={announcements}
                  setIndex={setIndex}
                  setOpen={setOpenUpdate}
                  setData={setData}
                  deleteHandler={deleteHandler}
                  action={true}
                  updateKey={"title"}
                />
              ) : (
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                  <Typography variant="h5" sx={{ marginY: "10px" }}>
                    No data found
                  </Typography>
                </Box>
              )}
            </>
          )}
        </Box>
      </Box>
    </Dashboard>
  );
};

export default withAdminAuth(Announcements);