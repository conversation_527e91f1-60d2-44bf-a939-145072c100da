import { CreateCategoryInput } from './dto/create-category.input';
import { UpdateCategoryInput } from './dto/update-category.input';
import { Category } from './entities/category.entity';
import { Repository } from 'typeorm';
import { UUID } from 'crypto';
export declare class CategoryService {
    private readonly categoryRepository;
    constructor(categoryRepository: Repository<Category>);
    create(createCategoryInput: CreateCategoryInput): Promise<Category>;
    findAll(): Promise<Category[]>;
    findOne(id: UUID): Promise<Category>;
    update(id: UUID, updateCategoryInput: UpdateCategoryInput): Promise<Category>;
    remove(id: UUID): Promise<Category | null>;
}
