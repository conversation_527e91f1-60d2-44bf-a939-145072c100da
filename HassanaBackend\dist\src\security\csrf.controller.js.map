{"version": 3, "file": "csrf.controller.js", "sourceRoot": "", "sources": ["../../../src/security/csrf.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoF;AAEpF,iDAA6C;AAC7C,6DAAyD;AACzD,6CAAqE;AAI9D,IAAM,cAAc,GAApB,MAAM,cAAc;IACzB,YACmB,WAAwB,EACxB,cAA8B;QAD9B,gBAAW,GAAX,WAAW,CAAa;QACxB,mBAAc,GAAd,cAAc,CAAgB;IAC9C,CAAC;IAgBJ,YAAY,CAAQ,GAAY,EAAS,GAAa;QACpD,IAAI,CAAC;YAEH,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC;YAG1E,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,YAAY,EAAE;gBACxC,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;gBAC7C,QAAQ,EAAE,QAAQ;gBAClB,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;aAC5B,CAAC,CAAC;YAEH,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;gBACpC,SAAS;gBACT,YAAY;gBACZ,OAAO,EAAE,mCAAmC;aAC7C,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,OAAO,EAAE,+BAA+B;gBACxC,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAeD,eAAe,CAAQ,GAAY,EAAS,GAAa;QACvD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAW,CAAC;YACxD,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAW,CAAC;YAE9D,IAAI,CAAC,SAAS,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChC,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;oBAC7C,KAAK,EAAE,KAAK;oBACZ,OAAO,EAAE,2CAA2C;iBACrD,CAAC,CAAC;YACL,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YAE1E,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;gBACpC,KAAK,EAAE,OAAO;gBACd,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,uBAAuB;aACnE,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,KAAK,EAAE,KAAK;gBACZ,OAAO,EAAE,6BAA6B;gBACtC,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,gBAAgB,CAAkB,MAAc,EAAS,GAAa;QAC1E,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;oBAC7C,OAAO,EAAE,8BAA8B;iBACxC,CAAC,CAAC;YACL,CAAC;YAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACrE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE,CAAC;YAErE,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC;gBACpC,MAAM;gBACN,cAAc,EAAE,WAAW;gBAC3B,iBAAiB,EAAE,WAAW;gBAC9B,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,yBAAyB;aAC7E,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC;gBACvD,OAAO,EAAE,8BAA8B;gBACvC,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF,CAAA;AAhHY,wCAAc;AAoBzB;IAdC,IAAA,YAAG,EAAC,YAAY,CAAC;IACjB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,oCAAoC,EAAE,CAAC;IAC/D,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,mCAAmC;QAChD,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAC7B,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBAChC,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC5B;SACF;KACF,CAAC;IACY,WAAA,IAAA,YAAG,GAAE,CAAA;IAAgB,WAAA,IAAA,YAAG,GAAE,CAAA;;;;kDAwBvC;AAeD;IAbC,IAAA,aAAI,EAAC,aAAa,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;IAC5D,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,gCAAgC;QAC7C,MAAM,EAAE;YACN,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;gBAC1B,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;aAC5B;SACF;KACF,CAAC;IACe,WAAA,IAAA,YAAG,GAAE,CAAA;IAAgB,WAAA,IAAA,YAAG,GAAE,CAAA;;;;qDAyB1C;AAIK;IAFL,IAAA,YAAG,EAAC,gBAAgB,CAAC;IACrB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,kCAAkC,EAAE,CAAC;IACtC,WAAA,IAAA,cAAK,EAAC,QAAQ,CAAC,CAAA;IAAkB,WAAA,IAAA,YAAG,GAAE,CAAA;;;;sDAuB7D;yBA/GU,cAAc;IAF1B,IAAA,iBAAO,EAAC,UAAU,CAAC;IACnB,IAAA,mBAAU,EAAC,UAAU,CAAC;qCAGW,0BAAW;QACR,gCAAc;GAHtC,cAAc,CAgH1B"}