import { Modu<PERSON> } from '@nestjs/common';
import { ThrottlerModule } from '@nestjs/throttler';
import { CsrfGuard } from './csrf.guard';
import { CsrfService } from './csrf.service';
import { CsrfController } from './csrf.controller';
import { SecurityConfig } from './security.config';

@Module({
  imports: [
    ThrottlerModule.forRoot([SecurityConfig.getRateLimitConfig()]),
  ],
  controllers: [CsrfController],
  providers: [CsrfGuard, CsrfService, SecurityConfig],
  exports: [CsrfGuard, CsrfService, SecurityConfig],
})
export class SecurityModule {}
