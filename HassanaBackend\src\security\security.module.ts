import { Modu<PERSON> } from '@nestjs/common';
import { ThrottlerModule } from '@nestjs/throttler';
import { CsrfGuard } from './csrf.guard';
import { CsrfService } from './csrf.service';
import { CsrfController } from './csrf.controller';
import { SecurityConfig } from './security.config';
import { SessionService } from '../auth/session.service';

@Module({
  imports: [
    ThrottlerModule.forRoot([SecurityConfig.getRateLimitConfig()]),
  ],
  controllers: [CsrfController],
  providers: [CsrfGuard, CsrfService, SecurityConfig, SessionService],
  exports: [CsrfGuard, CsrfService, SecurityConfig, SessionService],
})
export class SecurityModule {}
