"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./src/components/HassanaOfferPopUp.jsx":
/*!**********************************************!*\
  !*** ./src/components/HassanaOfferPopUp.jsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HassanaOfferPopUp; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _components_HelperFunctions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/HelperFunctions */ \"./src/components/HelperFunctions.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,Grid,TextField,useMediaQuery!=!@mui/material */ \"__barrel_optimize__?names=Alert,Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,Grid,TextField,useMediaQuery!=!./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _components_ColorContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ColorContext */ \"./src/components/ColorContext.jsx\");\n/* harmony import */ var _Data_Offer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Data/Offer */ \"./src/Data/Offer.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _utils_graphqlAuth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils/graphqlAuth */ \"./src/utils/graphqlAuth.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction HassanaOfferPopUp(param) {\n    let { open, handleClose, offer, refetchOffers } = param;\n    _s();\n    const theme = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_7__.useTheme)();\n    const isSmallScreen = (0,_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.useMediaQuery)(theme.breakpoints.down(\"sm\"));\n    const { color } = (0,_components_ColorContext__WEBPACK_IMPORTED_MODULE_3__.useColor)();\n    const selectedColor = (0,_components_HelperFunctions__WEBPACK_IMPORTED_MODULE_2__.useSelectedColor)(color);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: \"\",\n        name: \"\",\n        contact_information: \"\",\n        code: \"\",\n        expiry_date: \"\",\n        description: \"\",\n        status: true\n    });\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_5__.useSession)();\n    const resetForm = ()=>{\n        setFormData({\n            id: \"\",\n            name: \"\",\n            contact_information: \"\",\n            code: \"\",\n            expiry_date: \"\",\n            description: \"\",\n            status: true\n        });\n        setFormErrors({});\n        setError(null);\n        setSuccess(null);\n    };\n    // Apollo mutation hooks\n    const [createOffer, { loading: createLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_9__.useMutation)(_Data_Offer__WEBPACK_IMPORTED_MODULE_4__.CREATE_OFFER, {\n        onCompleted: ()=>{\n            setSuccess(\"Offer created successfully!\");\n            refetchOffers();\n            setTimeout(()=>{\n                resetForm();\n                handleClose();\n            }, 2000);\n        },\n        onError: (err)=>{\n            var _err_graphQLErrors_, _err_graphQLErrors, _err_networkError_result_errors_, _err_networkError_result_errors, _err_networkError_result, _err_networkError;\n            console.error(\"Create offer error:\", JSON.stringify(err, null, 2));\n            const message = ((_err_graphQLErrors = err.graphQLErrors) === null || _err_graphQLErrors === void 0 ? void 0 : (_err_graphQLErrors_ = _err_graphQLErrors[0]) === null || _err_graphQLErrors_ === void 0 ? void 0 : _err_graphQLErrors_.message) || ((_err_networkError = err.networkError) === null || _err_networkError === void 0 ? void 0 : (_err_networkError_result = _err_networkError.result) === null || _err_networkError_result === void 0 ? void 0 : (_err_networkError_result_errors = _err_networkError_result.errors) === null || _err_networkError_result_errors === void 0 ? void 0 : (_err_networkError_result_errors_ = _err_networkError_result_errors[0]) === null || _err_networkError_result_errors_ === void 0 ? void 0 : _err_networkError_result_errors_.message) || err.message || \"Error creating offer\";\n            if (message.includes(\"code is already registered\")) {\n                setFormErrors({\n                    code: \"This code is already registered\"\n                });\n            } else {\n                setError(message);\n            }\n        }\n    });\n    const [updateOffer, { loading: updateLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_9__.useMutation)(_Data_Offer__WEBPACK_IMPORTED_MODULE_4__.UPDATE_OFFER, {\n        onCompleted: ()=>{\n            setSuccess(\"Offer updated successfully!\");\n            refetchOffers();\n            setTimeout(()=>{\n                resetForm();\n                handleClose();\n            }, 2000);\n        },\n        onError: (err)=>{\n            var _err_graphQLErrors_, _err_graphQLErrors, _err_networkError_result_errors_, _err_networkError_result_errors, _err_networkError_result, _err_networkError;\n            console.error(\"Update offer error:\", JSON.stringify(err, null, 2));\n            const message = ((_err_graphQLErrors = err.graphQLErrors) === null || _err_graphQLErrors === void 0 ? void 0 : (_err_graphQLErrors_ = _err_graphQLErrors[0]) === null || _err_graphQLErrors_ === void 0 ? void 0 : _err_graphQLErrors_.message) || ((_err_networkError = err.networkError) === null || _err_networkError === void 0 ? void 0 : (_err_networkError_result = _err_networkError.result) === null || _err_networkError_result === void 0 ? void 0 : (_err_networkError_result_errors = _err_networkError_result.errors) === null || _err_networkError_result_errors === void 0 ? void 0 : (_err_networkError_result_errors_ = _err_networkError_result_errors[0]) === null || _err_networkError_result_errors_ === void 0 ? void 0 : _err_networkError_result_errors_.message) || err.message || \"Error updating offer\";\n            if (message.includes(\"code is already registered\")) {\n                setFormErrors({\n                    code: \"This code is already registered\"\n                });\n            } else {\n                setError(message);\n            }\n        }\n    });\n    // Pre-fill form for edit mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"Received offer for edit:\", JSON.stringify(offer, null, 2));\n        if (offer && offer.id) {\n            var _offer_status;\n            setFormData({\n                id: offer.id || \"\",\n                name: offer.name || \"\",\n                contact_information: offer.contact_information || \"\",\n                code: offer.code || \"\",\n                expiry_date: offer.expiry_date ? new Date(offer.expiry_date).toISOString().slice(0, 16) : \"\",\n                description: offer.description || \"\",\n                status: (_offer_status = offer.status) !== null && _offer_status !== void 0 ? _offer_status : true\n            });\n            var _offer_status1;\n            console.log(\"Form data set to:\", JSON.stringify({\n                id: offer.id || \"\",\n                name: offer.name || \"\",\n                contact_information: offer.contact_information || \"\",\n                code: offer.code || \"\",\n                expiry_date: offer.expiry_date ? new Date(offer.expiry_date).toISOString().slice(0, 16) : \"\",\n                description: offer.description || \"\",\n                status: (_offer_status1 = offer.status) !== null && _offer_status1 !== void 0 ? _offer_status1 : true\n            }, null, 2));\n        } else {\n            setFormData({\n                id: \"\",\n                name: \"\",\n                contact_information: \"\",\n                code: \"\",\n                expiry_date: \"\",\n                description: \"\",\n                status: true\n            });\n        }\n    }, [\n        offer\n    ]);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const FormAction = async (event)=>{\n        var _session_user;\n        event.preventDefault();\n        setError(null);\n        setSuccess(null);\n        setFormErrors({});\n        if (!(session === null || session === void 0 ? void 0 : session.accessToken)) {\n            setError(\"Authentication token missing. Please log in again.\");\n            return;\n        }\n        if (!(session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id)) {\n            setError(\"User ID missing. Please log in again.\");\n            return;\n        }\n        const expiryDateRaw = formData.expiry_date;\n        let expiryDate = null;\n        if (expiryDateRaw) {\n            const date = new Date(expiryDateRaw);\n            if (!isNaN(date.getTime())) {\n                expiryDate = date.toISOString();\n            }\n        }\n        const data = {\n            id: formData.id,\n            name: formData.name.trim() || \"\",\n            contact_information: formData.contact_information.trim() || null,\n            code: formData.code.trim() || \"\",\n            expiry_date: expiryDate,\n            description: formData.description.trim() || null,\n            status: formData.status,\n            updated_by: session.user.id\n        };\n        // Client-side validation\n        const errors = {};\n        if (!data.name) errors.name = \"Name is required\";\n        if (!data.code) errors.code = \"Code is required\";\n        if (!data.expiry_date) {\n            errors.expiry_date = \"Valid expiry date is required\";\n        } else if (new Date(data.expiry_date) < new Date()) {\n            errors.expiry_date = \"Expiry date must be in the future\";\n        }\n        if (data.code && !/^HASSANA-\\d+$/.test(data.code)) {\n            errors.code = \"Code must follow format HASSANA-123\";\n        }\n        if (formData.id && !data.id) {\n            errors.id = \"Offer ID is required for updating\";\n            setError(\"Cannot update offer: Invalid ID format.\");\n        }\n        if (Object.keys(errors).length > 0) {\n            setFormErrors(errors);\n            return;\n        }\n        console.log(\"Mutation input:\", JSON.stringify(data, null, 2));\n        console.log(\"Session:\", JSON.stringify(session, null, 2));\n        if (formData.id) {\n            // Update mode\n            console.log(\"Calling updateOffer with id and updateOffersInput:\", JSON.stringify({\n                id: data.id,\n                updateOffersInput: {\n                    name: data.name,\n                    contact_information: data.contact_information,\n                    code: data.code,\n                    expiry_date: data.expiry_date,\n                    description: data.description,\n                    status: data.status,\n                    updated_by: data.updated_by\n                }\n            }, null, 2));\n            await updateOffer({\n                variables: {\n                    id: data.id,\n                    updateOffersInput: {\n                        name: data.name,\n                        contact_information: data.contact_information,\n                        code: data.code,\n                        expiry_date: data.expiry_date,\n                        description: data.description,\n                        status: data.status,\n                        updated_by: data.updated_by\n                    }\n                },\n                context: {\n                    headers: {\n                        Authorization: \"Bearer \".concat(session.accessToken)\n                    }\n                }\n            });\n        } else {\n            // Create mode\n            console.log(\"Calling createOffer with createOffersInput:\", JSON.stringify({\n                name: data.name,\n                contact_information: data.contact_information,\n                code: data.code,\n                expiry_date: data.expiry_date,\n                description: data.description,\n                status: true,\n                created_by: session.user.id,\n                updated_by: data.updated_by\n            }, null, 2));\n            await createOffer({\n                variables: {\n                    createOffersInput: {\n                        name: data.name,\n                        contact_information: data.contact_information,\n                        code: data.code,\n                        expiry_date: data.expiry_date,\n                        description: data.description,\n                        status: true,\n                        created_by: session.user.id,\n                        updated_by: data.updated_by\n                    }\n                },\n                context: {\n                    headers: {\n                        Authorization: \"Bearer \".concat(session.accessToken)\n                    }\n                }\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Box, {\n        sx: {\n            background: \"#fff\",\n            color: theme.palette.text.primary,\n            padding: isSmallScreen ? \"2px\" : \"5px\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Dialog, {\n            open: open,\n            onClose: handleClose,\n            fullScreen: isSmallScreen,\n            maxWidth: \"md\",\n            fullWidth: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.DialogTitle, {\n                    sx: {\n                        textAlign: \"left\",\n                        fontSize: isSmallScreen ? \"1.2rem\" : \"1.5rem\",\n                        background: \"#fff\",\n                        color: theme.palette.text.primary\n                    },\n                    children: formData.id ? \"Edit Offer\" : \"Create Offer\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.DialogContent, {\n                    sx: {\n                        background: \"#fff\",\n                        color: theme.palette.text.primary,\n                        padding: isSmallScreen ? \"2px\" : \"5px\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Box, {\n                        sx: {\n                            color: theme.palette.text.primary,\n                            borderRadius: \"10px\",\n                            boxShadow: \"0px 4px 20px 0px rgba(0, 0, 0, 0.05)\",\n                            padding: isSmallScreen ? \"5px\" : \"10px\",\n                            background: \"#fff\"\n                        },\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                severity: \"error\",\n                                sx: {\n                                    mb: 2\n                                },\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, this),\n                            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                severity: \"success\",\n                                sx: {\n                                    mb: 2\n                                },\n                                children: success\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: FormAction,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.TextField, {\n                                        margin: \"normal\",\n                                        required: true,\n                                        fullWidth: true,\n                                        name: \"name\",\n                                        label: \"Name\",\n                                        placeholder: \"Enter Offer Name\",\n                                        variant: \"outlined\",\n                                        value: formData.name,\n                                        onChange: handleInputChange,\n                                        error: !!formErrors.name,\n                                        helperText: formErrors.name,\n                                        InputProps: {\n                                            sx: {\n                                                height: 52\n                                            }\n                                        },\n                                        sx: {\n                                            mb: 1,\n                                            background: \"#fff\",\n                                            borderRadius: \"8px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.TextField, {\n                                        margin: \"normal\",\n                                        name: \"contact_information\",\n                                        fullWidth: true,\n                                        type: \"tel\",\n                                        label: \"Contact Information\",\n                                        placeholder: \"Enter your contact (e.g., email or phone)\",\n                                        variant: \"outlined\",\n                                        value: formData.contact_information,\n                                        onChange: handleInputChange,\n                                        InputProps: {\n                                            sx: {\n                                                height: 52\n                                            }\n                                        },\n                                        sx: {\n                                            mb: 1,\n                                            background: \"#fff\",\n                                            borderRadius: \"8px\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Grid, {\n                                        container: true,\n                                        spacing: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Grid, {\n                                                item: true,\n                                                xs: 12,\n                                                sm: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.TextField, {\n                                                    margin: \"normal\",\n                                                    fullWidth: true,\n                                                    required: true,\n                                                    name: \"code\",\n                                                    label: \"Code\",\n                                                    placeholder: \"HASSANA-12345\",\n                                                    value: formData.code,\n                                                    onChange: handleInputChange,\n                                                    error: !!formErrors.code,\n                                                    helperText: formErrors.code,\n                                                    InputLabelProps: {\n                                                        shrink: true\n                                                    },\n                                                    variant: \"outlined\",\n                                                    InputProps: {\n                                                        sx: {\n                                                            height: 52\n                                                        }\n                                                    },\n                                                    sx: {\n                                                        background: \"#fff\",\n                                                        borderRadius: \"8px\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Grid, {\n                                                item: true,\n                                                xs: 12,\n                                                sm: 6,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.TextField, {\n                                                    margin: \"normal\",\n                                                    fullWidth: true,\n                                                    required: true,\n                                                    name: \"expiry_date\",\n                                                    label: \"Expiry Date\",\n                                                    type: \"datetime-local\",\n                                                    value: formData.expiry_date,\n                                                    onChange: handleInputChange,\n                                                    error: !!formErrors.expiry_date,\n                                                    helperText: formErrors.expiry_date,\n                                                    InputLabelProps: {\n                                                        shrink: true\n                                                    },\n                                                    variant: \"outlined\",\n                                                    InputProps: {\n                                                        sx: {\n                                                            height: 52\n                                                        },\n                                                        inputProps: {\n                                                            min: new Date().toISOString().slice(0, 16)\n                                                        }\n                                                    },\n                                                    sx: {\n                                                        background: \"#fff\",\n                                                        borderRadius: \"8px\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.TextField, {\n                                        margin: \"normal\",\n                                        fullWidth: true,\n                                        name: \"description\",\n                                        label: \"Description\",\n                                        placeholder: \"Enter offer description\",\n                                        variant: \"outlined\",\n                                        value: formData.description,\n                                        onChange: handleInputChange,\n                                        multiline: true,\n                                        rows: 2,\n                                        sx: {\n                                            borderRadius: \"8px\",\n                                            background: \"#fff\",\n                                            mb: 1\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.DialogActions, {\n                                        sx: {\n                                            background: \"#fff\",\n                                            padding: isSmallScreen ? \"5px\" : \"10px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Grid, {\n                                            container: true,\n                                            spacing: 2,\n                                            justifyContent: \"center\",\n                                            sx: {\n                                                background: \"#fff\",\n                                                padding: isSmallScreen ? \"5px\" : \"10px\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Grid, {\n                                                    item: true,\n                                                    xs: 12,\n                                                    sm: \"auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        sx: {\n                                                            width: isSmallScreen ? \"100%\" : \"auto\",\n                                                            px: 4\n                                                        },\n                                                        variant: \"outlined\",\n                                                        color: \"secondary\",\n                                                        onClick: handleClose,\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Grid, {\n                                                    item: true,\n                                                    xs: 12,\n                                                    sm: \"auto\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                        sx: {\n                                                            width: isSmallScreen ? \"100%\" : \"auto\",\n                                                            px: 4\n                                                        },\n                                                        variant: \"contained\",\n                                                        color: \"secondary\",\n                                                        type: \"submit\",\n                                                        disabled: createLoading || updateLoading,\n                                                        style: {\n                                                            backgroundColor: theme.palette.mode === \"black\" ? \"#7B1FA2\" : \"#9C27B0\"\n                                                        },\n                                                        children: createLoading || updateLoading ? \"Submitting...\" : formData.id ? \"Update\" : \"Create\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n            lineNumber: 286,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\Projects\\\\HBack\\\\Hassana-Fronted\\\\src\\\\components\\\\HassanaOfferPopUp.jsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(HassanaOfferPopUp, \"D6t1cTx0wrM7kVCl/DyeyLmTYiM=\", false, function() {\n    return [\n        _mui_material_styles__WEBPACK_IMPORTED_MODULE_7__.useTheme,\n        _barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_Grid_TextField_useMediaQuery_mui_material__WEBPACK_IMPORTED_MODULE_8__.useMediaQuery,\n        _components_ColorContext__WEBPACK_IMPORTED_MODULE_3__.useColor,\n        _components_HelperFunctions__WEBPACK_IMPORTED_MODULE_2__.useSelectedColor,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_5__.useSession,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_9__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_9__.useMutation\n    ];\n});\n_c = HassanaOfferPopUp;\nvar _c;\n$RefreshReg$(_c, \"HassanaOfferPopUp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9IYXNzYW5hT2ZmZXJQb3BVcC5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE0QztBQUNDO0FBQ21CO0FBWXpDO0FBQ3lCO0FBQ0s7QUFDTTtBQUNkO0FBQ3VEO0FBRXJGLFNBQVNzQixrQkFBa0IsS0FBMkM7UUFBM0MsRUFBRUMsSUFBSSxFQUFFQyxXQUFXLEVBQUVDLEtBQUssRUFBRUMsYUFBYSxFQUFFLEdBQTNDOztJQUN4QyxNQUFNQyxRQUFRYiw4REFBUUE7SUFDdEIsTUFBTWMsZ0JBQWdCaEIsK0tBQWFBLENBQUNlLE1BQU1FLFdBQVcsQ0FBQ0MsSUFBSSxDQUFDO0lBQzNELE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUdoQixrRUFBUUE7SUFDMUIsTUFBTWlCLGdCQUFnQjdCLDZFQUFnQkEsQ0FBQzRCO0lBRXZDLE1BQU0sQ0FBQ0UsT0FBT0MsU0FBUyxHQUFHbEMsK0NBQVFBLENBQUM7SUFDbkMsTUFBTSxDQUFDbUMsU0FBU0MsV0FBVyxHQUFHcEMsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDcUMsWUFBWUMsY0FBYyxHQUFHdEMsK0NBQVFBLENBQUMsQ0FBQztJQUM5QyxNQUFNLENBQUN1QyxVQUFVQyxZQUFZLEdBQUd4QywrQ0FBUUEsQ0FBQztRQUN2Q3lDLElBQUk7UUFDSkMsTUFBTTtRQUNOQyxxQkFBcUI7UUFDckJDLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFFBQVE7SUFDVjtJQUVBLE1BQU0sRUFBRUMsTUFBTUMsT0FBTyxFQUFFLEdBQUcvQiwyREFBVUE7SUFDcEMsTUFBTWdDLFlBQVk7UUFDaEJWLFlBQVk7WUFDVkMsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLHFCQUFxQjtZQUNyQkMsTUFBTTtZQUNOQyxhQUFhO1lBQ2JDLGFBQWE7WUFDYkMsUUFBUTtRQUNWO1FBQ0FULGNBQWMsQ0FBQztRQUNmSixTQUFTO1FBQ1RFLFdBQVc7SUFDYjtJQUVBLHdCQUF3QjtJQUN4QixNQUFNLENBQUNlLGFBQWEsRUFBRUMsU0FBU0MsYUFBYSxFQUFFLENBQUMsR0FBR25ELDJEQUFXQSxDQUFDYyxxREFBWUEsRUFBRTtRQUMxRXNDLGFBQWE7WUFDWGxCLFdBQVc7WUFDWFY7WUFDQTZCLFdBQVc7Z0JBQ1RMO2dCQUNBMUI7WUFDRixHQUFHO1FBQ0w7UUFDQWdDLFNBQVMsQ0FBQ0M7Z0JBR05BLHFCQUFBQSxvQkFDQUEsa0NBQUFBLGlDQUFBQSwwQkFBQUE7WUFIRkMsUUFBUXpCLEtBQUssQ0FBQyx1QkFBdUIwQixLQUFLQyxTQUFTLENBQUNILEtBQUssTUFBTTtZQUMvRCxNQUFNSSxVQUNKSixFQUFBQSxxQkFBQUEsSUFBSUssYUFBYSxjQUFqQkwsMENBQUFBLHNCQUFBQSxrQkFBbUIsQ0FBQyxFQUFFLGNBQXRCQSwwQ0FBQUEsb0JBQXdCSSxPQUFPLE9BQy9CSixvQkFBQUEsSUFBSU0sWUFBWSxjQUFoQk4seUNBQUFBLDJCQUFBQSxrQkFBa0JPLE1BQU0sY0FBeEJQLGdEQUFBQSxrQ0FBQUEseUJBQTBCUSxNQUFNLGNBQWhDUix1REFBQUEsbUNBQUFBLCtCQUFrQyxDQUFDLEVBQUUsY0FBckNBLHVEQUFBQSxpQ0FBdUNJLE9BQU8sS0FDOUNKLElBQUlJLE9BQU8sSUFDWDtZQUNGLElBQUlBLFFBQVFLLFFBQVEsQ0FBQywrQkFBK0I7Z0JBQ2xENUIsY0FBYztvQkFBRU0sTUFBTTtnQkFBa0M7WUFDMUQsT0FBTztnQkFDTFYsU0FBUzJCO1lBQ1g7UUFDRjtJQUNGO0lBRUEsTUFBTSxDQUFDTSxhQUFhLEVBQUVmLFNBQVNnQixhQUFhLEVBQUUsQ0FBQyxHQUFHbEUsMkRBQVdBLENBQUNlLHFEQUFZQSxFQUFFO1FBQzFFcUMsYUFBYTtZQUNYbEIsV0FBVztZQUNYVjtZQUNBNkIsV0FBVztnQkFDVEw7Z0JBQ0ExQjtZQUNGLEdBQUc7UUFDTDtRQUNBZ0MsU0FBUyxDQUFDQztnQkFHTkEscUJBQUFBLG9CQUNBQSxrQ0FBQUEsaUNBQUFBLDBCQUFBQTtZQUhGQyxRQUFRekIsS0FBSyxDQUFDLHVCQUF1QjBCLEtBQUtDLFNBQVMsQ0FBQ0gsS0FBSyxNQUFNO1lBQy9ELE1BQU1JLFVBQ0pKLEVBQUFBLHFCQUFBQSxJQUFJSyxhQUFhLGNBQWpCTCwwQ0FBQUEsc0JBQUFBLGtCQUFtQixDQUFDLEVBQUUsY0FBdEJBLDBDQUFBQSxvQkFBd0JJLE9BQU8sT0FDL0JKLG9CQUFBQSxJQUFJTSxZQUFZLGNBQWhCTix5Q0FBQUEsMkJBQUFBLGtCQUFrQk8sTUFBTSxjQUF4QlAsZ0RBQUFBLGtDQUFBQSx5QkFBMEJRLE1BQU0sY0FBaENSLHVEQUFBQSxtQ0FBQUEsK0JBQWtDLENBQUMsRUFBRSxjQUFyQ0EsdURBQUFBLGlDQUF1Q0ksT0FBTyxLQUM5Q0osSUFBSUksT0FBTyxJQUNYO1lBQ0YsSUFBSUEsUUFBUUssUUFBUSxDQUFDLCtCQUErQjtnQkFDbEQ1QixjQUFjO29CQUFFTSxNQUFNO2dCQUFrQztZQUMxRCxPQUFPO2dCQUNMVixTQUFTMkI7WUFDWDtRQUNGO0lBQ0Y7SUFFQSw4QkFBOEI7SUFDOUI1RCxnREFBU0EsQ0FBQztRQUNSeUQsUUFBUVcsR0FBRyxDQUFDLDRCQUE0QlYsS0FBS0MsU0FBUyxDQUFDbkMsT0FBTyxNQUFNO1FBQ3BFLElBQUlBLFNBQVNBLE1BQU1nQixFQUFFLEVBQUU7Z0JBVVhoQjtZQVRWZSxZQUFZO2dCQUNWQyxJQUFJaEIsTUFBTWdCLEVBQUUsSUFBSTtnQkFDaEJDLE1BQU1qQixNQUFNaUIsSUFBSSxJQUFJO2dCQUNwQkMscUJBQXFCbEIsTUFBTWtCLG1CQUFtQixJQUFJO2dCQUNsREMsTUFBTW5CLE1BQU1tQixJQUFJLElBQUk7Z0JBQ3BCQyxhQUFhcEIsTUFBTW9CLFdBQVcsR0FDMUIsSUFBSXlCLEtBQUs3QyxNQUFNb0IsV0FBVyxFQUFFMEIsV0FBVyxHQUFHQyxLQUFLLENBQUMsR0FBRyxNQUNuRDtnQkFDSjFCLGFBQWFyQixNQUFNcUIsV0FBVyxJQUFJO2dCQUNsQ0MsUUFBUXRCLENBQUFBLGdCQUFBQSxNQUFNc0IsTUFBTSxjQUFadEIsMkJBQUFBLGdCQUFnQjtZQUMxQjtnQkFVVUE7WUFUVmlDLFFBQVFXLEdBQUcsQ0FBQyxxQkFBcUJWLEtBQUtDLFNBQVMsQ0FBQztnQkFDOUNuQixJQUFJaEIsTUFBTWdCLEVBQUUsSUFBSTtnQkFDaEJDLE1BQU1qQixNQUFNaUIsSUFBSSxJQUFJO2dCQUNwQkMscUJBQXFCbEIsTUFBTWtCLG1CQUFtQixJQUFJO2dCQUNsREMsTUFBTW5CLE1BQU1tQixJQUFJLElBQUk7Z0JBQ3BCQyxhQUFhcEIsTUFBTW9CLFdBQVcsR0FDMUIsSUFBSXlCLEtBQUs3QyxNQUFNb0IsV0FBVyxFQUFFMEIsV0FBVyxHQUFHQyxLQUFLLENBQUMsR0FBRyxNQUNuRDtnQkFDSjFCLGFBQWFyQixNQUFNcUIsV0FBVyxJQUFJO2dCQUNsQ0MsUUFBUXRCLENBQUFBLGlCQUFBQSxNQUFNc0IsTUFBTSxjQUFadEIsNEJBQUFBLGlCQUFnQjtZQUMxQixHQUFHLE1BQU07UUFDWCxPQUFPO1lBQ0xlLFlBQVk7Z0JBQ1ZDLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLHFCQUFxQjtnQkFDckJDLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLGFBQWE7Z0JBQ2JDLFFBQVE7WUFDVjtRQUNGO0lBQ0YsR0FBRztRQUFDdEI7S0FBTTtJQUVWLE1BQU1nRCxvQkFBb0IsQ0FBQ0M7UUFDekIsTUFBTSxFQUFFaEMsSUFBSSxFQUFFaUMsS0FBSyxFQUFFLEdBQUdELEVBQUVFLE1BQU07UUFDaENwQyxZQUFZLENBQUNxQyxPQUFVO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUUsQ0FBQ25DLEtBQUssRUFBRWlDO1lBQU07SUFDbEQ7SUFFQSxNQUFNRyxhQUFhLE9BQU9DO1lBV25COUI7UUFWTDhCLE1BQU1DLGNBQWM7UUFDcEI5QyxTQUFTO1FBQ1RFLFdBQVc7UUFDWEUsY0FBYyxDQUFDO1FBRWYsSUFBSSxFQUFDVyxvQkFBQUEsOEJBQUFBLFFBQVNnQyxXQUFXLEdBQUU7WUFDekIvQyxTQUFTO1lBQ1Q7UUFDRjtRQUVBLElBQUksRUFBQ2Usb0JBQUFBLCtCQUFBQSxnQkFBQUEsUUFBU2lDLElBQUksY0FBYmpDLG9DQUFBQSxjQUFlUixFQUFFLEdBQUU7WUFDdEJQLFNBQVM7WUFDVDtRQUNGO1FBRUEsTUFBTWlELGdCQUFnQjVDLFNBQVNNLFdBQVc7UUFDMUMsSUFBSXVDLGFBQWE7UUFDakIsSUFBSUQsZUFBZTtZQUNqQixNQUFNRSxPQUFPLElBQUlmLEtBQUthO1lBQ3RCLElBQUksQ0FBQ0csTUFBTUQsS0FBS0UsT0FBTyxLQUFLO2dCQUMxQkgsYUFBYUMsS0FBS2QsV0FBVztZQUMvQjtRQUNGO1FBRUEsTUFBTXZCLE9BQU87WUFDWFAsSUFBSUYsU0FBU0UsRUFBRTtZQUNmQyxNQUFNSCxTQUFTRyxJQUFJLENBQUM4QyxJQUFJLE1BQU07WUFDOUI3QyxxQkFBcUJKLFNBQVNJLG1CQUFtQixDQUFDNkMsSUFBSSxNQUFNO1lBQzVENUMsTUFBTUwsU0FBU0ssSUFBSSxDQUFDNEMsSUFBSSxNQUFNO1lBQzlCM0MsYUFBYXVDO1lBQ2J0QyxhQUFhUCxTQUFTTyxXQUFXLENBQUMwQyxJQUFJLE1BQU07WUFDNUN6QyxRQUFRUixTQUFTUSxNQUFNO1lBQ3ZCMEMsWUFBWXhDLFFBQVFpQyxJQUFJLENBQUN6QyxFQUFFO1FBQzdCO1FBRUEseUJBQXlCO1FBQ3pCLE1BQU13QixTQUFTLENBQUM7UUFDaEIsSUFBSSxDQUFDakIsS0FBS04sSUFBSSxFQUFFdUIsT0FBT3ZCLElBQUksR0FBRztRQUM5QixJQUFJLENBQUNNLEtBQUtKLElBQUksRUFBRXFCLE9BQU9yQixJQUFJLEdBQUc7UUFDOUIsSUFBSSxDQUFDSSxLQUFLSCxXQUFXLEVBQUU7WUFDckJvQixPQUFPcEIsV0FBVyxHQUFHO1FBQ3ZCLE9BQU8sSUFBSSxJQUFJeUIsS0FBS3RCLEtBQUtILFdBQVcsSUFBSSxJQUFJeUIsUUFBUTtZQUNsREwsT0FBT3BCLFdBQVcsR0FBRztRQUN2QjtRQUNBLElBQUlHLEtBQUtKLElBQUksSUFBSSxDQUFDLGdCQUFnQjhDLElBQUksQ0FBQzFDLEtBQUtKLElBQUksR0FBRztZQUNqRHFCLE9BQU9yQixJQUFJLEdBQUc7UUFDaEI7UUFDQSxJQUFJTCxTQUFTRSxFQUFFLElBQUksQ0FBQ08sS0FBS1AsRUFBRSxFQUFFO1lBQzNCd0IsT0FBT3hCLEVBQUUsR0FBRztZQUNaUCxTQUFTO1FBQ1g7UUFDQSxJQUFJeUQsT0FBT0MsSUFBSSxDQUFDM0IsUUFBUTRCLE1BQU0sR0FBRyxHQUFHO1lBQ2xDdkQsY0FBYzJCO1lBQ2Q7UUFDRjtRQUVBUCxRQUFRVyxHQUFHLENBQUMsbUJBQW1CVixLQUFLQyxTQUFTLENBQUNaLE1BQU0sTUFBTTtRQUMxRFUsUUFBUVcsR0FBRyxDQUFDLFlBQVlWLEtBQUtDLFNBQVMsQ0FBQ1gsU0FBUyxNQUFNO1FBRXRELElBQUlWLFNBQVNFLEVBQUUsRUFBRTtZQUNmLGNBQWM7WUFDZGlCLFFBQVFXLEdBQUcsQ0FBQyxzREFBc0RWLEtBQUtDLFNBQVMsQ0FBQztnQkFDL0VuQixJQUFJTyxLQUFLUCxFQUFFO2dCQUNYcUQsbUJBQW1CO29CQUNqQnBELE1BQU1NLEtBQUtOLElBQUk7b0JBQ2ZDLHFCQUFxQkssS0FBS0wsbUJBQW1CO29CQUM3Q0MsTUFBTUksS0FBS0osSUFBSTtvQkFDZkMsYUFBYUcsS0FBS0gsV0FBVztvQkFDN0JDLGFBQWFFLEtBQUtGLFdBQVc7b0JBQzdCQyxRQUFRQyxLQUFLRCxNQUFNO29CQUNuQjBDLFlBQVl6QyxLQUFLeUMsVUFBVTtnQkFDN0I7WUFDRixHQUFHLE1BQU07WUFFVCxNQUFNdEIsWUFBWTtnQkFDaEI0QixXQUFXO29CQUNUdEQsSUFBSU8sS0FBS1AsRUFBRTtvQkFDWHFELG1CQUFtQjt3QkFDakJwRCxNQUFNTSxLQUFLTixJQUFJO3dCQUNmQyxxQkFBcUJLLEtBQUtMLG1CQUFtQjt3QkFDN0NDLE1BQU1JLEtBQUtKLElBQUk7d0JBQ2ZDLGFBQWFHLEtBQUtILFdBQVc7d0JBQzdCQyxhQUFhRSxLQUFLRixXQUFXO3dCQUM3QkMsUUFBUUMsS0FBS0QsTUFBTTt3QkFDbkIwQyxZQUFZekMsS0FBS3lDLFVBQVU7b0JBQzdCO2dCQUNGO2dCQUNBTyxTQUFTO29CQUNQQyxTQUFTO3dCQUNQQyxlQUFlLFVBQThCLE9BQXBCakQsUUFBUWdDLFdBQVc7b0JBQzlDO2dCQUNGO1lBQ0Y7UUFDRixPQUFPO1lBQ0wsY0FBYztZQUNkdkIsUUFBUVcsR0FBRyxDQUFDLCtDQUErQ1YsS0FBS0MsU0FBUyxDQUFDO2dCQUN4RWxCLE1BQU1NLEtBQUtOLElBQUk7Z0JBQ2ZDLHFCQUFxQkssS0FBS0wsbUJBQW1CO2dCQUM3Q0MsTUFBTUksS0FBS0osSUFBSTtnQkFDZkMsYUFBYUcsS0FBS0gsV0FBVztnQkFDN0JDLGFBQWFFLEtBQUtGLFdBQVc7Z0JBQzdCQyxRQUFRO2dCQUNSb0QsWUFBWWxELFFBQVFpQyxJQUFJLENBQUN6QyxFQUFFO2dCQUMzQmdELFlBQVl6QyxLQUFLeUMsVUFBVTtZQUM3QixHQUFHLE1BQU07WUFDVCxNQUFNdEMsWUFBWTtnQkFDaEI0QyxXQUFXO29CQUNUSyxtQkFBbUI7d0JBQ2pCMUQsTUFBTU0sS0FBS04sSUFBSTt3QkFDZkMscUJBQXFCSyxLQUFLTCxtQkFBbUI7d0JBQzdDQyxNQUFNSSxLQUFLSixJQUFJO3dCQUNmQyxhQUFhRyxLQUFLSCxXQUFXO3dCQUM3QkMsYUFBYUUsS0FBS0YsV0FBVzt3QkFDN0JDLFFBQVE7d0JBQ1JvRCxZQUFZbEQsUUFBUWlDLElBQUksQ0FBQ3pDLEVBQUU7d0JBQzNCZ0QsWUFBWXpDLEtBQUt5QyxVQUFVO29CQUM3QjtnQkFDRjtnQkFDQU8sU0FBUztvQkFDUEMsU0FBUzt3QkFDUEMsZUFBZSxVQUE4QixPQUFwQmpELFFBQVFnQyxXQUFXO29CQUM5QztnQkFDRjtZQUNGO1FBQ0Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDdEUsaUtBQUdBO1FBQ0YwRixJQUFJO1lBQ0ZDLFlBQVk7WUFDWnZFLE9BQU9KLE1BQU00RSxPQUFPLENBQUNDLElBQUksQ0FBQ0MsT0FBTztZQUNqQ0MsU0FBUzlFLGdCQUFnQixRQUFRO1FBQ25DO2tCQUVBLDRFQUFDdkIsb0tBQU1BO1lBQ0xrQixNQUFNQTtZQUNOb0YsU0FBU25GO1lBQ1RvRixZQUFZaEY7WUFDWmlGLFVBQVM7WUFDVEMsU0FBUzs7OEJBRVQsOERBQUN0Ryx5S0FBV0E7b0JBQ1Y2RixJQUFJO3dCQUNGVSxXQUFXO3dCQUNYQyxVQUFVcEYsZ0JBQWdCLFdBQVc7d0JBQ3JDMEUsWUFBWTt3QkFDWnZFLE9BQU9KLE1BQU00RSxPQUFPLENBQUNDLElBQUksQ0FBQ0MsT0FBTztvQkFDbkM7OEJBRUNsRSxTQUFTRSxFQUFFLEdBQUcsZUFBZTs7Ozs7OzhCQUVoQyw4REFBQ2xDLDJLQUFhQTtvQkFDWjhGLElBQUk7d0JBQ0ZDLFlBQVk7d0JBQ1p2RSxPQUFPSixNQUFNNEUsT0FBTyxDQUFDQyxJQUFJLENBQUNDLE9BQU87d0JBQ2pDQyxTQUFTOUUsZ0JBQWdCLFFBQVE7b0JBQ25DOzhCQUVBLDRFQUFDakIsaUtBQUdBO3dCQUNGMEYsSUFBSTs0QkFDRnRFLE9BQU9KLE1BQU00RSxPQUFPLENBQUNDLElBQUksQ0FBQ0MsT0FBTzs0QkFDakNRLGNBQWM7NEJBQ2RDLFdBQVc7NEJBQ1hSLFNBQVM5RSxnQkFBZ0IsUUFBUTs0QkFDakMwRSxZQUFZO3dCQUNkOzs0QkFFQ3JFLHVCQUNDLDhEQUFDcEIsbUtBQUtBO2dDQUFDc0csVUFBUztnQ0FBUWQsSUFBSTtvQ0FBRWUsSUFBSTtnQ0FBRTswQ0FDakNuRjs7Ozs7OzRCQUdKRSx5QkFDQyw4REFBQ3RCLG1LQUFLQTtnQ0FBQ3NHLFVBQVM7Z0NBQVVkLElBQUk7b0NBQUVlLElBQUk7Z0NBQUU7MENBQ25DakY7Ozs7OzswQ0FHTCw4REFBQ2tGO2dDQUFLQyxVQUFVeEM7O2tEQUNkLDhEQUFDckUsdUtBQVNBO3dDQUNSOEcsUUFBTzt3Q0FDUEMsUUFBUTt3Q0FDUlYsU0FBUzt3Q0FDVHBFLE1BQUs7d0NBQ0wrRSxPQUFNO3dDQUNOQyxhQUFZO3dDQUNaQyxTQUFRO3dDQUNSaEQsT0FBT3BDLFNBQVNHLElBQUk7d0NBQ3BCa0YsVUFBVW5EO3dDQUNWeEMsT0FBTyxDQUFDLENBQUNJLFdBQVdLLElBQUk7d0NBQ3hCbUYsWUFBWXhGLFdBQVdLLElBQUk7d0NBQzNCb0YsWUFBWTs0Q0FDVnpCLElBQUk7Z0RBQUUwQixRQUFROzRDQUFHO3dDQUNuQjt3Q0FDQTFCLElBQUk7NENBQ0ZlLElBQUk7NENBQ0pkLFlBQVk7NENBQ1pXLGNBQWM7d0NBQ2hCOzs7Ozs7a0RBRUYsOERBQUN4Ryx1S0FBU0E7d0NBQ1I4RyxRQUFPO3dDQUNQN0UsTUFBSzt3Q0FDTG9FLFNBQVM7d0NBQ1RrQixNQUFLO3dDQUNMUCxPQUFNO3dDQUNOQyxhQUFZO3dDQUNaQyxTQUFRO3dDQUNSaEQsT0FBT3BDLFNBQVNJLG1CQUFtQjt3Q0FDbkNpRixVQUFVbkQ7d0NBQ1ZxRCxZQUFZOzRDQUNWekIsSUFBSTtnREFBRTBCLFFBQVE7NENBQUc7d0NBQ25CO3dDQUNBMUIsSUFBSTs0Q0FDRmUsSUFBSTs0Q0FDSmQsWUFBWTs0Q0FDWlcsY0FBYzt3Q0FDaEI7Ozs7OztrREFFRiw4REFBQ3ZHLGtLQUFJQTt3Q0FBQ3VILFNBQVM7d0NBQUNDLFNBQVM7OzBEQUN2Qiw4REFBQ3hILGtLQUFJQTtnREFBQ3lILElBQUk7Z0RBQUNDLElBQUk7Z0RBQUlDLElBQUk7MERBQ3JCLDRFQUFDNUgsdUtBQVNBO29EQUNSOEcsUUFBTztvREFDUFQsU0FBUztvREFDVFUsUUFBUTtvREFDUjlFLE1BQUs7b0RBQ0wrRSxPQUFNO29EQUNOQyxhQUFZO29EQUNaL0MsT0FBT3BDLFNBQVNLLElBQUk7b0RBQ3BCZ0YsVUFBVW5EO29EQUNWeEMsT0FBTyxDQUFDLENBQUNJLFdBQVdPLElBQUk7b0RBQ3hCaUYsWUFBWXhGLFdBQVdPLElBQUk7b0RBQzNCMEYsaUJBQWlCO3dEQUFFQyxRQUFRO29EQUFLO29EQUNoQ1osU0FBUTtvREFDUkcsWUFBWTt3REFDVnpCLElBQUk7NERBQUUwQixRQUFRO3dEQUFHO29EQUNuQjtvREFDQTFCLElBQUk7d0RBQ0ZDLFlBQVk7d0RBQ1pXLGNBQWM7b0RBQ2hCOzs7Ozs7Ozs7OzswREFHSiw4REFBQ3ZHLGtLQUFJQTtnREFBQ3lILElBQUk7Z0RBQUNDLElBQUk7Z0RBQUlDLElBQUk7MERBQ3JCLDRFQUFDNUgsdUtBQVNBO29EQUNSOEcsUUFBTztvREFDUFQsU0FBUztvREFDVFUsUUFBUTtvREFDUjlFLE1BQUs7b0RBQ0wrRSxPQUFNO29EQUNOTyxNQUFLO29EQUNMckQsT0FBT3BDLFNBQVNNLFdBQVc7b0RBQzNCK0UsVUFBVW5EO29EQUNWeEMsT0FBTyxDQUFDLENBQUNJLFdBQVdRLFdBQVc7b0RBQy9CZ0YsWUFBWXhGLFdBQVdRLFdBQVc7b0RBQ2xDeUYsaUJBQWlCO3dEQUFFQyxRQUFRO29EQUFLO29EQUNoQ1osU0FBUTtvREFDUkcsWUFBWTt3REFDVnpCLElBQUk7NERBQUUwQixRQUFRO3dEQUFHO3dEQUNqQlMsWUFBWTs0REFDVkMsS0FBSyxJQUFJbkUsT0FBT0MsV0FBVyxHQUFHQyxLQUFLLENBQUMsR0FBRzt3REFDekM7b0RBQ0Y7b0RBQ0E2QixJQUFJO3dEQUNGQyxZQUFZO3dEQUNaVyxjQUFjO29EQUNoQjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSU4sOERBQUN4Ryx1S0FBU0E7d0NBQ1I4RyxRQUFPO3dDQUNQVCxTQUFTO3dDQUNUcEUsTUFBSzt3Q0FDTCtFLE9BQU07d0NBQ05DLGFBQVk7d0NBQ1pDLFNBQVE7d0NBQ1JoRCxPQUFPcEMsU0FBU08sV0FBVzt3Q0FDM0I4RSxVQUFVbkQ7d0NBQ1ZpRSxTQUFTO3dDQUNUQyxNQUFNO3dDQUNOdEMsSUFBSTs0Q0FDRlksY0FBYzs0Q0FDZFgsWUFBWTs0Q0FDWmMsSUFBSTt3Q0FDTjs7Ozs7O2tEQUVGLDhEQUFDOUcsMktBQWFBO3dDQUNaK0YsSUFBSTs0Q0FDRkMsWUFBWTs0Q0FDWkksU0FBUzlFLGdCQUFnQixRQUFRO3dDQUNuQztrREFFQSw0RUFBQ2xCLGtLQUFJQTs0Q0FDSHVILFNBQVM7NENBQ1RDLFNBQVM7NENBQ1RVLGdCQUFlOzRDQUNmdkMsSUFBSTtnREFDRkMsWUFBWTtnREFDWkksU0FBUzlFLGdCQUFnQixRQUFROzRDQUNuQzs7OERBRUEsOERBQUNsQixrS0FBSUE7b0RBQUN5SCxJQUFJO29EQUFDQyxJQUFJO29EQUFJQyxJQUFHOzhEQUNwQiw0RUFBQ2pJLG9LQUFNQTt3REFDTGlHLElBQUk7NERBQUV3QyxPQUFPakgsZ0JBQWdCLFNBQVM7NERBQVFrSCxJQUFJO3dEQUFFO3dEQUNwRG5CLFNBQVE7d0RBQ1I1RixPQUFNO3dEQUNOZ0gsU0FBU3ZIO2tFQUNWOzs7Ozs7Ozs7Ozs4REFJSCw4REFBQ2Qsa0tBQUlBO29EQUFDeUgsSUFBSTtvREFBQ0MsSUFBSTtvREFBSUMsSUFBRzs4REFDcEIsNEVBQUNqSSxvS0FBTUE7d0RBQ0xpRyxJQUFJOzREQUFFd0MsT0FBT2pILGdCQUFnQixTQUFTOzREQUFRa0gsSUFBSTt3REFBRTt3REFDcERuQixTQUFRO3dEQUNSNUYsT0FBTTt3REFDTmlHLE1BQUs7d0RBQ0xnQixVQUFVM0YsaUJBQWlCZTt3REFDM0I2RSxPQUFPOzREQUNMQyxpQkFDRXZILE1BQU00RSxPQUFPLENBQUM0QyxJQUFJLEtBQUssVUFBVSxZQUFZO3dEQUNqRDtrRUFFQzlGLGlCQUFpQmUsZ0JBQWdCLGtCQUFrQjdCLFNBQVNFLEVBQUUsR0FBRyxXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVduRztHQWhkd0JuQjs7UUFDUlIsMERBQVFBO1FBQ0FGLDJLQUFhQTtRQUNqQkcsOERBQVFBO1FBQ0paLHlFQUFnQkE7UUFlWmUsdURBQVVBO1FBaUJjaEIsdURBQVdBO1FBd0JYQSx1REFBV0E7OztLQTVEdkNvQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9IYXNzYW5hT2ZmZXJQb3BVcC5qc3g/Nzk3YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IHVzZU11dGF0aW9uIH0gZnJvbSBcIkBhcG9sbG8vY2xpZW50XCI7XHJcbmltcG9ydCB7IHVzZVNlbGVjdGVkQ29sb3IgfSBmcm9tIFwiQC9jb21wb25lbnRzL0hlbHBlckZ1bmN0aW9uc1wiO1xyXG5pbXBvcnQge1xyXG4gIEJ1dHRvbixcclxuICBEaWFsb2csXHJcbiAgRGlhbG9nQWN0aW9ucyxcclxuICBEaWFsb2dDb250ZW50LFxyXG4gIERpYWxvZ1RpdGxlLFxyXG4gIFRleHRGaWVsZCxcclxuICBHcmlkLFxyXG4gIEJveCxcclxuICB1c2VNZWRpYVF1ZXJ5LFxyXG4gIEFsZXJ0LFxyXG59IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XHJcbmltcG9ydCB7IHVzZVRoZW1lIH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWwvc3R5bGVzXCI7XHJcbmltcG9ydCB7IHVzZUNvbG9yIH0gZnJvbSBcIkAvY29tcG9uZW50cy9Db2xvckNvbnRleHRcIjtcclxuaW1wb3J0IHsgQ1JFQVRFX09GRkVSLCBVUERBVEVfT0ZGRVIgfSBmcm9tIFwiLi4vRGF0YS9PZmZlclwiO1xyXG5pbXBvcnQgeyB1c2VTZXNzaW9uIH0gZnJvbSBcIm5leHQtYXV0aC9yZWFjdFwiO1xyXG5pbXBvcnQgeyBjcmVhdGVBdXRoT3B0aW9ucywgY3JlYXRlR3JhcGhRTEVycm9ySGFuZGxlciwgZ2V0RXJyb3JNZXNzYWdlIH0gZnJvbSBcIkAvdXRpbHMvZ3JhcGhxbEF1dGhcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhhc3NhbmFPZmZlclBvcFVwKHsgb3BlbiwgaGFuZGxlQ2xvc2UsIG9mZmVyLCByZWZldGNoT2ZmZXJzIH0pIHtcclxuICBjb25zdCB0aGVtZSA9IHVzZVRoZW1lKCk7XHJcbiAgY29uc3QgaXNTbWFsbFNjcmVlbiA9IHVzZU1lZGlhUXVlcnkodGhlbWUuYnJlYWtwb2ludHMuZG93bihcInNtXCIpKTtcclxuICBjb25zdCB7IGNvbG9yIH0gPSB1c2VDb2xvcigpO1xyXG4gIGNvbnN0IHNlbGVjdGVkQ29sb3IgPSB1c2VTZWxlY3RlZENvbG9yKGNvbG9yKTtcclxuXHJcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZShudWxsKTtcclxuICBjb25zdCBbc3VjY2Vzcywgc2V0U3VjY2Vzc10gPSB1c2VTdGF0ZShudWxsKTtcclxuICBjb25zdCBbZm9ybUVycm9ycywgc2V0Rm9ybUVycm9yc10gPSB1c2VTdGF0ZSh7fSk7XHJcbiAgY29uc3QgW2Zvcm1EYXRhLCBzZXRGb3JtRGF0YV0gPSB1c2VTdGF0ZSh7XHJcbiAgICBpZDogXCJcIixcclxuICAgIG5hbWU6IFwiXCIsXHJcbiAgICBjb250YWN0X2luZm9ybWF0aW9uOiBcIlwiLFxyXG4gICAgY29kZTogXCJcIixcclxuICAgIGV4cGlyeV9kYXRlOiBcIlwiLFxyXG4gICAgZGVzY3JpcHRpb246IFwiXCIsXHJcbiAgICBzdGF0dXM6IHRydWUsXHJcbiAgfSk7XHJcblxyXG4gIGNvbnN0IHsgZGF0YTogc2Vzc2lvbiB9ID0gdXNlU2Vzc2lvbigpO1xyXG4gIGNvbnN0IHJlc2V0Rm9ybSA9ICgpID0+IHtcclxuICAgIHNldEZvcm1EYXRhKHtcclxuICAgICAgaWQ6IFwiXCIsXHJcbiAgICAgIG5hbWU6IFwiXCIsXHJcbiAgICAgIGNvbnRhY3RfaW5mb3JtYXRpb246IFwiXCIsXHJcbiAgICAgIGNvZGU6IFwiXCIsXHJcbiAgICAgIGV4cGlyeV9kYXRlOiBcIlwiLFxyXG4gICAgICBkZXNjcmlwdGlvbjogXCJcIixcclxuICAgICAgc3RhdHVzOiB0cnVlLFxyXG4gICAgfSk7XHJcbiAgICBzZXRGb3JtRXJyb3JzKHt9KTtcclxuICAgIHNldEVycm9yKG51bGwpO1xyXG4gICAgc2V0U3VjY2VzcyhudWxsKTtcclxuICB9O1xyXG5cclxuICAvLyBBcG9sbG8gbXV0YXRpb24gaG9va3NcclxuICBjb25zdCBbY3JlYXRlT2ZmZXIsIHsgbG9hZGluZzogY3JlYXRlTG9hZGluZyB9XSA9IHVzZU11dGF0aW9uKENSRUFURV9PRkZFUiwge1xyXG4gICAgb25Db21wbGV0ZWQ6ICgpID0+IHtcclxuICAgICAgc2V0U3VjY2VzcyhcIk9mZmVyIGNyZWF0ZWQgc3VjY2Vzc2Z1bGx5IVwiKTtcclxuICAgICAgcmVmZXRjaE9mZmVycygpO1xyXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICByZXNldEZvcm0oKTtcclxuICAgICAgICBoYW5kbGVDbG9zZSgpO1xyXG4gICAgICB9LCAyMDAwKTtcclxuICAgIH0sXHJcbiAgICBvbkVycm9yOiAoZXJyKSA9PiB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJDcmVhdGUgb2ZmZXIgZXJyb3I6XCIsIEpTT04uc3RyaW5naWZ5KGVyciwgbnVsbCwgMikpO1xyXG4gICAgICBjb25zdCBtZXNzYWdlID1cclxuICAgICAgICBlcnIuZ3JhcGhRTEVycm9ycz8uWzBdPy5tZXNzYWdlIHx8XHJcbiAgICAgICAgZXJyLm5ldHdvcmtFcnJvcj8ucmVzdWx0Py5lcnJvcnM/LlswXT8ubWVzc2FnZSB8fFxyXG4gICAgICAgIGVyci5tZXNzYWdlIHx8XHJcbiAgICAgICAgXCJFcnJvciBjcmVhdGluZyBvZmZlclwiO1xyXG4gICAgICBpZiAobWVzc2FnZS5pbmNsdWRlcyhcImNvZGUgaXMgYWxyZWFkeSByZWdpc3RlcmVkXCIpKSB7XHJcbiAgICAgICAgc2V0Rm9ybUVycm9ycyh7IGNvZGU6IFwiVGhpcyBjb2RlIGlzIGFscmVhZHkgcmVnaXN0ZXJlZFwiIH0pO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldEVycm9yKG1lc3NhZ2UpO1xyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gIH0pO1xyXG5cclxuICBjb25zdCBbdXBkYXRlT2ZmZXIsIHsgbG9hZGluZzogdXBkYXRlTG9hZGluZyB9XSA9IHVzZU11dGF0aW9uKFVQREFURV9PRkZFUiwge1xyXG4gICAgb25Db21wbGV0ZWQ6ICgpID0+IHtcclxuICAgICAgc2V0U3VjY2VzcyhcIk9mZmVyIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5IVwiKTtcclxuICAgICAgcmVmZXRjaE9mZmVycygpO1xyXG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICByZXNldEZvcm0oKTtcclxuICAgICAgICBoYW5kbGVDbG9zZSgpO1xyXG4gICAgICB9LCAyMDAwKTtcclxuICAgIH0sXHJcbiAgICBvbkVycm9yOiAoZXJyKSA9PiB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJVcGRhdGUgb2ZmZXIgZXJyb3I6XCIsIEpTT04uc3RyaW5naWZ5KGVyciwgbnVsbCwgMikpO1xyXG4gICAgICBjb25zdCBtZXNzYWdlID1cclxuICAgICAgICBlcnIuZ3JhcGhRTEVycm9ycz8uWzBdPy5tZXNzYWdlIHx8XHJcbiAgICAgICAgZXJyLm5ldHdvcmtFcnJvcj8ucmVzdWx0Py5lcnJvcnM/LlswXT8ubWVzc2FnZSB8fFxyXG4gICAgICAgIGVyci5tZXNzYWdlIHx8XHJcbiAgICAgICAgXCJFcnJvciB1cGRhdGluZyBvZmZlclwiO1xyXG4gICAgICBpZiAobWVzc2FnZS5pbmNsdWRlcyhcImNvZGUgaXMgYWxyZWFkeSByZWdpc3RlcmVkXCIpKSB7XHJcbiAgICAgICAgc2V0Rm9ybUVycm9ycyh7IGNvZGU6IFwiVGhpcyBjb2RlIGlzIGFscmVhZHkgcmVnaXN0ZXJlZFwiIH0pO1xyXG4gICAgICB9IGVsc2Uge1xyXG4gICAgICAgIHNldEVycm9yKG1lc3NhZ2UpO1xyXG4gICAgICB9XHJcbiAgICB9LFxyXG4gIH0pO1xyXG5cclxuICAvLyBQcmUtZmlsbCBmb3JtIGZvciBlZGl0IG1vZGVcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coXCJSZWNlaXZlZCBvZmZlciBmb3IgZWRpdDpcIiwgSlNPTi5zdHJpbmdpZnkob2ZmZXIsIG51bGwsIDIpKTtcclxuICAgIGlmIChvZmZlciAmJiBvZmZlci5pZCkge1xyXG4gICAgICBzZXRGb3JtRGF0YSh7XHJcbiAgICAgICAgaWQ6IG9mZmVyLmlkIHx8IFwiXCIsXHJcbiAgICAgICAgbmFtZTogb2ZmZXIubmFtZSB8fCBcIlwiLFxyXG4gICAgICAgIGNvbnRhY3RfaW5mb3JtYXRpb246IG9mZmVyLmNvbnRhY3RfaW5mb3JtYXRpb24gfHwgXCJcIixcclxuICAgICAgICBjb2RlOiBvZmZlci5jb2RlIHx8IFwiXCIsXHJcbiAgICAgICAgZXhwaXJ5X2RhdGU6IG9mZmVyLmV4cGlyeV9kYXRlXHJcbiAgICAgICAgICA/IG5ldyBEYXRlKG9mZmVyLmV4cGlyeV9kYXRlKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE2KVxyXG4gICAgICAgICAgOiBcIlwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBvZmZlci5kZXNjcmlwdGlvbiB8fCBcIlwiLFxyXG4gICAgICAgIHN0YXR1czogb2ZmZXIuc3RhdHVzID8/IHRydWUsXHJcbiAgICAgIH0pO1xyXG4gICAgICBjb25zb2xlLmxvZyhcIkZvcm0gZGF0YSBzZXQgdG86XCIsIEpTT04uc3RyaW5naWZ5KHtcclxuICAgICAgICBpZDogb2ZmZXIuaWQgfHwgXCJcIixcclxuICAgICAgICBuYW1lOiBvZmZlci5uYW1lIHx8IFwiXCIsXHJcbiAgICAgICAgY29udGFjdF9pbmZvcm1hdGlvbjogb2ZmZXIuY29udGFjdF9pbmZvcm1hdGlvbiB8fCBcIlwiLFxyXG4gICAgICAgIGNvZGU6IG9mZmVyLmNvZGUgfHwgXCJcIixcclxuICAgICAgICBleHBpcnlfZGF0ZTogb2ZmZXIuZXhwaXJ5X2RhdGVcclxuICAgICAgICAgID8gbmV3IERhdGUob2ZmZXIuZXhwaXJ5X2RhdGUpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTYpXHJcbiAgICAgICAgICA6IFwiXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IG9mZmVyLmRlc2NyaXB0aW9uIHx8IFwiXCIsXHJcbiAgICAgICAgc3RhdHVzOiBvZmZlci5zdGF0dXMgPz8gdHJ1ZSxcclxuICAgICAgfSwgbnVsbCwgMikpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0Rm9ybURhdGEoe1xyXG4gICAgICAgIGlkOiBcIlwiLFxyXG4gICAgICAgIG5hbWU6IFwiXCIsXHJcbiAgICAgICAgY29udGFjdF9pbmZvcm1hdGlvbjogXCJcIixcclxuICAgICAgICBjb2RlOiBcIlwiLFxyXG4gICAgICAgIGV4cGlyeV9kYXRlOiBcIlwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIlwiLFxyXG4gICAgICAgIHN0YXR1czogdHJ1ZSxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfSwgW29mZmVyXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUlucHV0Q2hhbmdlID0gKGUpID0+IHtcclxuICAgIGNvbnN0IHsgbmFtZSwgdmFsdWUgfSA9IGUudGFyZ2V0O1xyXG4gICAgc2V0Rm9ybURhdGEoKHByZXYpID0+ICh7IC4uLnByZXYsIFtuYW1lXTogdmFsdWUgfSkpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IEZvcm1BY3Rpb24gPSBhc3luYyAoZXZlbnQpID0+IHtcclxuICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XHJcbiAgICBzZXRFcnJvcihudWxsKTtcclxuICAgIHNldFN1Y2Nlc3MobnVsbCk7XHJcbiAgICBzZXRGb3JtRXJyb3JzKHt9KTtcclxuXHJcbiAgICBpZiAoIXNlc3Npb24/LmFjY2Vzc1Rva2VuKSB7XHJcbiAgICAgIHNldEVycm9yKFwiQXV0aGVudGljYXRpb24gdG9rZW4gbWlzc2luZy4gUGxlYXNlIGxvZyBpbiBhZ2Fpbi5cIik7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoIXNlc3Npb24/LnVzZXI/LmlkKSB7XHJcbiAgICAgIHNldEVycm9yKFwiVXNlciBJRCBtaXNzaW5nLiBQbGVhc2UgbG9nIGluIGFnYWluLlwiKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IGV4cGlyeURhdGVSYXcgPSBmb3JtRGF0YS5leHBpcnlfZGF0ZTtcclxuICAgIGxldCBleHBpcnlEYXRlID0gbnVsbDtcclxuICAgIGlmIChleHBpcnlEYXRlUmF3KSB7XHJcbiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShleHBpcnlEYXRlUmF3KTtcclxuICAgICAgaWYgKCFpc05hTihkYXRlLmdldFRpbWUoKSkpIHtcclxuICAgICAgICBleHBpcnlEYXRlID0gZGF0ZS50b0lTT1N0cmluZygpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZGF0YSA9IHtcclxuICAgICAgaWQ6IGZvcm1EYXRhLmlkLFxyXG4gICAgICBuYW1lOiBmb3JtRGF0YS5uYW1lLnRyaW0oKSB8fCBcIlwiLFxyXG4gICAgICBjb250YWN0X2luZm9ybWF0aW9uOiBmb3JtRGF0YS5jb250YWN0X2luZm9ybWF0aW9uLnRyaW0oKSB8fCBudWxsLFxyXG4gICAgICBjb2RlOiBmb3JtRGF0YS5jb2RlLnRyaW0oKSB8fCBcIlwiLFxyXG4gICAgICBleHBpcnlfZGF0ZTogZXhwaXJ5RGF0ZSxcclxuICAgICAgZGVzY3JpcHRpb246IGZvcm1EYXRhLmRlc2NyaXB0aW9uLnRyaW0oKSB8fCBudWxsLFxyXG4gICAgICBzdGF0dXM6IGZvcm1EYXRhLnN0YXR1cyxcclxuICAgICAgdXBkYXRlZF9ieTogc2Vzc2lvbi51c2VyLmlkLFxyXG4gICAgfTtcclxuXHJcbiAgICAvLyBDbGllbnQtc2lkZSB2YWxpZGF0aW9uXHJcbiAgICBjb25zdCBlcnJvcnMgPSB7fTtcclxuICAgIGlmICghZGF0YS5uYW1lKSBlcnJvcnMubmFtZSA9IFwiTmFtZSBpcyByZXF1aXJlZFwiO1xyXG4gICAgaWYgKCFkYXRhLmNvZGUpIGVycm9ycy5jb2RlID0gXCJDb2RlIGlzIHJlcXVpcmVkXCI7XHJcbiAgICBpZiAoIWRhdGEuZXhwaXJ5X2RhdGUpIHtcclxuICAgICAgZXJyb3JzLmV4cGlyeV9kYXRlID0gXCJWYWxpZCBleHBpcnkgZGF0ZSBpcyByZXF1aXJlZFwiO1xyXG4gICAgfSBlbHNlIGlmIChuZXcgRGF0ZShkYXRhLmV4cGlyeV9kYXRlKSA8IG5ldyBEYXRlKCkpIHtcclxuICAgICAgZXJyb3JzLmV4cGlyeV9kYXRlID0gXCJFeHBpcnkgZGF0ZSBtdXN0IGJlIGluIHRoZSBmdXR1cmVcIjtcclxuICAgIH1cclxuICAgIGlmIChkYXRhLmNvZGUgJiYgIS9eSEFTU0FOQS1cXGQrJC8udGVzdChkYXRhLmNvZGUpKSB7XHJcbiAgICAgIGVycm9ycy5jb2RlID0gXCJDb2RlIG11c3QgZm9sbG93IGZvcm1hdCBIQVNTQU5BLTEyM1wiO1xyXG4gICAgfVxyXG4gICAgaWYgKGZvcm1EYXRhLmlkICYmICFkYXRhLmlkKSB7XHJcbiAgICAgIGVycm9ycy5pZCA9IFwiT2ZmZXIgSUQgaXMgcmVxdWlyZWQgZm9yIHVwZGF0aW5nXCI7XHJcbiAgICAgIHNldEVycm9yKFwiQ2Fubm90IHVwZGF0ZSBvZmZlcjogSW52YWxpZCBJRCBmb3JtYXQuXCIpO1xyXG4gICAgfVxyXG4gICAgaWYgKE9iamVjdC5rZXlzKGVycm9ycykubGVuZ3RoID4gMCkge1xyXG4gICAgICBzZXRGb3JtRXJyb3JzKGVycm9ycyk7XHJcbiAgICAgIHJldHVybjtcclxuICAgIH1cclxuXHJcbiAgICBjb25zb2xlLmxvZyhcIk11dGF0aW9uIGlucHV0OlwiLCBKU09OLnN0cmluZ2lmeShkYXRhLCBudWxsLCAyKSk7XHJcbiAgICBjb25zb2xlLmxvZyhcIlNlc3Npb246XCIsIEpTT04uc3RyaW5naWZ5KHNlc3Npb24sIG51bGwsIDIpKTtcclxuXHJcbiAgICBpZiAoZm9ybURhdGEuaWQpIHtcclxuICAgICAgLy8gVXBkYXRlIG1vZGVcclxuICAgICAgY29uc29sZS5sb2coXCJDYWxsaW5nIHVwZGF0ZU9mZmVyIHdpdGggaWQgYW5kIHVwZGF0ZU9mZmVyc0lucHV0OlwiLCBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgaWQ6IGRhdGEuaWQsXHJcbiAgICAgICAgdXBkYXRlT2ZmZXJzSW5wdXQ6IHtcclxuICAgICAgICAgIG5hbWU6IGRhdGEubmFtZSxcclxuICAgICAgICAgIGNvbnRhY3RfaW5mb3JtYXRpb246IGRhdGEuY29udGFjdF9pbmZvcm1hdGlvbixcclxuICAgICAgICAgIGNvZGU6IGRhdGEuY29kZSxcclxuICAgICAgICAgIGV4cGlyeV9kYXRlOiBkYXRhLmV4cGlyeV9kYXRlLFxyXG4gICAgICAgICAgZGVzY3JpcHRpb246IGRhdGEuZGVzY3JpcHRpb24sXHJcbiAgICAgICAgICBzdGF0dXM6IGRhdGEuc3RhdHVzLFxyXG4gICAgICAgICAgdXBkYXRlZF9ieTogZGF0YS51cGRhdGVkX2J5LFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH0sIG51bGwsIDIpKTtcclxuXHJcbiAgICAgIGF3YWl0IHVwZGF0ZU9mZmVyKHtcclxuICAgICAgICB2YXJpYWJsZXM6IHtcclxuICAgICAgICAgIGlkOiBkYXRhLmlkLFxyXG4gICAgICAgICAgdXBkYXRlT2ZmZXJzSW5wdXQ6IHtcclxuICAgICAgICAgICAgbmFtZTogZGF0YS5uYW1lLFxyXG4gICAgICAgICAgICBjb250YWN0X2luZm9ybWF0aW9uOiBkYXRhLmNvbnRhY3RfaW5mb3JtYXRpb24sXHJcbiAgICAgICAgICAgIGNvZGU6IGRhdGEuY29kZSxcclxuICAgICAgICAgICAgZXhwaXJ5X2RhdGU6IGRhdGEuZXhwaXJ5X2RhdGUsXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBkYXRhLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICBzdGF0dXM6IGRhdGEuc3RhdHVzLFxyXG4gICAgICAgICAgICB1cGRhdGVkX2J5OiBkYXRhLnVwZGF0ZWRfYnksXHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgY29udGV4dDoge1xyXG4gICAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7c2Vzc2lvbi5hY2Nlc3NUb2tlbn1gLFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICB9KTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIC8vIENyZWF0ZSBtb2RlXHJcbiAgICAgIGNvbnNvbGUubG9nKFwiQ2FsbGluZyBjcmVhdGVPZmZlciB3aXRoIGNyZWF0ZU9mZmVyc0lucHV0OlwiLCBKU09OLnN0cmluZ2lmeSh7XHJcbiAgICAgICAgbmFtZTogZGF0YS5uYW1lLFxyXG4gICAgICAgIGNvbnRhY3RfaW5mb3JtYXRpb246IGRhdGEuY29udGFjdF9pbmZvcm1hdGlvbixcclxuICAgICAgICBjb2RlOiBkYXRhLmNvZGUsXHJcbiAgICAgICAgZXhwaXJ5X2RhdGU6IGRhdGEuZXhwaXJ5X2RhdGUsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IGRhdGEuZGVzY3JpcHRpb24sXHJcbiAgICAgICAgc3RhdHVzOiB0cnVlLFxyXG4gICAgICAgIGNyZWF0ZWRfYnk6IHNlc3Npb24udXNlci5pZCxcclxuICAgICAgICB1cGRhdGVkX2J5OiBkYXRhLnVwZGF0ZWRfYnksXHJcbiAgICAgIH0sIG51bGwsIDIpKTtcclxuICAgICAgYXdhaXQgY3JlYXRlT2ZmZXIoe1xyXG4gICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgY3JlYXRlT2ZmZXJzSW5wdXQ6IHtcclxuICAgICAgICAgICAgbmFtZTogZGF0YS5uYW1lLFxyXG4gICAgICAgICAgICBjb250YWN0X2luZm9ybWF0aW9uOiBkYXRhLmNvbnRhY3RfaW5mb3JtYXRpb24sXHJcbiAgICAgICAgICAgIGNvZGU6IGRhdGEuY29kZSxcclxuICAgICAgICAgICAgZXhwaXJ5X2RhdGU6IGRhdGEuZXhwaXJ5X2RhdGUsXHJcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBkYXRhLmRlc2NyaXB0aW9uLFxyXG4gICAgICAgICAgICBzdGF0dXM6IHRydWUsXHJcbiAgICAgICAgICAgIGNyZWF0ZWRfYnk6IHNlc3Npb24udXNlci5pZCxcclxuICAgICAgICAgICAgdXBkYXRlZF9ieTogZGF0YS51cGRhdGVkX2J5LFxyXG4gICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIGNvbnRleHQ6IHtcclxuICAgICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Nlc3Npb24uYWNjZXNzVG9rZW59YCxcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxCb3hcclxuICAgICAgc3g9e3tcclxuICAgICAgICBiYWNrZ3JvdW5kOiAnI2ZmZicsIC8vIFNldCBvdXRlciBCb3ggYmFja2dyb3VuZCB0byB3aGl0ZVxyXG4gICAgICAgIGNvbG9yOiB0aGVtZS5wYWxldHRlLnRleHQucHJpbWFyeSwgLy8gRW5zdXJlIHRleHQgaXMgcmVhZGFibGVcclxuICAgICAgICBwYWRkaW5nOiBpc1NtYWxsU2NyZWVuID8gXCIycHhcIiA6IFwiNXB4XCIsXHJcbiAgICAgIH19XHJcbiAgICA+XHJcbiAgICAgIDxEaWFsb2dcclxuICAgICAgICBvcGVuPXtvcGVufVxyXG4gICAgICAgIG9uQ2xvc2U9e2hhbmRsZUNsb3NlfVxyXG4gICAgICAgIGZ1bGxTY3JlZW49e2lzU21hbGxTY3JlZW59XHJcbiAgICAgICAgbWF4V2lkdGg9XCJtZFwiXHJcbiAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgID5cclxuICAgICAgICA8RGlhbG9nVGl0bGVcclxuICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgIHRleHRBbGlnbjogXCJsZWZ0XCIsXHJcbiAgICAgICAgICAgIGZvbnRTaXplOiBpc1NtYWxsU2NyZWVuID8gXCIxLjJyZW1cIiA6IFwiMS41cmVtXCIsXHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjZmZmJywgLy8gU2V0IERpYWxvZ1RpdGxlIGJhY2tncm91bmQgdG8gd2hpdGVcclxuICAgICAgICAgICAgY29sb3I6IHRoZW1lLnBhbGV0dGUudGV4dC5wcmltYXJ5LCAvLyBFbnN1cmUgdGV4dCBpcyByZWFkYWJsZVxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICB7Zm9ybURhdGEuaWQgPyBcIkVkaXQgT2ZmZXJcIiA6IFwiQ3JlYXRlIE9mZmVyXCJ9XHJcbiAgICAgICAgPC9EaWFsb2dUaXRsZT5cclxuICAgICAgICA8RGlhbG9nQ29udGVudFxyXG4gICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogJyNmZmYnLCAvLyBTZXQgRGlhbG9nQ29udGVudCBiYWNrZ3JvdW5kIHRvIHdoaXRlXHJcbiAgICAgICAgICAgIGNvbG9yOiB0aGVtZS5wYWxldHRlLnRleHQucHJpbWFyeSwgLy8gRW5zdXJlIHRleHQgaXMgcmVhZGFibGVcclxuICAgICAgICAgICAgcGFkZGluZzogaXNTbWFsbFNjcmVlbiA/IFwiMnB4XCIgOiBcIjVweFwiLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICA+XHJcbiAgICAgICAgICA8Qm94XHJcbiAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgY29sb3I6IHRoZW1lLnBhbGV0dGUudGV4dC5wcmltYXJ5LCAvLyBFbnN1cmUgdGV4dCBpcyByZWFkYWJsZVxyXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogXCIxMHB4XCIsXHJcbiAgICAgICAgICAgICAgYm94U2hhZG93OiBcIjBweCA0cHggMjBweCAwcHggcmdiYSgwLCAwLCAwLCAwLjA1KVwiLFxyXG4gICAgICAgICAgICAgIHBhZGRpbmc6IGlzU21hbGxTY3JlZW4gPyBcIjVweFwiIDogXCIxMHB4XCIsXHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyNmZmYnLCAvLyBTZXQgaW5uZXIgQm94IGJhY2tncm91bmQgdG8gd2hpdGVcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgID5cclxuICAgICAgICAgICAge2Vycm9yICYmIChcclxuICAgICAgICAgICAgICA8QWxlcnQgc2V2ZXJpdHk9XCJlcnJvclwiIHN4PXt7IG1iOiAyIH19PlxyXG4gICAgICAgICAgICAgICAge2Vycm9yfVxyXG4gICAgICAgICAgICAgIDwvQWxlcnQ+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIHtzdWNjZXNzICYmIChcclxuICAgICAgICAgICAgICA8QWxlcnQgc2V2ZXJpdHk9XCJzdWNjZXNzXCIgc3g9e3sgbWI6IDIgfX0+XHJcbiAgICAgICAgICAgICAgICB7c3VjY2Vzc31cclxuICAgICAgICAgICAgICA8L0FsZXJ0PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17Rm9ybUFjdGlvbn0+XHJcbiAgICAgICAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgbWFyZ2luPVwibm9ybWFsXCJcclxuICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICBmdWxsV2lkdGhcclxuICAgICAgICAgICAgICAgIG5hbWU9XCJuYW1lXCJcclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiTmFtZVwiXHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIE9mZmVyIE5hbWVcIlxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5uYW1lfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgZXJyb3I9eyEhZm9ybUVycm9ycy5uYW1lfVxyXG4gICAgICAgICAgICAgICAgaGVscGVyVGV4dD17Zm9ybUVycm9ycy5uYW1lfVxyXG4gICAgICAgICAgICAgICAgSW5wdXRQcm9wcz17e1xyXG4gICAgICAgICAgICAgICAgICBzeDogeyBoZWlnaHQ6IDUyIH0sXHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgbWI6IDEsXHJcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjZmZmJywgLy8gU2V0IFRleHRGaWVsZCBiYWNrZ3JvdW5kIHRvIHdoaXRlXHJcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogXCI4cHhcIixcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICBtYXJnaW49XCJub3JtYWxcIlxyXG4gICAgICAgICAgICAgICAgbmFtZT1cImNvbnRhY3RfaW5mb3JtYXRpb25cIlxyXG4gICAgICAgICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICAgICAgICB0eXBlPVwidGVsXCJcclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiQ29udGFjdCBJbmZvcm1hdGlvblwiXHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIHlvdXIgY29udGFjdCAoZS5nLiwgZW1haWwgb3IgcGhvbmUpXCJcclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29udGFjdF9pbmZvcm1hdGlvbn1cclxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cclxuICAgICAgICAgICAgICAgIElucHV0UHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgc3g6IHsgaGVpZ2h0OiA1MiB9LFxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgIG1iOiAxLFxyXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnI2ZmZicsIC8vIFNldCBUZXh0RmllbGQgYmFja2dyb3VuZCB0byB3aGl0ZVxyXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IFwiOHB4XCIsXHJcbiAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPEdyaWQgY29udGFpbmVyIHNwYWNpbmc9ezJ9PlxyXG4gICAgICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fT5cclxuICAgICAgICAgICAgICAgICAgPFRleHRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbj1cIm5vcm1hbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwiY29kZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJDb2RlXCJcclxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkhBU1NBTkEtMTIzNDVcIlxyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb2RlfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVJbnB1dENoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICBlcnJvcj17ISFmb3JtRXJyb3JzLmNvZGV9XHJcbiAgICAgICAgICAgICAgICAgICAgaGVscGVyVGV4dD17Zm9ybUVycm9ycy5jb2RlfVxyXG4gICAgICAgICAgICAgICAgICAgIElucHV0TGFiZWxQcm9wcz17eyBzaHJpbms6IHRydWUgfX1cclxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgIElucHV0UHJvcHM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgIHN4OiB7IGhlaWdodDogNTIgfSxcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgIHN4PXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnI2ZmZicsIC8vIFNldCBUZXh0RmllbGQgYmFja2dyb3VuZCB0byB3aGl0ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiBcIjhweFwiLFxyXG4gICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9PlxyXG4gICAgICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luPVwibm9ybWFsXCJcclxuICAgICAgICAgICAgICAgICAgICBmdWxsV2lkdGhcclxuICAgICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJleHBpcnlfZGF0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw9XCJFeHBpcnkgRGF0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGV0aW1lLWxvY2FsXCJcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZXhwaXJ5X2RhdGV9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUlucHV0Q2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgICAgIGVycm9yPXshIWZvcm1FcnJvcnMuZXhwaXJ5X2RhdGV9XHJcbiAgICAgICAgICAgICAgICAgICAgaGVscGVyVGV4dD17Zm9ybUVycm9ycy5leHBpcnlfZGF0ZX1cclxuICAgICAgICAgICAgICAgICAgICBJbnB1dExhYmVsUHJvcHM9e3sgc2hyaW5rOiB0cnVlIH19XHJcbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICBJbnB1dFByb3BzPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzeDogeyBoZWlnaHQ6IDUyIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICBpbnB1dFByb3BzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1pbjogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE2KSxcclxuICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyNmZmYnLCAvLyBTZXQgVGV4dEZpZWxkIGJhY2tncm91bmQgdG8gd2hpdGVcclxuICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogXCI4cHhcIixcclxuICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9HcmlkPlxyXG4gICAgICAgICAgICAgIDwvR3JpZD5cclxuICAgICAgICAgICAgICA8VGV4dEZpZWxkXHJcbiAgICAgICAgICAgICAgICBtYXJnaW49XCJub3JtYWxcIlxyXG4gICAgICAgICAgICAgICAgZnVsbFdpZHRoXHJcbiAgICAgICAgICAgICAgICBuYW1lPVwiZGVzY3JpcHRpb25cIlxyXG4gICAgICAgICAgICAgICAgbGFiZWw9XCJEZXNjcmlwdGlvblwiXHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIG9mZmVyIGRlc2NyaXB0aW9uXCJcclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuZGVzY3JpcHRpb259XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICBtdWx0aWxpbmVcclxuICAgICAgICAgICAgICAgIHJvd3M9ezJ9XHJcbiAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IFwiOHB4XCIsXHJcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjZmZmJywgLy8gU2V0IFRleHRGaWVsZCBiYWNrZ3JvdW5kIHRvIHdoaXRlXHJcbiAgICAgICAgICAgICAgICAgIG1iOiAxLFxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxEaWFsb2dBY3Rpb25zXHJcbiAgICAgICAgICAgICAgICBzeD17e1xyXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnI2ZmZicsIC8vIFNldCBEaWFsb2dBY3Rpb25zIGJhY2tncm91bmQgdG8gd2hpdGVcclxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogaXNTbWFsbFNjcmVlbiA/IFwiNXB4XCIgOiBcIjEwcHhcIixcclxuICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPEdyaWRcclxuICAgICAgICAgICAgICAgICAgY29udGFpbmVyXHJcbiAgICAgICAgICAgICAgICAgIHNwYWNpbmc9ezJ9XHJcbiAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50PVwiY2VudGVyXCJcclxuICAgICAgICAgICAgICAgICAgc3g9e3tcclxuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnI2ZmZicsIC8vIFNldCBHcmlkIGJhY2tncm91bmQgdG8gd2hpdGVcclxuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiBpc1NtYWxsU2NyZWVuID8gXCI1cHhcIiA6IFwiMTBweFwiLFxyXG4gICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209XCJhdXRvXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgc3g9e3sgd2lkdGg6IGlzU21hbGxTY3JlZW4gPyBcIjEwMCVcIiA6IFwiYXV0b1wiLCBweDogNCB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPVwic2Vjb25kYXJ5XCJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsb3NlfVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIENhbmNlbFxyXG4gICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICA8L0dyaWQ+XHJcbiAgICAgICAgICAgICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT1cImF1dG9cIj5cclxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICBzeD17eyB3aWR0aDogaXNTbWFsbFNjcmVlbiA/IFwiMTAwJVwiIDogXCJhdXRvXCIsIHB4OiA0IH19XHJcbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiY29udGFpbmVkXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yPVwic2Vjb25kYXJ5XCJcclxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2NyZWF0ZUxvYWRpbmcgfHwgdXBkYXRlTG9hZGluZ31cclxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB0aGVtZS5wYWxldHRlLm1vZGUgPT09IFwiYmxhY2tcIiA/IFwiIzdCMUZBMlwiIDogXCIjOUMyN0IwXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHtjcmVhdGVMb2FkaW5nIHx8IHVwZGF0ZUxvYWRpbmcgPyBcIlN1Ym1pdHRpbmcuLi5cIiA6IGZvcm1EYXRhLmlkID8gXCJVcGRhdGVcIiA6IFwiQ3JlYXRlXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuICAgICAgICAgICAgICAgIDwvR3JpZD5cclxuICAgICAgICAgICAgICA8L0RpYWxvZ0FjdGlvbnM+XHJcbiAgICAgICAgICAgIDwvZm9ybT5cclxuICAgICAgICAgIDwvQm94PlxyXG4gICAgICAgIDwvRGlhbG9nQ29udGVudD5cclxuICAgICAgPC9EaWFsb2c+XHJcbiAgICA8L0JveD5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlTXV0YXRpb24iLCJ1c2VTZWxlY3RlZENvbG9yIiwiQnV0dG9uIiwiRGlhbG9nIiwiRGlhbG9nQWN0aW9ucyIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dUaXRsZSIsIlRleHRGaWVsZCIsIkdyaWQiLCJCb3giLCJ1c2VNZWRpYVF1ZXJ5IiwiQWxlcnQiLCJ1c2VUaGVtZSIsInVzZUNvbG9yIiwiQ1JFQVRFX09GRkVSIiwiVVBEQVRFX09GRkVSIiwidXNlU2Vzc2lvbiIsImNyZWF0ZUF1dGhPcHRpb25zIiwiY3JlYXRlR3JhcGhRTEVycm9ySGFuZGxlciIsImdldEVycm9yTWVzc2FnZSIsIkhhc3NhbmFPZmZlclBvcFVwIiwib3BlbiIsImhhbmRsZUNsb3NlIiwib2ZmZXIiLCJyZWZldGNoT2ZmZXJzIiwidGhlbWUiLCJpc1NtYWxsU2NyZWVuIiwiYnJlYWtwb2ludHMiLCJkb3duIiwiY29sb3IiLCJzZWxlY3RlZENvbG9yIiwiZXJyb3IiLCJzZXRFcnJvciIsInN1Y2Nlc3MiLCJzZXRTdWNjZXNzIiwiZm9ybUVycm9ycyIsInNldEZvcm1FcnJvcnMiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwiaWQiLCJuYW1lIiwiY29udGFjdF9pbmZvcm1hdGlvbiIsImNvZGUiLCJleHBpcnlfZGF0ZSIsImRlc2NyaXB0aW9uIiwic3RhdHVzIiwiZGF0YSIsInNlc3Npb24iLCJyZXNldEZvcm0iLCJjcmVhdGVPZmZlciIsImxvYWRpbmciLCJjcmVhdGVMb2FkaW5nIiwib25Db21wbGV0ZWQiLCJzZXRUaW1lb3V0Iiwib25FcnJvciIsImVyciIsImNvbnNvbGUiLCJKU09OIiwic3RyaW5naWZ5IiwibWVzc2FnZSIsImdyYXBoUUxFcnJvcnMiLCJuZXR3b3JrRXJyb3IiLCJyZXN1bHQiLCJlcnJvcnMiLCJpbmNsdWRlcyIsInVwZGF0ZU9mZmVyIiwidXBkYXRlTG9hZGluZyIsImxvZyIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsInNsaWNlIiwiaGFuZGxlSW5wdXRDaGFuZ2UiLCJlIiwidmFsdWUiLCJ0YXJnZXQiLCJwcmV2IiwiRm9ybUFjdGlvbiIsImV2ZW50IiwicHJldmVudERlZmF1bHQiLCJhY2Nlc3NUb2tlbiIsInVzZXIiLCJleHBpcnlEYXRlUmF3IiwiZXhwaXJ5RGF0ZSIsImRhdGUiLCJpc05hTiIsImdldFRpbWUiLCJ0cmltIiwidXBkYXRlZF9ieSIsInRlc3QiLCJPYmplY3QiLCJrZXlzIiwibGVuZ3RoIiwidXBkYXRlT2ZmZXJzSW5wdXQiLCJ2YXJpYWJsZXMiLCJjb250ZXh0IiwiaGVhZGVycyIsIkF1dGhvcml6YXRpb24iLCJjcmVhdGVkX2J5IiwiY3JlYXRlT2ZmZXJzSW5wdXQiLCJzeCIsImJhY2tncm91bmQiLCJwYWxldHRlIiwidGV4dCIsInByaW1hcnkiLCJwYWRkaW5nIiwib25DbG9zZSIsImZ1bGxTY3JlZW4iLCJtYXhXaWR0aCIsImZ1bGxXaWR0aCIsInRleHRBbGlnbiIsImZvbnRTaXplIiwiYm9yZGVyUmFkaXVzIiwiYm94U2hhZG93Iiwic2V2ZXJpdHkiLCJtYiIsImZvcm0iLCJvblN1Ym1pdCIsIm1hcmdpbiIsInJlcXVpcmVkIiwibGFiZWwiLCJwbGFjZWhvbGRlciIsInZhcmlhbnQiLCJvbkNoYW5nZSIsImhlbHBlclRleHQiLCJJbnB1dFByb3BzIiwiaGVpZ2h0IiwidHlwZSIsImNvbnRhaW5lciIsInNwYWNpbmciLCJpdGVtIiwieHMiLCJzbSIsIklucHV0TGFiZWxQcm9wcyIsInNocmluayIsImlucHV0UHJvcHMiLCJtaW4iLCJtdWx0aWxpbmUiLCJyb3dzIiwianVzdGlmeUNvbnRlbnQiLCJ3aWR0aCIsInB4Iiwib25DbGljayIsImRpc2FibGVkIiwic3R5bGUiLCJiYWNrZ3JvdW5kQ29sb3IiLCJtb2RlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/HassanaOfferPopUp.jsx\n"));

/***/ }),

/***/ "./src/utils/graphqlAuth.js":
/*!**********************************!*\
  !*** ./src/utils/graphqlAuth.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAuthOptions: function() { return /* binding */ createAuthOptions; },\n/* harmony export */   createGraphQLErrorHandler: function() { return /* binding */ createGraphQLErrorHandler; },\n/* harmony export */   getErrorMessage: function() { return /* binding */ getErrorMessage; },\n/* harmony export */   isAuthError: function() { return /* binding */ isAuthError; },\n/* harmony export */   isForbiddenError: function() { return /* binding */ isForbiddenError; }\n/* harmony export */ });\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Creates GraphQL query/mutation options with authentication headers\n * @param {Object} session - NextAuth session object\n * @param {Object} additionalOptions - Additional Apollo options\n * @returns {Object} Apollo query/mutation options with auth headers\n */ const createAuthOptions = function(session) {\n    let additionalOptions = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    var _additionalOptions_context;\n    const token = (session === null || session === void 0 ? void 0 : session.accessToken) || ( true ? localStorage.getItem(\"jwtToken\") : 0);\n    return {\n        ...additionalOptions,\n        context: {\n            ...additionalOptions.context,\n            headers: {\n                ...(_additionalOptions_context = additionalOptions.context) === null || _additionalOptions_context === void 0 ? void 0 : _additionalOptions_context.headers,\n                ...token && {\n                    authorization: \"Bearer \".concat(token)\n                }\n            }\n        },\n        errorPolicy: \"all\"\n    };\n};\n/**\n * Creates error handler for GraphQL operations\n * @param {Function} onAuthError - Callback for authentication errors\n * @param {Function} onForbiddenError - Callback for authorization errors\n * @param {Function} onOtherError - Callback for other errors\n * @returns {Function} Error handler function\n */ const createGraphQLErrorHandler = (onAuthError, onForbiddenError, onOtherError)=>{\n    return (error)=>{\n        console.error(\"GraphQL Error:\", error);\n        // Handle GraphQL errors\n        if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n            error.graphQLErrors.forEach((gqlError)=>{\n                const { message, extensions } = gqlError;\n                // Authentication errors\n                if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\") || message.includes(\"Token has expired\") || message.includes(\"Invalid token\")) {\n                    if (onAuthError) {\n                        onAuthError(gqlError);\n                    } else {\n                        console.warn(\"Authentication error:\", message);\n                        // Clear invalid token\n                        if (true) {\n                            localStorage.removeItem(\"jwtToken\");\n                            window.location.href = \"/login?login=false&reason=auth_error\";\n                        }\n                    }\n                } else if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\") || message.includes(\"Required role\")) {\n                    if (onForbiddenError) {\n                        onForbiddenError(gqlError);\n                    } else {\n                        console.warn(\"Authorization error:\", message);\n                    }\n                } else {\n                    if (onOtherError) {\n                        onOtherError(gqlError);\n                    } else {\n                        console.error(\"GraphQL error:\", message);\n                    }\n                }\n            });\n        }\n        // Handle network errors\n        if (error.networkError) {\n            const { statusCode } = error.networkError;\n            if (statusCode === 401) {\n                if (onAuthError) {\n                    onAuthError(error.networkError);\n                } else {\n                    console.warn(\"Network authentication error\");\n                    if (true) {\n                        localStorage.removeItem(\"jwtToken\");\n                        window.location.href = \"/login?login=false&reason=network_auth\";\n                    }\n                }\n            } else if (statusCode === 403) {\n                if (onForbiddenError) {\n                    onForbiddenError(error.networkError);\n                } else {\n                    console.warn(\"Network authorization error\");\n                }\n            } else {\n                if (onOtherError) {\n                    onOtherError(error.networkError);\n                } else {\n                    console.error(\"Network error:\", error.networkError);\n                }\n            }\n        }\n    };\n};\n/**\n * Utility function to check if an error is an authentication error\n * @param {Object} error - Apollo error object\n * @returns {boolean} Whether the error is an authentication error\n */ const isAuthError = (error)=>{\n    if (!error) return false;\n    // Check GraphQL errors\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        return error.graphQLErrors.some((gqlError)=>{\n            const { message, extensions } = gqlError;\n            return (extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\") || message.includes(\"Token has expired\") || message.includes(\"Invalid token\");\n        });\n    }\n    // Check network errors\n    if (error.networkError && error.networkError.statusCode === 401) {\n        return true;\n    }\n    return false;\n};\n/**\n * Utility function to check if an error is an authorization error\n * @param {Object} error - Apollo error object\n * @returns {boolean} Whether the error is an authorization error\n */ const isForbiddenError = (error)=>{\n    if (!error) return false;\n    // Check GraphQL errors\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        return error.graphQLErrors.some((gqlError)=>{\n            const { message, extensions } = gqlError;\n            return (extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\") || message.includes(\"Required role\");\n        });\n    }\n    // Check network errors\n    if (error.networkError && error.networkError.statusCode === 403) {\n        return true;\n    }\n    return false;\n};\n/**\n * Extracts user-friendly error message from GraphQL error\n * @param {Object} error - Apollo error object\n * @returns {string} User-friendly error message\n */ const getErrorMessage = (error)=>{\n    if (!error) return \"An unknown error occurred\";\n    // Check for GraphQL errors first\n    if (error.graphQLErrors && error.graphQLErrors.length > 0) {\n        const gqlError = error.graphQLErrors[0];\n        // Return custom messages for common errors\n        if (isAuthError(error)) {\n            return \"Authentication required. Please log in again.\";\n        }\n        if (isForbiddenError(error)) {\n            return \"Access denied. You do not have permission to perform this action.\";\n        }\n        return gqlError.message || \"A GraphQL error occurred\";\n    }\n    // Check for network errors\n    if (error.networkError) {\n        if (error.networkError.statusCode === 401) {\n            return \"Authentication required. Please log in again.\";\n        }\n        if (error.networkError.statusCode === 403) {\n            return \"Access denied. You do not have permission to perform this action.\";\n        }\n        return error.networkError.message || \"A network error occurred\";\n    }\n    return error.message || \"An error occurred\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/graphqlAuth.js\n"));

/***/ })

});