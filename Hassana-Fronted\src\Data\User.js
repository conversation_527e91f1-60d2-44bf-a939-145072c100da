import { gql } from "@apollo/client";
import axios from "axios";
import { baseUrl } from "./ApolloClient";

let base_url = baseUrl;
let users = "v1/app-users";

export const getAllUsers = async (token, page = 1, pageSize = 100) => {
  try {
    console.log("=== getAllUsers Debug ===");
    console.log("URL:", `${base_url}/${users}`);
    console.log("Token:", token ? token.substring(0, 20) + "..." : "No token");
    console.log("Params:", { page, pageSize });

    let response = await axios.get(`${base_url}/${users}`, {
      params: { page, pageSize },
      headers: { Authorization: `Bearer ${token || ""}` },
    });

    console.log("Response status:", response.status);
    console.log("Response headers:", response.headers);
    console.log("Users fetched:", JSON.stringify(response.data, null, 2));

    return response.data;
  } catch (error) {
    console.error("Error fetching users:", error);
    console.error("Error details:", {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });
    return { error: error.message, status: error.response?.status };
  }
};

export const updateUser = async (formData, isImageChanged, token) => {
  try {
    let data = new FormData();

    // Handle profile field - either file upload or avatar string
    if (formData.profile !== undefined) {
      if (isImageChanged && formData.profile instanceof File) {
        // Custom uploaded image file
        data.append("profile", formData.profile);
      } else if (typeof formData.profile =='string') {
        // Avatar selection or existing profile path
        data.append("profile", formData.profile);
      }
    }

    // Only append fields that exist in formData
    if (formData.name !== undefined) data.append("name", formData.name);
    if (formData.nameArabic !== undefined) data.append("name_arabic", formData.nameArabic);
    if (formData.principal !== undefined) data.append("user_principal_name", formData.principal);
    if (formData.email !== undefined) data.append("email", formData.email);
    if (formData.designation !== undefined) data.append("designation", formData.designation);
    if (formData.designationArabic !== undefined) data.append("designation_arabic", formData.designationArabic);
    if (formData.department !== undefined) data.append("department", formData.department);
    if (formData.departmentArabic !== undefined) data.append("department_arabic", formData.departmentArabic);
    if (formData.gender !== undefined) data.append("gender", formData.gender);
    if (formData.role !== undefined) data.append("role", formData.role);
    if (formData.status !== undefined) data.append("status", formData.status);
    if (formData.newJoining !== undefined) data.append("new_joiner", formData.newJoining === "true");
    if (formData.bioLink !== undefined) data.append("bio_link", formData.bioLink);
    if (formData.activity !== undefined) data.append("activity", formData.activity);
    if (formData.extension !== undefined) data.append("extension", formData.extension);
    console.log("FormData entries:", [...data.entries()]); // Log FormData

    let config = {
      method: "patch",
      maxBodyLength: Infinity,
      url: `${base_url}/${users}/${formData.id}`,
      headers: {
        Authorization: `Bearer ${token || ""}`,
      },
      data: data,
    };

    let res = await axios.request(config);
    console.log("Update user response:", res.data); // Log response
    return res.data;
  } catch (error) {
    console.error("Error updating user:", error);
    return { error: error.message, status: error.response?.status };
  }
};

export const getUsers = gql`
  query {
    users {
      id
      name
      dn
      role
      gender
      status
      user_principal_name
      createdAt
      updatedAt
      extension
      activity
    }
  }
`;

export const getUsersInstance = gql`
  query {
    users {
      id
      name
    }
  }
`;

export const getNewUsers = gql`
  query GetNewUsers($days: ID!) {
    getNewUsers(days: $days) {
      id
      profile
      name
      name_arabic
      department
      department_arabic
      designation
      designation_arabic
      bio_link
      email
      extension
      activity
      new_joiner
      is_cultural_ambassador
    }
  }
`;

export const getCulturalAmbassadors = gql`
  query GetCulturalAmbassadors {
    getCulturalAmbassadors {
      id
      profile
      name
      name_arabic
      department
      department_arabic
      designation
      designation_arabic
      bio_link
      email
      extension
      activity
      new_joiner
      is_cultural_ambassador
    }
  }
`;

export const mutationUpdateUser = gql`
  mutation UpdateUser($id: ID!, $role: String!, $gender: String!, $status: Boolean!) {
    updateUser(updateUserInput: { id: $id, role: $role, gender: $gender, status: $status }) {
      id
      name
      dn
      role
      status
      gender
      user_principal_name
      extension
      activity
      createdAt
      updatedAt
    }
  }
`;