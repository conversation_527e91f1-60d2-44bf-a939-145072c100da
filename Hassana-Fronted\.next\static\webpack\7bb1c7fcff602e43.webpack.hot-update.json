{"c": ["webpack"], "r": ["pages/admin/notifications", "pages/notifications"], "m": ["./node_modules/@mui/icons-material/esm/AddShoppingCart.js", "./node_modules/@mui/icons-material/esm/Cancel.js", "./node_modules/@mui/icons-material/esm/Close.js", "./node_modules/@mui/icons-material/esm/Delete.js", "./node_modules/@mui/icons-material/esm/RemoveRedEye.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=F%3A%5CProjects%5CHBack%5CHassana-Fronted%5Csrc%5Cpages%5Cadmin%5Cnotifications.jsx&page=%2Fadmin%2Fnotifications!", "./src/components/Modal.jsx", "./src/components/SnackBar.jsx", "./src/components/auth/UnauthorizedAccessPage.jsx", "./src/components/auth/withAdminAuth.js", "./src/pages/admin/component/DataTable.jsx", "./src/pages/admin/component/DialogBox.jsx", "./src/pages/admin/notifications.jsx", "__barrel_optimize__?names=AddShoppingCart,Delete,Group,RemoveRedEye!=!./node_modules/@mui/icons-material/esm/index.js", "__barrel_optimize__?names=<PERSON><PERSON>,Snackbar!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=Box,Button,CircularProgress,Grid,IconButton,Input,InputAdornment,MenuItem,Select,TextField,Typography,useTheme!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=Cancel,Close!=!./node_modules/@mui/icons-material/esm/index.js", "__barrel_optimize__?names=Dialog,Grid,TextField,useTheme!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=<PERSON><PERSON><PERSON><PERSON>on,<PERSON>ack,useTheme!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=IconButton,useTheme!=!./node_modules/@mui/material/index.js", "./node_modules/@mui/icons-material/Close.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=F%3A%5CProjects%5CHBack%5CHassana-Fronted%5Csrc%5Cpages%5Cnotifications.js&page=%2Fnotifications!", "./src/Data/Events.js", "./src/components/AnnouncementCard.js", "./src/components/NotificationButtons.jsx", "./src/components/NotificationCard.jsx", "./src/pages/notifications.js", "__barrel_optimize__?names=Box,Button!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=<PERSON>,Button,Dialog,DialogActions,DialogContent,DialogTitle,Grid,Typography!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=<PERSON>,<PERSON><PERSON>r,IconButton,Modal,Typography!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=Box,Divider,Typography!=!./node_modules/@mui/material/index.js", "__barrel_optimize__?names=useMediaQuery,useTheme!=!./node_modules/@mui/material/index.js"]}