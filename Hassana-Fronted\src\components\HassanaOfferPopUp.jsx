import { useState, useEffect } from "react";
import { useMutation } from "@apollo/client";
import { useSelectedColor } from "@/components/HelperFunctions";
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Grid,
  Box,
  useMediaQuery,
  Alert,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useColor } from "@/components/ColorContext";
import { CREATE_OFFER, UPDATE_OFFER } from "../Data/Offer";
import { useSession } from "next-auth/react";
import { createAuthOptions, createGraphQLErrorHandler, getErrorMessage } from "@/utils/graphqlAuth";

export default function HassanaOfferPopUp({ open, handleClose, offer, refetchOffers }) {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const { color } = useColor();
  const selectedColor = useSelectedColor(color);

  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [formErrors, setFormErrors] = useState({});
  const [formData, setFormData] = useState({
    id: "",
    name: "",
    contact_information: "",
    code: "",
    expiry_date: "",
    description: "",
    status: true,
  });

  const { data: session } = useSession();
  const resetForm = () => {
    setFormData({
      id: "",
      name: "",
      contact_information: "",
      code: "",
      expiry_date: "",
      description: "",
      status: true,
    });
    setFormErrors({});
    setError(null);
    setSuccess(null);
  };

  // Apollo mutation hooks with authentication
  const [createOffer, { loading: createLoading }] = useMutation(CREATE_OFFER,
    createAuthOptions(session, {
      onCompleted: () => {
        setSuccess("Offer created successfully!");
        refetchOffers();
        setTimeout(() => {
          resetForm();
          handleClose();
        }, 1000);
      },
      onError: createGraphQLErrorHandler(
        () => setError("Authentication failed. Please log in again."),
        () => setError("Access denied. You don't have permission to create offers."),
        (err) => {
          console.error("Create offer error:", err);
          if (err.message && err.message.includes("code is already registered")) {
            setFormErrors({ code: "This code is already registered" });
          } else {
            setError(getErrorMessage({ graphQLErrors: [err] }));
          }
        }
      ),
    })
  );

  const [updateOffer, { loading: updateLoading }] = useMutation(UPDATE_OFFER,
    createAuthOptions(session, {
      onCompleted: () => {
        setSuccess("Offer updated successfully!");
        refetchOffers();
        setTimeout(() => {
          resetForm();
          handleClose();
        }, 1000);
      },
      onError: createGraphQLErrorHandler(
        () => setError("Authentication failed. Please log in again."),
        () => setError("Access denied. You don't have permission to update offers."),
        (err) => {
          console.error("Update offer error:", err);
          if (err.message && err.message.includes("code is already registered")) {
            setFormErrors({ code: "This code is already registered" });
          } else {
            setError(getErrorMessage({ graphQLErrors: [err] }));
          }
        }
      ),
    })
  );

  // Pre-fill form for edit mode
  useEffect(() => {
    console.log("Received offer for edit:", JSON.stringify(offer, null, 2));
    if (offer && offer.id) {
      setFormData({
        id: offer.id || "",
        name: offer.name || "",
        contact_information: offer.contact_information || "",
        code: offer.code || "",
        expiry_date: offer.expiry_date
          ? new Date(offer.expiry_date).toISOString().slice(0, 16)
          : "",
        description: offer.description || "",
        status: offer.status ?? true,
      });
      console.log("Form data set to:", JSON.stringify({
        id: offer.id || "",
        name: offer.name || "",
        contact_information: offer.contact_information || "",
        code: offer.code || "",
        expiry_date: offer.expiry_date
          ? new Date(offer.expiry_date).toISOString().slice(0, 16)
          : "",
        description: offer.description || "",
        status: offer.status ?? true,
      }, null, 2));
    } else {
      setFormData({
        id: "",
        name: "",
        contact_information: "",
        code: "",
        expiry_date: "",
        description: "",
        status: true,
      });
    }
  }, [offer]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const FormAction = async (event) => {
    event.preventDefault();
    setError(null);
    setSuccess(null);
    setFormErrors({});

    if (!session?.accessToken) {
      setError("Authentication token missing. Please log in again.");
      return;
    }

    if (!session?.user?.id) {
      setError("User ID missing. Please log in again.");
      return;
    }

    const expiryDateRaw = formData.expiry_date;
    let expiryDate = null;
    if (expiryDateRaw) {
      const date = new Date(expiryDateRaw);
      if (!isNaN(date.getTime())) {
        expiryDate = date.toISOString();
      }
    }

    const data = {
      id: formData.id,
      name: formData.name.trim() || "",
      contact_information: formData.contact_information.trim() || null,
      code: formData.code.trim() || "",
      expiry_date: expiryDate,
      description: formData.description.trim() || null,
      status: formData.status,
      updated_by: session.user.id,
    };

    // Client-side validation
    const errors = {};
    if (!data.name) errors.name = "Name is required";
    if (!data.code) errors.code = "Code is required";
    if (!data.expiry_date) {
      errors.expiry_date = "Valid expiry date is required";
    } else if (new Date(data.expiry_date) < new Date()) {
      errors.expiry_date = "Expiry date must be in the future";
    }
    if (data.code && !/^HASSANA-\d+$/.test(data.code)) {
      errors.code = "Code must follow format HASSANA-123";
    }
    if (formData.id && !data.id) {
      errors.id = "Offer ID is required for updating";
      setError("Cannot update offer: Invalid ID format.");
    }
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    console.log("Mutation input:", JSON.stringify(data, null, 2));
    console.log("Session:", JSON.stringify(session, null, 2));

    if (formData.id) {
      // Update mode
      console.log("Calling updateOffer with id and updateOffersInput:", JSON.stringify({
        id: data.id,
        updateOffersInput: {
          name: data.name,
          contact_information: data.contact_information,
          code: data.code,
          expiry_date: data.expiry_date,
          description: data.description,
          status: data.status,
          updated_by: data.updated_by,
        },
      }, null, 2));

      await updateOffer({
        variables: {
          id: data.id,
          updateOffersInput: {
            name: data.name,
            contact_information: data.contact_information,
            code: data.code,
            expiry_date: data.expiry_date,
            description: data.description,
            status: data.status,
            updated_by: data.updated_by,
          },
        },
        context: {
          headers: {
            Authorization: `Bearer ${session.accessToken}`,
          },
        },
      });
    } else {
      // Create mode
      console.log("Calling createOffer with createOffersInput:", JSON.stringify({
        name: data.name,
        contact_information: data.contact_information,
        code: data.code,
        expiry_date: data.expiry_date,
        description: data.description,
        status: true,
        created_by: session.user.id,
        updated_by: data.updated_by,
      }, null, 2));
      await createOffer({
        variables: {
          createOffersInput: {
            name: data.name,
            contact_information: data.contact_information,
            code: data.code,
            expiry_date: data.expiry_date,
            description: data.description,
            status: true,
            created_by: session.user.id,
            updated_by: data.updated_by,
          },
        },
        context: {
          headers: {
            Authorization: `Bearer ${session.accessToken}`,
          },
        },
      });
    }
  };

  return (
    <Box
      sx={{
        background: '#fff', // Set outer Box background to white
        color: theme.palette.text.primary, // Ensure text is readable
        padding: isSmallScreen ? "2px" : "5px",
      }}
    >
      <Dialog
        open={open}
        onClose={handleClose}
        fullScreen={isSmallScreen}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle
          sx={{
            textAlign: "left",
            fontSize: isSmallScreen ? "1.2rem" : "1.5rem",
            background: '#fff', // Set DialogTitle background to white
            color: theme.palette.text.primary, // Ensure text is readable
          }}
        >
          {formData.id ? "Edit Offer" : "Create Offer"}
        </DialogTitle>
        <DialogContent
          sx={{
            background: '#fff', // Set DialogContent background to white
            color: theme.palette.text.primary, // Ensure text is readable
            padding: isSmallScreen ? "2px" : "5px",
          }}
        >
          <Box
            sx={{
              color: theme.palette.text.primary, // Ensure text is readable
              borderRadius: "10px",
              boxShadow: "0px 4px 20px 0px rgba(0, 0, 0, 0.05)",
              padding: isSmallScreen ? "5px" : "10px",
              background: '#fff', // Set inner Box background to white
            }}
          >
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}
            {success && (
              <Alert severity="success" sx={{ mb: 2 }}>
                {success}
              </Alert>
            )}
            <form onSubmit={FormAction}>
              <TextField
                margin="normal"
                required
                fullWidth
                name="name"
                label="Name"
                placeholder="Enter Offer Name"
                variant="outlined"
                value={formData.name}
                onChange={handleInputChange}
                error={!!formErrors.name}
                helperText={formErrors.name}
                InputProps={{
                  sx: { height: 52 },
                }}
                sx={{
                  mb: 1,
                  background: '#fff', // Set TextField background to white
                  borderRadius: "8px",
                }}
              />
              <TextField
                margin="normal"
                name="contact_information"
                fullWidth
                type="tel"
                label="Contact Information"
                placeholder="Enter your contact (e.g., email or phone)"
                variant="outlined"
                value={formData.contact_information}
                onChange={handleInputChange}
                InputProps={{
                  sx: { height: 52 },
                }}
                sx={{
                  mb: 1,
                  background: '#fff', // Set TextField background to white
                  borderRadius: "8px",
                }}
              />
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    margin="normal"
                    fullWidth
                    required
                    name="code"
                    label="Code"
                    placeholder="HASSANA-12345"
                    value={formData.code}
                    onChange={handleInputChange}
                    error={!!formErrors.code}
                    helperText={formErrors.code}
                    InputLabelProps={{ shrink: true }}
                    variant="outlined"
                    InputProps={{
                      sx: { height: 52 },
                    }}
                    sx={{
                      background: '#fff', // Set TextField background to white
                      borderRadius: "8px",
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    margin="normal"
                    fullWidth
                    required
                    name="expiry_date"
                    label="Expiry Date"
                    type="datetime-local"
                    value={formData.expiry_date}
                    onChange={handleInputChange}
                    error={!!formErrors.expiry_date}
                    helperText={formErrors.expiry_date}
                    InputLabelProps={{ shrink: true }}
                    variant="outlined"
                    InputProps={{
                      sx: { height: 52 },
                      inputProps: {
                        min: new Date().toISOString().slice(0, 16),
                      },
                    }}
                    sx={{
                      background: '#fff', // Set TextField background to white
                      borderRadius: "8px",
                    }}
                  />
                </Grid>
              </Grid>
              <TextField
                margin="normal"
                fullWidth
                name="description"
                label="Description"
                placeholder="Enter offer description"
                variant="outlined"
                value={formData.description}
                onChange={handleInputChange}
                multiline
                rows={2}
                sx={{
                  borderRadius: "8px",
                  background: '#fff', // Set TextField background to white
                  mb: 1,
                }}
              />
              <DialogActions
                sx={{
                  background: '#fff', // Set DialogActions background to white
                  padding: isSmallScreen ? "5px" : "10px",
                }}
              >
                <Grid
                  container
                  spacing={2}
                  justifyContent="center"
                  sx={{
                    background: '#fff', // Set Grid background to white
                    padding: isSmallScreen ? "5px" : "10px",
                  }}
                >
                  <Grid item xs={12} sm="auto">
                    <Button
                      sx={{ width: isSmallScreen ? "100%" : "auto", px: 4 }}
                      variant="outlined"
                      color="secondary"
                      onClick={handleClose}
                    >
                      Cancel
                    </Button>
                  </Grid>
                  <Grid item xs={12} sm="auto">
                    <Button
                      sx={{ width: isSmallScreen ? "100%" : "auto", px: 4 }}
                      variant="contained"
                      color="secondary"
                      type="submit"
                      disabled={createLoading || updateLoading}
                      style={{
                        backgroundColor:
                          theme.palette.mode === "black" ? "#7B1FA2" : "#9C27B0",
                      }}
                    >
                      {createLoading || updateLoading ? "Submitting..." : formData.id ? "Update" : "Create"}
                    </Button>
                  </Grid>
                </Grid>
              </DialogActions>
            </form>
          </Box>
        </DialogContent>
      </Dialog>
    </Box>
  );
}