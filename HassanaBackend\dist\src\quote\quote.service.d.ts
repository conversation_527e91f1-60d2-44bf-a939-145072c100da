import { CreateQuoteInput } from './dto/create-quote.input';
import { UpdateQuoteInput } from './dto/update-quote.input';
import { QuoteEntity } from './entities/quote.entity';
import { Repository } from 'typeorm';
import { UUID } from 'crypto';
export declare class QuoteService {
    private readonly quoteRepository;
    constructor(quoteRepository: Repository<QuoteEntity>);
    create(createQuoteInput: CreateQuoteInput): Promise<QuoteEntity>;
    findAll(): Promise<QuoteEntity[]>;
    findOne(id: number): Promise<string>;
    update(id: UUID, updateQuoteInput: UpdateQuoteInput): Promise<UpdateQuoteInput & QuoteEntity>;
    remove(id: UUID): Promise<QuoteEntity>;
    findByVisibility(): Promise<QuoteEntity> | null;
}
