import { Box, CircularProgress, Typography, useTheme } from "@mui/material";
import Image from "next/image";
import { useEffect, useState } from "react";
import { getCurrentTime } from "./HelperFunctions";
import { baseUrl } from "@/Data/ApolloClient";

const WeatherHeader = () => {
    const [weatherData, setWeatherData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const theme = useTheme();

    useEffect(() => {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const lat = position.coords.latitude;
                    const lon = position.coords.longitude;
                    fetchWeatherData(lat, lon);
                },
                () => {
                    const defaultLat = 24.6877;
                    const defaultLon = 46.7219;
                    fetchWeatherData(defaultLat, defaultLon);
                }
            );
        } else {
            setError("Geolocation is not supported by this browser.");
        }
    }, []);

    function fetchWeatherData(lat, lon) {
        const url = `${baseUrl}/v1/test/testing-route?lat=${lat}&lon=${lon}`;
        fetch(url)
            .then((response) => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then((data) => {
                setWeatherData(data.data);
                setLoading(false);
            })
            .catch((error) => {
                setError(error.message);
                setLoading(false);
            });
    }

    const formatDateTime = (unixTime) => {
        const date = new Date(unixTime * 1000);
        return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
    };

    if (loading) {
        return (
            <Box sx={{ padding: 2, textAlign: "center" }}>
                <CircularProgress />
                <Typography>Loading weather data...</Typography>
            </Box>
        );
    }

    if (error) {
        return (
            <Box sx={{ padding: 2 }}>
                <Typography color="error">Weather Error: {error}</Typography>
            </Box>
        );
    }

    if (!weatherData) {
        return (
            <Box sx={{ padding: 2 }}>
                <Typography color="warning">No weather data available.</Typography>
            </Box>
        );
    }

    if (!weatherData.weather || !weatherData.main) {
        return (
            <Box sx={{ padding: 2 }}>
                <Typography color="warning">Weather data is incomplete.</Typography>
            </Box>
        );
    }

    const weatherIconUrl = `/weatherIcons/${weatherData.weather[0].icon}.svg`;
    const currentTime = getCurrentTime(); // Using the imported function

    return (
        <Box
            sx={{
                borderRadius: "10px",
                boxShadow: "0px 4px 20px 0px rgba(0, 0, 0, 0.05)",
                display: "flex",
                gap: "10px",
                maxWidth: "300px",
                marginRight: "10px",
            }}
        >
            <Image
                src={weatherIconUrl}
                alt={weatherData.weather[0].description}
                width={50}
                height={20}
                style={{
                    filter: "drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3))",
                }}
            />
            <Box>
                <Typography
                    variant="h6"
                    sx={{
                        color: "#fff",
                        marginTop: "10px",
                        fontSize: "1.8rem",
                        "& sup": {
                            fontSize: "0.8rem",
                            verticalAlign: "super",
                            color: "#fff",
                            marginLeft: "2px",
                        },
                        "& sub": {
                            fontSize: "1rem",
                            color: "#fff",
                            marginBottom: "2px",
                        },
                    }}
                >
                    {Math.round(weatherData.main.temp - 273.15)}
                    <sup>o</sup>
                    <sub>C</sub>
                </Typography>
            </Box>
            <Typography
                variant="body1"
                sx={{ fontWeight: "600", color: "#E0E0E0", marginTop: "20px" }}
            >
                {weatherData.weather[0].main}
            </Typography>
        </Box>
    );
};

export default WeatherHeader;