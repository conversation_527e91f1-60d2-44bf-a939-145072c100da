{"version": 3, "file": "throttler.module.js", "sourceRoot": "", "sources": ["../src/throttler.module.ts"], "names": [], "mappings": ";;;;;;;;;;AAAA,2CAAyE;AAMzE,+DAA0D;AAC1D,+DAA2F;AAOpF,IAAM,eAAe,uBAArB,MAAM,eAAe;IAI1B,MAAM,CAAC,OAAO,CAAC,UAAkC,EAAE;QACjD,MAAM,SAAS,GAAG,CAAC,GAAG,IAAA,8CAAwB,EAAC,OAAO,CAAC,EAAE,8CAAwB,CAAC,CAAC;QACnF,OAAO;YACL,MAAM,EAAE,iBAAe;YACvB,SAAS;YACT,OAAO,EAAE,SAAS;SACnB,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,YAAY,CAAC,OAA8B;QAChD,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAE,8CAAwB,CAAC,CAAC;QACpF,OAAO;YACL,MAAM,EAAE,iBAAe;YACvB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;YAC9B,SAAS;YACT,OAAO,EAAE,SAAS;SACnB,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,OAA8B;QAChE,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YAC9C,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,CAAC,CAAC;QACpD,CAAC;QACD,OAAO;YACL,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC;YACxC;gBACE,OAAO,EAAE,OAAO,CAAC,QAAQ;gBACzB,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B;SACF,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,0BAA0B,CAAC,OAA8B;QACtE,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,OAAO;gBACL,OAAO,EAAE,uCAAiB;gBAC1B,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,EAAE;aAC7B,CAAC;QACJ,CAAC;QACD,OAAO;YACL,OAAO,EAAE,uCAAiB;YAC1B,UAAU,EAAE,KAAK,EAAE,cAAuC,EAAE,EAAE,CAC5D,MAAM,cAAc,CAAC,sBAAsB,EAAE;YAC/C,MAAM,EAAE,CAAC,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,QAAQ,CAAC;SAClD,CAAC;IACJ,CAAC;CACF,CAAA;AAtDY,0CAAe;0BAAf,eAAe;IAF3B,IAAA,eAAM,GAAE;IACR,IAAA,eAAM,EAAC,EAAE,CAAC;GACE,eAAe,CAsD3B"}