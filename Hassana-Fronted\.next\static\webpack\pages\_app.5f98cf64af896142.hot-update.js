"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/Data/ApolloClient.js":
/*!**********************************!*\
  !*** ./src/Data/ApolloClient.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   baseUrl: function() { return /* binding */ baseUrl; }\n/* harmony export */ });\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @apollo/client */ \"./node_modules/@apollo/client/index.js\");\n/* harmony import */ var _apollo_client_link_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client/link/context */ \"./node_modules/@apollo/client/link/context/index.js\");\n/* harmony import */ var _apollo_client_link_error__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @apollo/client/link/error */ \"./node_modules/@apollo/client/link/error/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/react */ \"./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_csrfUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/csrfUtils */ \"./src/utils/csrfUtils.js\");\n\n\n\n\n\n//export const baseUrl = \"http://*************:3001/v1\";\n//export const baseUrl = \"https://portal.hassana.com.sa/v1\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com/v1\";\nconst baseUrl = \"http://localhost:3001\";\n//export const baseUrl = \"https://hassana-api.360xpertsolutions.com\";\n//export const baseUrl = \"https://v2-portal.hassana.com.sa\";\n//export const base_url = \"https://v2-portal.hassana.com.sa/v1\";\n//export const base_url = \"https://localhost:3001/v1\";\nconst httpLink = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_2__.createHttpLink)({\n    uri: baseUrl + \"/graphql\"\n});\n// Auth link to add JWT token to headers\nconst authLink = (0,_apollo_client_link_context__WEBPACK_IMPORTED_MODULE_3__.setContext)(async (_, param)=>{\n    let { headers } = param;\n    // Get token from session or localStorage\n    let token = null;\n    try {\n        const session = await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_0__.getSession)();\n        token = session === null || session === void 0 ? void 0 : session.accessToken;\n    } catch (error) {\n        console.warn(\"Could not get session:\", error);\n    }\n    // Fallback to localStorage if session token not available\n    if (!token && \"object\" !== \"undefined\") {\n        token = localStorage.getItem(\"jwtToken\");\n    }\n    return {\n        headers: {\n            ...headers,\n            ...token && {\n                authorization: \"Bearer \".concat(token)\n            }\n        }\n    };\n});\n// Error link to handle GraphQL and network errors\nconst errorLink = (0,_apollo_client_link_error__WEBPACK_IMPORTED_MODULE_4__.onError)((param)=>{\n    let { graphQLErrors, networkError } = param;\n    if (graphQLErrors) {\n        graphQLErrors.forEach((param)=>{\n            let { message, locations, path, extensions } = param;\n            console.error(\"[GraphQL error]: Message: \".concat(message, \", Location: \").concat(locations, \", Path: \").concat(path));\n            // Handle authentication errors\n            if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"UNAUTHENTICATED\" || message.includes(\"Unauthorized\") || message.includes(\"Authorization header not found\")) {\n                console.warn(\"Authentication error detected, redirecting to login...\");\n                // Clear invalid token\n                if (true) {\n                    localStorage.removeItem(\"jwtToken\");\n                }\n                // Redirect to login page\n                if (true) {\n                    window.location.href = \"/login?login=false\";\n                }\n            }\n            // Handle authorization errors\n            if ((extensions === null || extensions === void 0 ? void 0 : extensions.code) === \"FORBIDDEN\" || message.includes(\"Access denied\") || message.includes(\"Forbidden\")) {\n                console.warn(\"Authorization error detected\");\n                // You can show a toast or redirect to unauthorized page\n                if (true) {\n                    // You can implement a toast notification here\n                    console.error(\"Access denied: Insufficient permissions\");\n                }\n            }\n        });\n    }\n    if (networkError) {\n        console.error(\"[Network error]: \".concat(networkError));\n        // Handle network errors that might indicate auth issues\n        if (networkError.statusCode === 401) {\n            console.warn(\"Network 401 error, clearing token and redirecting...\");\n            if (true) {\n                localStorage.removeItem(\"jwtToken\");\n                window.location.href = \"/login?login=false\";\n            }\n        }\n    }\n});\nconst client = new _apollo_client__WEBPACK_IMPORTED_MODULE_2__.ApolloClient({\n    link: (0,_apollo_client__WEBPACK_IMPORTED_MODULE_2__.from)([\n        errorLink,\n        authLink,\n        httpLink\n    ]),\n    cache: new _apollo_client__WEBPACK_IMPORTED_MODULE_2__.InMemoryCache(),\n    defaultOptions: {\n        watchQuery: {\n            errorPolicy: \"all\"\n        },\n        query: {\n            errorPolicy: \"all\"\n        }\n    }\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (client);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/Data/ApolloClient.js\n"));

/***/ }),

/***/ "./src/utils/csrfUtils.js":
/*!********************************!*\
  !*** ./src/utils/csrfUtils.js ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearCsrfTokens: function() { return /* binding */ clearCsrfTokens; },\n/* harmony export */   getCsrfToken: function() { return /* binding */ getCsrfToken; },\n/* harmony export */   getSecureHeaders: function() { return /* binding */ getSecureHeaders; },\n/* harmony export */   getSessionToken: function() { return /* binding */ getSessionToken; },\n/* harmony export */   initializeCsrfTokens: function() { return /* binding */ initializeCsrfTokens; },\n/* harmony export */   verifyCsrfToken: function() { return /* binding */ verifyCsrfToken; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var _Data_ApolloClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/Data/ApolloClient */ \"./src/Data/ApolloClient.js\");\n\n\n/**\n * CSRF Token Management Utility\n * Handles fetching, storing, and using CSRF tokens for secure requests\n */ class CsrfManager {\n    /**\n   * Fetch CSRF token from backend\n   * @returns {Promise<{csrfToken: string, sessionToken: string}>}\n   */ async fetchCsrfToken() {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(\"\".concat(_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_0__.baseUrl, \"/security/csrf-token\"), {\n                withCredentials: true\n            });\n            const { csrfToken, sessionToken } = response.data;\n            // Store tokens\n            this.csrfToken = csrfToken;\n            this.sessionToken = sessionToken;\n            this.tokenExpiry = Date.now() + 23 * 60 * 60 * 1000; // 23 hours\n            // Store in localStorage for persistence\n            localStorage.setItem(\"csrf-token\", csrfToken);\n            localStorage.setItem(\"session-token\", sessionToken);\n            localStorage.setItem(\"csrf-expiry\", this.tokenExpiry.toString());\n            console.log(\"CSRF token fetched successfully\");\n            return {\n                csrfToken,\n                sessionToken\n            };\n        } catch (error) {\n            console.error(\"Failed to fetch CSRF token:\", error);\n            throw new Error(\"Failed to fetch CSRF token\");\n        }\n    }\n    /**\n   * Get current CSRF token, fetch new one if expired\n   * @returns {Promise<string>}\n   */ async getCsrfToken() {\n        // Check if we have a valid token\n        if (this.isTokenValid()) {\n            return this.csrfToken;\n        }\n        // Try to load from localStorage\n        const storedToken = localStorage.getItem(\"csrf-token\");\n        const storedSession = localStorage.getItem(\"session-token\");\n        const storedExpiry = localStorage.getItem(\"csrf-expiry\");\n        if (storedToken && storedSession && storedExpiry) {\n            const expiry = parseInt(storedExpiry);\n            if (Date.now() < expiry) {\n                this.csrfToken = storedToken;\n                this.sessionToken = storedSession;\n                this.tokenExpiry = expiry;\n                return this.csrfToken;\n            }\n        }\n        // Fetch new token\n        const { csrfToken } = await this.fetchCsrfToken();\n        return csrfToken;\n    }\n    /**\n   * Get current session token\n   * @returns {Promise<string>}\n   */ async getSessionToken() {\n        if (!this.isTokenValid()) {\n            await this.getCsrfToken(); // This will fetch both tokens\n        }\n        return this.sessionToken;\n    }\n    /**\n   * Check if current token is valid\n   * @returns {boolean}\n   */ isTokenValid() {\n        return this.csrfToken && this.sessionToken && this.tokenExpiry && Date.now() < this.tokenExpiry;\n    }\n    /**\n   * Clear stored tokens\n   */ clearTokens() {\n        this.csrfToken = null;\n        this.sessionToken = null;\n        this.tokenExpiry = null;\n        localStorage.removeItem(\"csrf-token\");\n        localStorage.removeItem(\"session-token\");\n        localStorage.removeItem(\"csrf-expiry\");\n    }\n    /**\n   * Get headers for secure requests\n   * @returns {Promise<Object>}\n   */ async getSecureHeaders() {\n        const csrfToken = await this.getCsrfToken();\n        const sessionToken = await this.getSessionToken();\n        return {\n            \"X-CSRF-Token\": csrfToken,\n            \"X-Session-Token\": sessionToken\n        };\n    }\n    /**\n   * Verify CSRF token with backend (for testing)\n   * @returns {Promise<boolean>}\n   */ async verifyCsrfToken() {\n        try {\n            const headers = await this.getSecureHeaders();\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].post(\"\".concat(_Data_ApolloClient__WEBPACK_IMPORTED_MODULE_0__.baseUrl, \"/security/verify-csrf\"), {}, {\n                headers,\n                withCredentials: true\n            });\n            return response.data.valid;\n        } catch (error) {\n            console.error(\"CSRF token verification failed:\", error);\n            return false;\n        }\n    }\n    constructor(){\n        this.csrfToken = null;\n        this.sessionToken = null;\n        this.tokenExpiry = null;\n    }\n}\n// Create singleton instance\nconst csrfManager = new CsrfManager();\n/**\n * Get CSRF token for use in requests\n * @returns {Promise<string>}\n */ const getCsrfToken = ()=>csrfManager.getCsrfToken();\n/**\n * Get session token for use in requests\n * @returns {Promise<string>}\n */ const getSessionToken = ()=>csrfManager.getSessionToken();\n/**\n * Get secure headers for GraphQL requests\n * @returns {Promise<Object>}\n */ const getSecureHeaders = ()=>csrfManager.getSecureHeaders();\n/**\n * Clear CSRF tokens (on logout)\n */ const clearCsrfTokens = ()=>csrfManager.clearTokens();\n/**\n * Initialize CSRF tokens (call on app startup)\n * @returns {Promise<void>}\n */ const initializeCsrfTokens = async ()=>{\n    try {\n        await csrfManager.getCsrfToken();\n        console.log(\"CSRF tokens initialized\");\n    } catch (error) {\n        console.warn(\"Failed to initialize CSRF tokens:\", error);\n    }\n};\n/**\n * Verify CSRF token (for testing)\n * @returns {Promise<boolean>}\n */ const verifyCsrfToken = ()=>csrfManager.verifyCsrfToken();\n/* harmony default export */ __webpack_exports__[\"default\"] = (csrfManager);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/csrfUtils.js\n"));

/***/ })

});