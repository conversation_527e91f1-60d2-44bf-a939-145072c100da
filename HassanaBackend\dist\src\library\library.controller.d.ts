import { LibraryService } from './library.service';
import { UUID } from 'crypto';
export declare class LibraryController {
    private readonly libraryService;
    constructor(libraryService: LibraryService);
    createFile(file: Express.Multer.File): Promise<{
        code: number;
        message: string;
        data: import("./entities/library.entity").Library;
    } | {
        code: any;
        message: any;
        error: any;
    }>;
    findAllLibrary(): Promise<Object>;
    removeLibrary(id: UUID): Promise<{
        code: number;
        message: string;
        data: import("./entities/library.entity").Library;
    } | {
        code: any;
        mesage: any;
        error: any;
    }>;
}
