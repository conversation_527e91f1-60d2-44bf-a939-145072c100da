"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const jwt = require("jsonwebtoken");
const auth_guard_1 = require("./auth/auth.guard");
const jwt_guard_1 = require("./auth/jwt.guard");
const user_entity_1 = require("./users/entities/user.entity");
const role_guard_1 = require("./auth/role.guard");
let AppResolver = class AppResolver {
    index() {
        return 'Nest JS Graphql server .....';
    }
    securedDataForAdmin(user) {
        return 'This is secured data for admin ' + JSON.stringify(user);
    }
    securedDataForUser(user) {
        return 'This is secured data for user ' + JSON.stringify(user);
    }
    login(email, password, user) {
        let payload = {
            id: user.id,
            name: user.name,
            username: user.user_principal_name,
            role: user.role
        };
        const JWT_KEY = process.env.JWT_KEY || 'fallback-secret-key';
        const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';
        return jwt.sign(payload, JWT_KEY, { expiresIn: JWT_EXPIRES_IN });
    }
};
exports.AppResolver = AppResolver;
__decorate([
    (0, graphql_1.Query)((returns) => String),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", String)
], AppResolver.prototype, "index", null);
__decorate([
    (0, graphql_1.Query)((returns) => String),
    (0, common_1.UseGuards)(jwt_guard_1.JwtGuard, new role_guard_1.RoleGuard(role_guard_1.Roles.ADMIN)),
    __param(0, (0, graphql_1.Context)('user')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", String)
], AppResolver.prototype, "securedDataForAdmin", null);
__decorate([
    (0, graphql_1.Query)((returns) => String),
    (0, common_1.UseGuards)(jwt_guard_1.JwtGuard, new role_guard_1.RoleGuard(role_guard_1.Roles.USER)),
    __param(0, (0, graphql_1.Context)('user')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", String)
], AppResolver.prototype, "securedDataForUser", null);
__decorate([
    (0, graphql_1.Query)((returns) => String),
    (0, common_1.UseGuards)(auth_guard_1.AuthGuard),
    __param(0, (0, graphql_1.Args)({ name: 'email', type: () => String })),
    __param(1, (0, graphql_1.Args)({ name: 'password', type: () => String })),
    __param(2, (0, graphql_1.Context)('user')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, user_entity_1.User]),
    __metadata("design:returntype", String)
], AppResolver.prototype, "login", null);
exports.AppResolver = AppResolver = __decorate([
    (0, graphql_1.Resolver)((of) => String)
], AppResolver);
//# sourceMappingURL=app.resolver.js.map