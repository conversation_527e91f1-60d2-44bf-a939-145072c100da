"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserFeedbackService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_feedback_entity_1 = require("./entities/user-feedback.entity");
const typeorm_2 = require("typeorm");
const Emailer_1 = require("../Emailer");
let UserFeedbackService = class UserFeedbackService {
    constructor(feedbackRepository) {
        this.feedbackRepository = feedbackRepository;
    }
    async create(createUserFeedbackDto, userId) {
        (0, Emailer_1.sendMail)({
            from: "<EMAIL>",
            to: "<EMAIL>",
            subject: `${createUserFeedbackDto.type} - ${createUserFeedbackDto.subject}`,
            text: createUserFeedbackDto.description,
            html: null
        });
        const create = this.feedbackRepository.create({
            ...createUserFeedbackDto,
            user_id: userId
        });
        return await this.feedbackRepository.save(create);
    }
    ;
    async findAll() {
        return await this.feedbackRepository.find();
    }
    ;
    async findOne(id) {
        const resource = await this.feedbackRepository.findOne({ where: { id } });
        if (!resource) {
            throw new common_1.NotFoundException(`Resource with ID ${id} not found`);
        }
        return resource;
    }
    ;
    async remove(id) {
        const toDelete = await this.findOne(id);
        await this.feedbackRepository.remove(toDelete);
        return toDelete;
    }
    ;
};
exports.UserFeedbackService = UserFeedbackService;
exports.UserFeedbackService = UserFeedbackService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_feedback_entity_1.UserFeedback)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], UserFeedbackService);
//# sourceMappingURL=user-feedback.service.js.map