import { Repository } from 'typeorm';
import { UpdateUserInput } from './dto/update-user.input';
import { User } from './entities/user.entity';
import { UUID } from 'crypto';
export declare class UserService {
    readonly userRepo: Repository<User>;
    private readonly LDAP_URL;
    private readonly BASE_DN;
    constructor(userRepo: Repository<User>);
    userAuth(username: string, password: string): Promise<any>;
    private authenticateWithAD;
    private searchUserInLDAP;
    private handleLDAPSearchResults;
    getNameGender(name: string): Promise<string>;
    createNewUser(newUser: any): Promise<User>;
    update(id: UUID, updateUserInput: UpdateUserInput): Promise<User>;
    findNewUsers(days: number): Promise<User[]>;
    findAllCulturalAmbassadors(): Promise<User[]>;
    findUserById(id: UUID): Promise<User>;
    findAllUsers(page: number | null, pageSize: number | null): Promise<{
        users: any[];
        meta: object;
    }>;
    findUserByEmail(username: string): Promise<any>;
    deleteUser(id: number): Promise<String>;
}
