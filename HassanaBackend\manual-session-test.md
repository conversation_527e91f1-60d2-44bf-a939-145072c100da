# Manual Single Session Test

## Prerequisites
1. Backend server should be running on `http://localhost:3001`
2. <PERSON>is should be running and accessible
3. You should have a test user account

## Test Steps

### Step 1: Check Current Session Status
```bash
curl -X GET "http://localhost:3001/security/session-status?userId=YOUR_USER_ID"
```

### Step 2: Login from Browser 1 (Simulate Device 1)
```bash
curl -X POST http://localhost:3001/graphql \
  -H "Content-Type: application/json" \
  -H "User-Agent: Browser1-Chrome" \
  -d '{
    "query": "query LoginUser($username: String!, $password: String!) { loginUser(username: $username, password: $password) { id username role token } }",
    "variables": {
      "username": "YOUR_USERNAME",
      "password": "YOUR_PASSWORD"
    }
  }'
```

**Expected Result:** Should return user data with JWT token
**Save the token as TOKEN_1**

### Step 3: Test Authenticated Request with Token 1
```bash
curl -X POST http://localhost:3001/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer TOKEN_1" \
  -d '{
    "query": "query { securedDataForUser }"
  }'
```

**Expected Result:** Should return secured data successfully

### Step 4: Check Session Status After First Login
```bash
curl -X GET "http://localhost:3001/security/session-status?userId=YOUR_USER_ID"
```

**Expected Result:** Should show one active session

### Step 5: Login from Browser 2 (Simulate Device 2)
```bash
curl -X POST http://localhost:3001/graphql \
  -H "Content-Type: application/json" \
  -H "User-Agent: Browser2-Firefox" \
  -d '{
    "query": "query LoginUser($username: String!, $password: String!) { loginUser(username: $username, password: $password) { id username role token } }",
    "variables": {
      "username": "YOUR_USERNAME",
      "password": "YOUR_PASSWORD"
    }
  }'
```

**Expected Result:** Should return user data with NEW JWT token
**Save the token as TOKEN_2**

### Step 6: Test Token 1 (Should Fail)
```bash
curl -X POST http://localhost:3001/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer TOKEN_1" \
  -d '{
    "query": "query { securedDataForUser }"
  }'
```

**Expected Result:** Should return error "Session is no longer valid. Please log in again."

### Step 7: Test Token 2 (Should Work)
```bash
curl -X POST http://localhost:3001/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer TOKEN_2" \
  -d '{
    "query": "query { securedDataForUser }"
  }'
```

**Expected Result:** Should return secured data successfully

### Step 8: Check Session Status After Second Login
```bash
curl -X GET "http://localhost:3001/security/session-status?userId=YOUR_USER_ID"
```

**Expected Result:** Should show one active session (the new one)

### Step 9: Test Logout
```bash
curl -X POST http://localhost:3001/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer TOKEN_2" \
  -d '{
    "query": "mutation LogoutUser($userId: String!) { logoutUser(userId: $userId) }",
    "variables": {
      "userId": "YOUR_USER_ID"
    }
  }'
```

**Expected Result:** Should return "Logged out successfully"

### Step 10: Test Token 2 After Logout (Should Fail)
```bash
curl -X POST http://localhost:3001/graphql \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer TOKEN_2" \
  -d '{
    "query": "query { securedDataForUser }"
  }'
```

**Expected Result:** Should return error "Session is no longer valid. Please log in again."

## What to Look For

### ✅ Success Indicators:
1. Only one session exists at a time in Redis
2. Previous tokens become invalid when new login occurs
3. Logout properly invalidates the current session
4. Session status endpoint shows correct information

### ❌ Failure Indicators:
1. Multiple sessions exist simultaneously
2. Old tokens continue to work after new login
3. Sessions are not properly cleaned up
4. Redis doesn't contain session data

## Debugging

### Check Redis Directly:
```bash
redis-cli
> KEYS user_session:*
> HGETALL user_session:YOUR_USER_ID
> KEYS blacklist:*
```

### Check Application Logs:
Look for log messages like:
- "New session created for user X with session ID: Y"
- "User X already has an active session. Invalidating previous session."
- "Session ID mismatch for user X"
- "User X logged out successfully"

## Common Issues

1. **Redis not connected**: Check Redis connection in logs
2. **Session service not injected**: Check if SessionService is properly imported
3. **JWT guard not using session validation**: Verify JwtGuard is calling validateUserSession
4. **Frontend not detecting conflicts**: Check session monitoring implementation

## Browser Testing

You can also test this manually in browsers:

1. Open Browser 1 (Chrome) and login to the application
2. Open Browser 2 (Firefox) and login with the same credentials
3. Go back to Browser 1 and try to make a request
4. Browser 1 should detect session conflict and show appropriate message

## Expected Behavior Summary

- ✅ User can only have ONE active session at a time
- ✅ New login invalidates previous session
- ✅ Invalid sessions are properly cleaned up
- ✅ Tokens are blacklisted when sessions are invalidated
- ✅ Frontend detects session conflicts and handles them gracefully
