import { CreateNotificationInput } from './dto/create-notification.input';
import { UpdateNotificationInput } from './dto/update-notification.input';
import { Repository } from 'typeorm';
import { NotificationEntity, NotificationViewEntity } from './entities/notification.entity';
import { User } from '@app/users/entities/user.entity';
import { UUID } from 'crypto';
export declare class NotificationService {
    private readonly notificationRepo;
    private readonly notificationViewRepo;
    private readonly userRepo;
    constructor(notificationRepo: Repository<NotificationEntity>, notificationViewRepo: Repository<NotificationViewEntity>, userRepo: Repository<User>);
    create(createNotificationInput: CreateNotificationInput): Promise<CreateNotificationInput & NotificationEntity>;
    addView(notificationId: UUID, userId: UUID): Promise<NotificationViewEntity>;
    findAllNotificationViews(): Promise<NotificationViewEntity[]>;
    findAll(): Promise<NotificationEntity[]>;
    getAllNewNotification(): Promise<NotificationEntity[]>;
    getAllNewNotificationsForUser(userId: number): Promise<NotificationEntity[]>;
    getUnseenNotificationsCount(userId: UUID): Promise<number>;
    markAllNotificationsAsSeen(userId: UUID): Promise<boolean>;
    findOne(id: UUID): string;
    update(id: UUID, updateNotificationInput: UpdateNotificationInput): Promise<UpdateNotificationInput & NotificationEntity>;
    remove(id: UUID): Promise<NotificationEntity>;
}
