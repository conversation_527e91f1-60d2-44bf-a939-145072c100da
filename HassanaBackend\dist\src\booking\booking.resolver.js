"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookingResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const booking_service_1 = require("./booking.service");
const update_booking_input_1 = require("./dto/update-booking.input");
const booking_schema_1 = require("./schema/booking.schema");
const jwt_guard_1 = require("../auth/jwt.guard");
let BookingResolver = class BookingResolver {
    constructor(bookingService) {
        this.bookingService = bookingService;
    }
    findAll() {
        return this.bookingService.findAll();
    }
    getAllBookingOfTeaBoy() {
        return this.bookingService.getAllBookingTeaBoy();
    }
    findByUser(id) {
        return this.bookingService.findByUser(id);
    }
    createBooking(updateBookingInput) {
        return this.bookingService.create(updateBookingInput);
    }
};
exports.BookingResolver = BookingResolver;
__decorate([
    (0, graphql_1.Query)(() => [booking_schema_1.BookingSchema], { name: 'bookings' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BookingResolver.prototype, "findAll", null);
__decorate([
    (0, graphql_1.Query)(() => [booking_schema_1.BookingSchema], { name: 'bookingsOfTeaBoy' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BookingResolver.prototype, "getAllBookingOfTeaBoy", null);
__decorate([
    (0, graphql_1.Query)(() => [booking_schema_1.BookingSchema], { name: 'bookingsOfUser' }),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BookingResolver.prototype, "findByUser", null);
__decorate([
    (0, common_1.UseGuards)(jwt_guard_1.JwtGuard),
    (0, graphql_1.Mutation)(() => booking_schema_1.BookingSchema),
    __param(0, (0, graphql_1.Args)('CreateBookingInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_booking_input_1.UpdateBookingInput]),
    __metadata("design:returntype", void 0)
], BookingResolver.prototype, "createBooking", null);
exports.BookingResolver = BookingResolver = __decorate([
    (0, graphql_1.Resolver)(() => booking_schema_1.BookingSchema),
    __metadata("design:paramtypes", [booking_service_1.BookingService])
], BookingResolver);
//# sourceMappingURL=booking.resolver.js.map