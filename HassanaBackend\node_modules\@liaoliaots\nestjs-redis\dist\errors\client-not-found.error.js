"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientNotFoundError = void 0;
/**
 * Thrown when consumer tries to get client that does not exist.
 */
class ClientNotFoundError extends Error {
    constructor(namespace) {
        super(`Connection "${namespace}" was not found.`);
        this.name = ClientNotFoundError.name;
    }
}
exports.ClientNotFoundError = ClientNotFoundError;
