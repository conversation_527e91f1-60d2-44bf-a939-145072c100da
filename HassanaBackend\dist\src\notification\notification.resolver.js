"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const notification_service_1 = require("./notification.service");
const create_notification_input_1 = require("./dto/create-notification.input");
const update_notification_input_1 = require("./dto/update-notification.input");
const notification_schema_1 = require("./schema/notification.schema");
const jwt_guard_1 = require("../auth/jwt.guard");
let NotificationResolver = class NotificationResolver {
    constructor(notificationService) {
        this.notificationService = notificationService;
    }
    createNotification(createNotificationInput) {
        return this.notificationService.create(createNotificationInput);
    }
    addNotificationView(notificationId, userId) {
        return this.notificationService.addView(notificationId, userId);
    }
    notifications() {
        return this.notificationService.findAll();
    }
    notificationViews() {
        return this.notificationService.findAllNotificationViews();
    }
    newNotificationsForUser(id) {
        return this.notificationService.getAllNewNotificationsForUser(id);
    }
    unseenNotificationsCount(userId) {
        return this.notificationService.getUnseenNotificationsCount(userId);
    }
    markAllNotificationsAsSeen(userId) {
        return this.notificationService.markAllNotificationsAsSeen(userId);
    }
    notification(id) {
        return this.notificationService.findOne(id);
    }
    updateNotification(id, updateNotificationInput) {
        return this.notificationService.update(id, updateNotificationInput);
    }
    removeNotification(id) {
        return this.notificationService.remove(id);
    }
};
exports.NotificationResolver = NotificationResolver;
__decorate([
    (0, common_1.UseGuards)(jwt_guard_1.JwtGuard),
    (0, graphql_1.Mutation)(() => notification_schema_1.NotificationSchema),
    __param(0, (0, graphql_1.Args)('createNotificationInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_notification_input_1.CreateNotificationInput]),
    __metadata("design:returntype", void 0)
], NotificationResolver.prototype, "createNotification", null);
__decorate([
    (0, common_1.UseGuards)(jwt_guard_1.JwtGuard),
    (0, graphql_1.Mutation)(() => notification_schema_1.NotificationSchema),
    __param(0, (0, graphql_1.Args)('notificationId', { type: () => graphql_1.ID })),
    __param(1, (0, graphql_1.Args)('userId', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], NotificationResolver.prototype, "addNotificationView", null);
__decorate([
    (0, graphql_1.Query)(() => [notification_schema_1.NotificationSchema]),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], NotificationResolver.prototype, "notifications", null);
__decorate([
    (0, graphql_1.Query)(() => [notification_schema_1.NotificationSchema]),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], NotificationResolver.prototype, "notificationViews", null);
__decorate([
    (0, graphql_1.Query)(() => [notification_schema_1.NotificationSchema]),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], NotificationResolver.prototype, "newNotificationsForUser", null);
__decorate([
    (0, graphql_1.Query)(() => graphql_1.ID),
    __param(0, (0, graphql_1.Args)('userId', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NotificationResolver.prototype, "unseenNotificationsCount", null);
__decorate([
    (0, common_1.UseGuards)(jwt_guard_1.JwtGuard),
    (0, graphql_1.Mutation)(() => Boolean),
    __param(0, (0, graphql_1.Args)('userId', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NotificationResolver.prototype, "markAllNotificationsAsSeen", null);
__decorate([
    (0, graphql_1.Query)(() => notification_schema_1.NotificationSchema),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NotificationResolver.prototype, "notification", null);
__decorate([
    (0, common_1.UseGuards)(jwt_guard_1.JwtGuard),
    (0, graphql_1.Mutation)(() => notification_schema_1.NotificationSchema),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __param(1, (0, graphql_1.Args)('updateNotificationInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_notification_input_1.UpdateNotificationInput]),
    __metadata("design:returntype", void 0)
], NotificationResolver.prototype, "updateNotification", null);
__decorate([
    (0, common_1.UseGuards)(jwt_guard_1.JwtGuard),
    (0, graphql_1.Mutation)(() => notification_schema_1.NotificationSchema),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], NotificationResolver.prototype, "removeNotification", null);
exports.NotificationResolver = NotificationResolver = __decorate([
    (0, graphql_1.Resolver)(() => notification_schema_1.NotificationSchema),
    __metadata("design:paramtypes", [notification_service_1.NotificationService])
], NotificationResolver);
//# sourceMappingURL=notification.resolver.js.map