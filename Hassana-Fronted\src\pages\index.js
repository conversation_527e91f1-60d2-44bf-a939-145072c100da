import React, { useState } from "react";
import Dashboard from "@/components/Dashboard";
import { Box, Grid, Typography } from "@mui/material";
import TaskSummary from "@/components/TaskSummary";
import Celebration from "@/components/Celebration";
import PropTypes from "prop-types";
import Schedule from "@/components/Schedule";
import NewJoin from "@/components/NewJoin";
import LeaveTracker from "@/components/LeaveTracker";
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import { useColor } from "@/components/ColorContext";
import { useSelectedColor } from "@/components/HelperFunctions";
import { breakpoints } from "../helper/mediaQueries";
import Link from "next/link";
import Quote from "@/components/Quote";
// import withAuth from "@/components/auth/withAuth";
import { withAuthServerSideProps } from "@/components/auth/withAuthServerSide";
import { ModeProvider } from "@/components/ModeContext";
import withAuth from "@/components/auth/withAuth";
import WeatherComponent from "@/components/WeatherComponent";
import Hassan<PERSON><PERSON><PERSON>PopUp from "@/components/HassanaOfferPopUp";
import YourIdeasModal from "@/components/VoiceModal";
import { useSession } from "next-auth/react";
import { useRouter } from 'next/router';
import { lightTheme } from "@/theme";
import RatePopup from "@/components/RateModal";
import OfferCard from "@/components/OfferCard";
import VerticalScroller from "@/components/VerticalScroller";

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}
export const HomeComponent = () => {
  const theme = useTheme();
  const { color } = useColor();

  const {
    smallScreen,
    mediumScreen,
    largeScreen,
    xLargeScreen,
    xxLargeScreen,
    xxxLargeScreen,
    xxxxLargeScreen,
  } = breakpoints;

  const isSmallScreen = useMediaQuery(smallScreen);
  const isMediumScreen = useMediaQuery(mediumScreen);
  const isLargeScreen = useMediaQuery(largeScreen);
  const isXLargeScreen = useMediaQuery(xLargeScreen);
  const isXXLargeScreen = useMediaQuery(xxLargeScreen);
  const isXXXLargeScreen = useMediaQuery(xxxLargeScreen);
  const isXXXXLargeScreen = useMediaQuery(xxxxLargeScreen);

  let mainDivWidth;
  if (isSmallScreen) {
    mainDivWidth = "70vw";
  } else if (isMediumScreen) {
    mainDivWidth = "70vw";
  } else if (isLargeScreen) {
    mainDivWidth = "77vw";
  } else if (isXLargeScreen) {
    mainDivWidth = "54vw";
  } else if (isXXLargeScreen) {
    mainDivWidth = "62vw";
  } else if (isXXXLargeScreen) {
    mainDivWidth = "67vw";
  } else if (isXXXXLargeScreen) {
    mainDivWidth = "70vw";
  } else {
    mainDivWidth = "70vw";
  }

  const data = [
    {
      name: "New IT Ticket",
      tag: "Raise Task In Board",
      icon: "/images/Ticket.png",
      link: "https://support.hassana.com.sa/",
      target: "_blank",
    },
    {
      name: "Submit Leave",
      tag: "Add Leave Request",
      icon: "/images/clipboard.png",
      link: "https://fa-exsq-saasfaprod1.fa.ocs.oraclecloud.com/hcmUI/faces/FndOverview?macKey=vLT97HMqpC6JvxLz&amp;_afrLoop=4116657012806712&amp;_afrWindowMode=0&amp;_afrWindowId=null&amp;_adf.ctrl-state=fnkgj5sn9_14&amp;_afrFS=16&amp;_afrMT=screen&amp;_afrMFW=1272&amp;_afrMFH=686&amp;_afrMFDW=1280&amp;_afrMFDH=800&amp;_afrMFC=8&amp;_afrMFCI=0&amp;_afrMFM=0&amp;_afrMFR=144&amp;_afrMFG=0&amp;_afrMFS=0&amp;_afrMFO=0",
      target: "_blank",
    },
    {
      name: "Your Voice Matters",
      tag: "Innovative Ideas",
      icon: "/images/your voice.png",
      link: "https://forms.office.com/pages/responsepage.aspx?id=lZNxJq_6M0qt0PNs91rIfTyK2reu4A1OvI4rfIC09cRUN0syOEkwODdBSkdaQjUwRVJDQlRUWDZLVi4u&amp;web=1&amp;wdLOR=cA5E9AA54-B977-4DA5-9AEA-E9B6711C02BF",
      target: "_blank",
    },
    {
      name: "Thank You Program",
      tag: "Gratitude in Action",
      icon: "/images/like.png",
      link: "/AddressBook",
      target: "",
    },

    // {
    //   name: "Book Meeting",
    //   tag: "Request For Meeting",
    //   icon: "/icons/button3.png",
    //   link: "/allocator",
    // },
  ];
  const data2 = [
    {
      name: "New IT Ticket",
      tag: "Raise Task In Board",
      icon: "/icons/light-icon-button1.png",
      link: "https://support.hassana.com.sa/",
      target: "",
    },
    {
      name: "Submit Leave",
      tag: "Add Leave Request",
      icon: "/icons/light-icon-button2.png",
      link: "https://fa-exsq-saasfaprod1.fa.ocs.oraclecloud.com/hcmUI/faces/FndOverview?macKey=vLT97HMqpC6JvxLz&amp;_afrLoop=4116657012806712&amp;_afrWindowMode=0&amp;_afrWindowId=null&amp;_adf.ctrl-state=fnkgj5sn9_14&amp;_afrFS=16&amp;_afrMT=screen&amp;_afrMFW=1272&amp;_afrMFH=686&amp;_afrMFDW=1280&amp;_afrMFDH=800&amp;_afrMFC=8&amp;_afrMFCI=0&amp;_afrMFM=0&amp;_afrMFR=144&amp;_afrMFG=0&amp;_afrMFS=0&amp;_afrMFO=0",
      target: "_blank",
    },
    {
      name: "Your Voice Matters",
      tag: "Innovative Ideas",
      icon: "/icons/light-icon-button3.png",
      link: "https://forms.office.com/pages/responsepage.aspx?id=lZNxJq_6M0qt0PNs91rIfTyK2reu4A1OvI4rfIC09cRUN0syOEkwODdBSkdaQjUwRVJDQlRUWDZLVi4u&amp;web=1&amp;wdLOR=cA5E9AA54-B977-4DA5-9AEA-E9B6711C02BF",
      target: "_blank",
    },
    {
      name: "Thank You Program",
      tag: "Gratitude in Action",
      icon: "/icons/light-icon-4.png",
      link: "/AddressBook",
      target: "",
    },
    // {
    //   name: "Book Meeting",
    //   tag: "Request For Meeting",
    //   icon: "/icons/light-icon-4.png",
    //   link: "/allocator",
    //   target: "",
    // },
  ];
  const slides = [
    { title: "Company Special Events at 10:30 pm" },
    { title: "Company Special Events at 11:30 pm" },
    { title: "Company Special Events at 12:30 pm" },
  ];
  // const context = useContext(ColorContext);
  // const theme = useTheme();

  const router = useRouter();
  const selectedColor = useSelectedColor(color);
  const [hassanaPopup, setHassanaPopup] = useState(false);
  const [ideasModalOpen, setIdeasModalOpen] = useState(false);
  const { data: session } = useSession();
  const isAdmin = session?.user.role === "ADMIN";

  const handleThankYouProgram = () => {
    router.push("/AddressBook");
  };

  function showPopup() {
    setHassanaPopup(true);
  }
  function closePopup() {
    setHassanaPopup(false);
  }

  function openIdeasModal() {
    setIdeasModalOpen(true);
  }

  function closeIdeasModal() {
    setIdeasModalOpen(false);
  }

  return (
    <>
      <Box
        sx={{
          width: mainDivWidth,
          margin: "auto",
          height: "90vh",
          overflow: "auto",
          overflow: "auto",
          "&::-webkit-scrollbar": {
            width: 0,
          },
          scrollbarWidth: "none",
          msOverflowStyle: "none",
        }}
      >
        <Box sx={{ marginY: "20px" }}>
          <Grid container spacing={isSmallScreen ? 1 : 3}>
            <Grid item xs={12} sm={12} md={6}>
              {/* <TaskSummary /> */}

              <Quote />
            </Grid>
            <Grid item xs={12} sm={12} md={6}>
              {/* <VerticalScroller/> */}
              <Celebration />
            </Grid>
          </Grid>

          {/* <Box sx={{ marginY: "20px" }}>
            <Grid container spacing={isSmallScreen ? 1 : 3}>
              <Grid
                item
                xs={12}
                sm={12}
                md={12}
                style={
                  isSmallScreen ? { marginTop: "40px" } : { marginTop: "0px" }
                }
              >
                <NewJoin />
              </Grid>
            </Grid>
          </Box> */}
          {/* celebration and quate of the day */}
        </Box>
        <Box sx={{ marginY: "20px" }}>
          <Grid container spacing={isSmallScreen ? 1 : 3}>
            {(color != "white" ? data2 : data).map((btn, index) => {
              console.log("in home", color);

              return (
                <Grid item xs={6} sm={6} md={3} key={index}>
                  <Box
                    sx={{
                      background:
                        selectedColor == theme.palette.background.primary
                          ? theme.palette.text.blue
                          : selectedColor,
                      color:
                        selectedColor == theme.palette.background.primary
                          ? theme.palette.text.primary
                          : theme.palette.text.white,
                      textAlign: "left",
                      width: "100%",
                      height: "100%",
                      cursor: "default",
                      borderRadius: "10px",
                      boxShadow: "0px 4px 20px 0px rgba(0, 0, 0, 0.05)",
                      padding: isSmallScreen ? "5px" : "10px",
                    }}
                  >
                    {index != 3 && index != 2 && (
                      <Link href={btn.link} target={btn.target}>
                        <Box sx={{ display: "flex" }}>
                          <Box sx={{ flexGrow: 1 }}>
                            <Typography variant="body4" fontWeight="900">
                              {btn.name}
                            </Typography>
                            <Typography variant="subtitle2" color="black">
                              {btn.tag}
                            </Typography>
                          </Box>
                          <Box>
                            <img src={btn.icon} loading="lazy" width={20} />
                          </Box>
                        </Box>
                      </Link>
                    )}

                    {/* Your Ideas box (index 2) */}
                    {index == 2 && (
                      <Box
                        sx={{ display: "flex", cursor: "pointer" }}
                        onClick={openIdeasModal}
                      >
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="body4" fontWeight="900">
                            {btn.name}
                          </Typography>
                          <Typography variant="subtitle2" color="black">
                            {btn.tag}
                          </Typography>
                        </Box>
                        <Box>
                          <img src={btn.icon} loading="lazy" width={25} />
                        </Box>
                      </Box>
                    )}

                    {/* Thank You Program box (index 3) */}
                    {index === 3 && (
                      <Box
                        sx={{
                          display: "flex",
                          cursor: "pointer",
                          background: lightTheme.palette.blue,
                        }}
                        onClick={handleThankYouProgram}
                      >
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="body4" fontWeight="900">
                            {btn.name}
                          </Typography>
                          <Typography variant="subtitle2" color="black">
                            {btn.tag}
                          </Typography>
                        </Box>
                        <Box>
                          <img src={btn.icon} loading="lazy" width={25} />
                        </Box>
                      </Box>
                    )}
                  </Box>
                </Grid>
              );
            })}
          </Grid>
        </Box>

        <Box sx={{ marginY: "20px" }}>
          <Grid container spacing={isSmallScreen ? 1 : 3}>
            <Grid item xs={12} sm={12} md={6}>
              <Schedule />
            </Grid>
            <Grid item xs={12} sm={12} md={6}>
              {/* <WeatherComponent /> */}
              <LeaveTracker />
            </Grid>
          </Grid>
        </Box>

        {/* celebration and quate of the day */}
        {/* <RatePopup open={thankyouModalOpen} onClose={closeThankyouModal} /> */}

        {/* {hassanaPopup == true && <HassanaOfferPopUp closePopup={closePopup} />} */}

        {/* Your Ideas Modal */}
        <YourIdeasModal open={ideasModalOpen} handleClose={closeIdeasModal} />

        <Box sx={{ marginY: "20px" }}>
          <Grid container spacing={isSmallScreen ? 1 : 3}>
            <Grid item xs={12} sm={12} md={12}>
              <NewJoin />
            </Grid>
          </Grid>
        </Box>

      </Box>
    </>
  );
};

const Home = () => {
  return (
    <Dashboard>
      <HomeComponent />
    </Dashboard>
  );
};

// export default Home;
export default withAuth(Home);
