import { CreateUserReviewDto } from './dto/create-user-review.dto';
import { UpdateUserReviewDto } from './dto/update-user-review.dto';
import { UserReview } from './entities/user-review.entity';
import { Repository } from 'typeorm';
import { UUID } from 'crypto';
export declare class UserReviewsService {
    private reviewRepository;
    constructor(reviewRepository: Repository<UserReview>);
    create(createUserReviewDto: CreateUserReviewDto, user_id: UUID): Promise<UserReview>;
    findAll(filter?: object): Promise<UserReview[]>;
    findOne(id: number): string;
    update(id: number, updateUserReviewDto: UpdateUserReviewDto): string;
    remove(id: number): string;
}
