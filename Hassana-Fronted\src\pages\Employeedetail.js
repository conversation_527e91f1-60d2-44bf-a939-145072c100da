import React, { useState, useEffect } from "react";
import {
  <PERSON>rid,
  <PERSON>,
  Typo<PERSON>,
  TextField,
  Avatar,
  Button,
  Rating,
  Alert,
  FormControl,
  Checkbox,
  FormControlLabel,
} from "@mui/material";
import Dashboard from "@/components/Dashboard";
import RatePopup from "@/components/RateModal";
import { useRouter } from "next/router";
import { useSession } from "next-auth/react";
import { getAllUsers } from "@/Data/User";
import { useTheme } from "@emotion/react";
import { baseUrl } from "@/Data/ApolloClient";

const EmployeeProfile = () => {
  const router = useRouter();
  const { userId, employee: employeeData } = router.query;
  const { data: session, status, update: updateSession } = useSession();
  const [employee, setEmployee] = useState(null);
  const [errorMessage, setErrorMessage] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [editedName, setEditedName] = useState("");
  const [editedExtension, setEditedExtension] = useState("");
  const [editedIsNewJoiner, setEditedIsNewJoiner] = useState(false);
  const [editedIsCulturalAmbassador, setEditedIsCulturalAmbassador] = useState(false);
  const [editedDepartment, setEditedDepartment] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [openThankYouModal, setOpenThankYouModal] = useState(false);
  const theme = useTheme();

  const isAdmin = session?.user?.role === "ADMIN";

  // Predefined avatars (consistent with ProfileEdit)
  const avatars = [
    '/gAvatar.svg',
    '/gAvatar2.jpg',
    '/pAvatar.avif',
    '/pAvatar2.avif',
  ];

  // Helper function to construct and validate image URL
  const getImageUrl = (profilePath, userId) => {
    if (!profilePath) {
      console.log("No profile path provided, using fallback for user:", userId);
      return `/profile${userId}.png`;
    }

    // Handle predefined avatars
    if (avatars.includes(profilePath) || avatars.includes('/' + profilePath)) {
      const avatarPath = profilePath.startsWith('/') ? profilePath : '/' + profilePath;
      console.log("Using predefined avatar:", avatarPath);
      return avatarPath;
    }

    // Handle server-stored custom images
    if (!profilePath.startsWith('http') && !profilePath.startsWith('/')) {
      const url = `${baseUrl}/v1/${profilePath}`;
      console.log("Constructed custom image URL:", url);
      return url;
    }

    // Handle full URLs
    if (profilePath.startsWith('http')) {
      console.log("Using server-provided full URL:", profilePath);
      return profilePath;
    }

    // Fallback for invalid paths
    console.log("Invalid profile path, using fallback:", profilePath);
    return `/profile${userId}.png`;
  };

  const handleReviewClick = () => setOpenThankYouModal(true);
  const handleCloseThankYou = () => setOpenThankYouModal(false);

  // Fetch employee data
  useEffect(() => {
    const fetchEmployee = async () => {
      if (employeeData) {
        try {
          const parsedEmployee = JSON.parse(employeeData);
          console.log("=== Employee Detail Debug (Query Data) ===");
          console.log("Parsed employeeData:", JSON.stringify(parsedEmployee, null, 2));
          console.log("Extension from query data:", parsedEmployee.extension);
          console.log("Profile/Image from query data:", parsedEmployee.profile, parsedEmployee.image);
          console.log("Activity from query data:", parsedEmployee.activity);
          const isCulturalAmbassador =
            parsedEmployee.activity === "Cultural Ambassador" ||
            parsedEmployee.is_cultural_ambassador === "true" ||
            parsedEmployee.is_cultural_ambassador === true;
          const isNewJoiner =
            parsedEmployee.activity === "New Joiner" ||
            parsedEmployee.new_joiner === "true" ||
            parsedEmployee.new_joiner === true;
          const employeeInfo = {
            id: parsedEmployee.id || "N/A",
            name: parsedEmployee.name || "Unknown",
            designation: parsedEmployee.designation || "Employee",
            department: parsedEmployee.department || "Unknown",
            is_cultural_ambassador: isCulturalAmbassador,
            new_joiner: isNewJoiner,
            email: parsedEmployee.email || "<EMAIL>",
            extension: parsedEmployee.extension || "",
            thisMonth: parseFloat(parsedEmployee.starScore) || 0,
            thisYear: parseFloat(parsedEmployee.thisYear) || 0,
            image: getImageUrl(parsedEmployee.profile || parsedEmployee.image, parsedEmployee.id),
            rating: parseInt(parsedEmployee.rating) || 5,
          };
          console.log("Employee info:", JSON.stringify(employeeInfo, null, 2));
          console.log("Final image URL (Query):", employeeInfo.image);
          setEmployee(employeeInfo);
          setEditedName(employeeInfo.name);
          setEditedExtension(employeeInfo.extension);
          setEditedIsNewJoiner(employeeInfo.new_joiner);
          setEditedIsCulturalAmbassador(employeeInfo.is_cultural_ambassador);
          setEditedDepartment(employeeInfo.department);
          setLoading(false);
          return;
        } catch (error) {
          console.error("Error parsing employee data:", error);
          setErrorMessage("Failed to parse employee data. Fetching from API...");
        }
      }

      if (!userId || !session) {
        console.log("Missing userId or session:", { userId, session });
        setLoading(false);
        setErrorMessage("Missing user ID or session. Please log in.");
        return;
      }

      setLoading(true);
      setErrorMessage(null);

      try {
        const token = session?.accessToken || localStorage.getItem("jwtToken");
        console.log("Fetching users with token:", token?.substring(0, 20) + "...", "for userId:", userId);
        const result = await getAllUsers(token);
        console.log("getAllUsers result:", JSON.stringify(result, null, 2));

        if (result?.error || result?.message) {
          setErrorMessage(`Error: ${result.error || result.message} (Status: ${result.status || "unknown"})`);
          setEmployee(null);
        } else {
          const user = result.data?.find((u) => u.id === userId || u.userId === userId);
          console.log("=== Employee Detail Debug (API Data) ===");
          console.log("Found user:", JSON.stringify(user, null, 2));
          console.log("Extension from API data:", user?.extension);
          console.log("Profile/Image from API data:", user?.profile, user?.image);
          console.log("Activity from API data:", user?.activity);
          console.log("Cultural Ambassador flag:", user?.is_cultural_ambassador);
          console.log("New Joiner flag:", user?.new_joiner);


          if (!user) {
            setErrorMessage(`User with ID ${userId} not found`);
            setEmployee(null);
          } else {
            const isCulturalAmbassador =
              user.activity === "Cultural Ambassador" ||
              user.is_cultural_ambassador === "true" ||
              user.is_cultural_ambassador === true;
            const isNewJoiner =
              user.activity === "New Joiner" ||
              user.new_joiner === "true" ||
              user.new_joiner === true;
            const mappedData = {
              id: user.id || user.userId || "N/A",
              name: user.name || "Unknown",
              designation: user.designation || user.role || "Employee",
              is_cultural_ambassador: isCulturalAmbassador,
              new_joiner: isNewJoiner,
              email: user.user_principal_name || user.email || "<EMAIL>",
              department: user.department || "Unknown",
              extension: user.extension || "",
              thisMonth: parseFloat(user.monthly_rating) || 0,
              thisYear: parseFloat(user.yearly_rating) || 0,
              image: getImageUrl(user.profile || user.image, user.id || user.userId),
              rating: parseInt(user.rating) || 5,
            };
            console.log("Mapped data:", JSON.stringify(mappedData, null, 2));
            console.log("Final image URL (API):", mappedData.image);
            setEmployee(mappedData);
            setEditedName(mappedData.name);
            setEditedExtension(mappedData.extension);
            setEditedIsNewJoiner(mappedData.new_joiner);
            setEditedIsCulturalAmbassador(mappedData.is_cultural_ambassador);
            setEditedDepartment(mappedData.department);

          }
        }
      } catch (error) {
        console.error("Error fetching employee data:", error);
        setErrorMessage(`Failed to fetch employee data: ${error.message}`);
        setEmployee(null);
      }
      setLoading(false);
    };

    fetchEmployee();
  }, [userId, employeeData, session]);

  // Handle save changes
  const handleSave = async () => {
    if (!employee || !isAdmin) {
      setErrorMessage("Only admins can save changes.");
      return;
    }

    console.log("=== Saving Employee Changes ===");
    console.log("Original employee:", JSON.stringify(employee, null, 2));
    console.log("Edited values:", {
      name: editedName,
      extension: editedExtension,
      isNewJoiner: editedIsNewJoiner,
      isCulturalAmbassador: editedIsCulturalAmbassador,
      department: editedDepartment,
    });

    setLoading(true);
    setErrorMessage(null);
    setSuccessMessage(null);

    const updateData = {
      name: editedName,
      extension: editedExtension,
      is_cultural_ambassador: editedIsCulturalAmbassador,
      new_joiner: editedIsNewJoiner,
      department: editedDepartment,
    };

    console.log("Update data to send:", JSON.stringify(updateData, null, 2));

    try {
      const token = session?.accessToken || localStorage.getItem("jwtToken");
      const url = `${baseUrl}/v1/app-users/${employee.id}`;
      console.log("Update URL:", url);

      const response = await fetch(url, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(updateData),
      });

      console.log("Response status:", response.status);
      console.log("Response headers:", Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to update employee data (Status: ${response.status})`);
      }

      const result = await response.json();
      console.log("Update response:", JSON.stringify(result, null, 2));

      if (result.code === 200) {
        // Update employee state with the new values
        const updatedEmployee = {
          ...employee,
          name: editedName,
          extension: editedExtension,
          is_cultural_ambassador: editedIsCulturalAmbassador,
          new_joiner: editedIsNewJoiner,
          department: editedDepartment,
        };

        console.log("Updated employee data:", JSON.stringify(updatedEmployee, null, 2));
        setEmployee(updatedEmployee);
        setSuccessMessage("Employee data updated successfully!");

        // Update session if this is the current user
        if (session?.user?.id === userId) {
          await updateSession({
            name: editedName,
            profile: employee.image,
          });
        }

        setIsEditing(false);
      } else {
        throw new Error(result.message || "Invalid response from server");
      }
    } catch (error) {
      console.error("Error updating employee data:", error);
      setErrorMessage(`Failed to update employee: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle cancel edit
  const handleCancel = () => {
    setEditedName(employee.name);
    setEditedExtension(employee.extension);
    setEditedIsNewJoiner(employee.new_joiner);
    setEditedIsCulturalAmbassador(employee.is_cultural_ambassador);
    setEditedDepartment(employee.department);
    setIsEditing(false);
  };

  if (status === "loading") {
    return (
      <Dashboard>
        <Box py={2} px={4}>
          <Typography variant="h6" textAlign="center">
            Loading session...
          </Typography>
        </Box>
      </Dashboard>
    );
  }

  if (loading) {
    return (
      <Dashboard>
        <Box py={2} px={4}>
          <Typography variant="h6" textAlign="center">
            Loading...
          </Typography>
        </Box>
      </Dashboard>
    );
  }

  if (errorMessage || !employee) {
    return (
      <Dashboard>
        <Box py={2} px={4}>
          <Alert severity="error">{errorMessage || "Employee not found."}</Alert>
        </Box>
      </Dashboard>
    );
  }

  return (
    <Dashboard>
      <Box py={2} px={4}>
        <Typography variant="h6" color="textSecondary" gutterBottom>
          Rate and Review Employee
        </Typography>
        <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom={2}>
          <Typography variant="h4" fontWeight={600} gutterBottom>
            Employee Profile
          </Typography>
          <Box>
            <Button
              variant="contained"
              color="primary"
              Trips and Falls
              sx={{ backgroundColor: "#A665E1 !important", mt: 1, mr: 2 }}
              onClick={handleReviewClick}
            >
              Add Reviews
            </Button>
            {isAdmin && !isEditing && (
              <Button
                variant="outlined"
                color="secondary"
                sx={{ mt: 1 }}
                onClick={() => setIsEditing(true)}
              >
                Edit
              </Button>
            )}
            {isAdmin && isEditing && (
              <>
                <Button
                  variant="contained"
                  color="primary"
                  sx={{ backgroundColor: "#A665E1 !important", mt: 1, mr: 2 }}
                  onClick={handleSave}
                  disabled={loading}
                >
                  Save
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  sx={{ mt: 1 }}
                  onClick={handleCancel}
                  disabled={loading}
                >
                  Cancel
                </Button>
              </>
            )}
          </Box>
        </Box>
        {successMessage && (
          <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccessMessage(null)}>
            {successMessage}
          </Alert>
        )}
        {errorMessage && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setErrorMessage(null)}>
            {errorMessage}
          </Alert>
        )}

        <Grid container spacing={4}>
          <Grid item xs={12} md={4}>
            <Box>
              <Avatar
                src={employee.image}
                alt={employee.name}
                sx={{ width: 300, height: 350, borderRadius: 2 }}
                variant="rounded"
                onError={(e) => {
                  console.error("Image failed to load:", employee.image);
                  e.target.src = `/profile${employee.id}.png`;
                }}
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={8}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  label="Employee ID"
                  value={employee.id}
                  fullWidth
                  disabled
                  size="small"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="User Name"
                  value={editedName}
                  fullWidth
                  disabled={!isAdmin || !isEditing}
                  size="small"
                  onChange={(e) => setEditedName(e.target.value)}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Designation"
                  value={employee.designation}
                  fullWidth
                  disabled
                  size="small"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Department"
                  value={editedDepartment}
                  fullWidth
                  disabled={!isAdmin || !isEditing}
                  size="small"
                  onChange={(e) => setEditedDepartment(e.target.value)}
                />
              </Grid>
              <Grid item xs={12}>
                {isAdmin && isEditing ? (
                  <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Activity
                    </Typography>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={!!editedIsNewJoiner}
                          onChange={(e) => setEditedIsNewJoiner(e.target.checked)}
                          size="small"
                          sx={{ color: " #9b9696ff" }}
                        />
                      }
                      label="New Joiner"
                    />
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={!!editedIsCulturalAmbassador}
                          onChange={(e) => setEditedIsCulturalAmbassador(e.target.checked)}
                          size="small"
                          sx={{ color: " #9b9696ff" }}
                        />
                      }
                      label="Cultural Ambassador"
                    />
                  </Box>
                ) : (
                  <TextField
                    label="Activity"
                    value={
                      [
                        employee.new_joiner ? "New Joiner" : "",
                        employee.is_cultural_ambassador ? "Cultural Ambassador" : "",
                      ]
                        .filter(Boolean)
                        .join(", ") || "None"
                    }
                    fullWidth
                    disabled
                    size="small"
                  />
                )}
              </Grid>

              <Grid item xs={12}>
                <TextField
                  label="Email"
                  value={employee.email}
                  fullWidth
                  disabled
                  size="small"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Extension"
                  value={editedExtension}
                  fullWidth
                  disabled={!isAdmin || !isEditing}
                  size="small"
                  onChange={(e) => setEditedExtension(e.target.value)}
                />
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ display: "flex", justifyContent: "space-around" }}>
                  <Box textAlign="center">
                    <Typography fontSize={24} fontWeight={700}>
                      {employee.thisMonth}
                      <Typography component="span" fontSize={12}>/This month</Typography>
                    </Typography>
                    <Rating value={employee.rating} readOnly size="medium" />
                  </Box>
                  <Box textAlign="center">
                    <Typography fontSize={24} fontWeight={700}>
                      {employee.thisYear >= 0 ? employee.thisYear : "N/A"}
                      <Typography component="span" fontSize={12}>/This year</Typography>
                    </Typography>
                    <Rating value={employee.rating} readOnly size="medium" />
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <RatePopup
          open={openThankYouModal}
          onClose={handleCloseThankYou}
          employeeId={employee.id}
          profileImage={employee.image}
          employeeName={employee.name}
        />
      </Box>
    </Dashboard>
  );
};

export default EmployeeProfile;