import { ApolloClient, InMemoryCache, createHttpLink, from } from "@apollo/client";
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import { getSession } from "next-auth/react";

 //export const baseUrl = "http://*************:3001/v1";
//export const baseUrl = "https://portal.hassana.com.sa/v1";
//export const baseUrl = "https://hassana-api.360xpertsolutions.com/v1";
export const baseUrl = "http://localhost:3001";
//export const baseUrl = "https://hassana-api.360xpertsolutions.com";
//export const baseUrl = "https://v2-portal.hassana.com.sa";
//export const base_url = "https://v2-portal.hassana.com.sa/v1";
//export const base_url = "https://localhost:3001/v1";

const httpLink = createHttpLink({
  uri: baseUrl + "/graphql",
});

// Auth link to add JWT token to headers
const authLink = setContext(async (_, { headers }) => {
  // Get token from session or localStorage
  let token = null;

  try {
    const session = await getSession();
    token = session?.accessToken;
  } catch (error) {
    console.warn("Could not get session:", error);
  }

  // Fallback to localStorage if session token not available
  if (!token && typeof window !== 'undefined') {
    token = localStorage.getItem("jwtToken");
  }

  return {
    headers: {
      ...headers,
      ...(token && { authorization: `Bearer ${token}` }),
    }
  };
});

// Error link to handle GraphQL and network errors
const errorLink = onError(({ graphQLErrors, networkError }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path, extensions }) => {
      console.error(
        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
      );

      // Handle authentication errors
      if (extensions?.code === 'UNAUTHENTICATED' || message.includes('Unauthorized') || message.includes('Authorization header not found')) {
        console.warn('Authentication error detected, redirecting to login...');
        // Clear invalid token
        if (typeof window !== 'undefined') {
          localStorage.removeItem("jwtToken");
        }
        // Redirect to login page
        if (typeof window !== 'undefined') {
          window.location.href = '/login?login=false';
        }
      }

      // Handle authorization errors
      if (extensions?.code === 'FORBIDDEN' || message.includes('Access denied') || message.includes('Forbidden')) {
        console.warn('Authorization error detected');
        // You can show a toast or redirect to unauthorized page
        if (typeof window !== 'undefined') {
          // You can implement a toast notification here
          console.error('Access denied: Insufficient permissions');
        }
      }
    });
  }

  if (networkError) {
    console.error(`[Network error]: ${networkError}`);

    // Handle network errors that might indicate auth issues
    if (networkError.statusCode === 401) {
      console.warn('Network 401 error, clearing token and redirecting...');
      if (typeof window !== 'undefined') {
        localStorage.removeItem("jwtToken");
        window.location.href = '/login?login=false';
      }
    }
  }
});

const client = new ApolloClient({
  link: from([
    errorLink,
    authLink,
    httpLink
  ]),
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all'
    },
    query: {
      errorPolicy: 'all'
    }
  }
});

export default client;
