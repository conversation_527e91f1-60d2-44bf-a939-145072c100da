import { ApolloClient, InMemoryCache, createHttpLink } from "@apollo/client";

 //export const baseUrl = "http://*************:3001/v1";
//export const baseUrl = "https://portal.hassana.com.sa/v1";
//export const baseUrl = "https://hassana-api.360xpertsolutions.com/v1";
export const baseUrl = "http://localhost:3001";
//export const baseUrl = "https://hassana-api.360xpertsolutions.com";
//export const baseUrl = "https://v2-portal.hassana.com.sa";
//export const base_url = "https://v2-portal.hassana.com.sa/v1";
//export const base_url = "https://localhost:3001/v1";


const httpLink = createHttpLink({
  uri: baseUrl + "/graphql",
});

const client = new ApolloClient({
  link: httpLink,
  cache: new InMemoryCache(),

  onError: ({ operation, networkError, response }) => {
    console.log(
      `[GraphQL Error]: Operation: ${operation.operationName}, Message: ${networkError?.message}, Response: `,
      response
    );
  },
});

export default client;
