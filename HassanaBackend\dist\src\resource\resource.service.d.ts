import { Repository } from 'typeorm';
import { CreateResourceInput } from './dto/create-resource.input';
import { UpdateResourceInput } from './dto/update-resource.input';
import { Resource } from './entities/resource.entity';
import { UUID } from 'crypto';
export declare class ResourceService {
    private resourceRepository;
    constructor(resourceRepository: Repository<Resource>);
    create(createResourceInput: CreateResourceInput): Promise<Resource>;
    findAll(): Promise<Resource[]>;
    findOne(id: UUID): Promise<Resource>;
    update(id: UUID, updateResourceInput: UpdateResourceInput): Promise<Resource>;
    remove(id: UUID): Promise<Resource>;
}
