{"c": ["pages/adminOffer", "pages/index", "webpack"], "r": ["pages/adminOffer", "/_error"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=F%3A%5CProjects%5CHBack%5CHassana-Fronted%5Csrc%5Cpages%5CadminOffer.js&page=%2FadminOffer!", "./src/pages/adminOffer.js", "__barrel_optimize__?names=<PERSON><PERSON>,<PERSON>,<PERSON><PERSON>,CircularProgress,Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,Grid,IconButton,Paper,Table,TableBody,TableCell,TableHead,TablePagination,TableRow,TextField,Typography!=!./node_modules/@mui/material/index.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=F%3A%5CProjects%5CHBack%5CHassana-Fronted%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!"]}