import { DynamicModule, OnApplicationShutdown } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { RedisModuleOptions, RedisModuleAsyncOptions } from './interfaces';
export declare class RedisModule implements OnApplicationShutdown {
    private moduleRef;
    constructor(moduleRef: ModuleRef);
    /**
     * Registers the module synchronously.
     *
     * @param options - The module options
     * @param isGlobal - Register in the global scope
     * @returns A DynamicModule
     */
    static forRoot(options?: RedisModuleOptions, isGlobal?: boolean): DynamicModule;
    /**
     * Registers the module asynchronously.
     *
     * @param options - The async module options
     * @param isGlobal - Register in the global scope
     * @returns A DynamicModule
     */
    static forRootAsync(options: RedisModuleAsyncOptions, isGlobal?: boolean): DynamicModule;
    onApplicationShutdown(): Promise<void>;
}
