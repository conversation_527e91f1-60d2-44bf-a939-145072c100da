"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LibraryService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const library_entity_1 = require("./entities/library.entity");
const typeorm_2 = require("typeorm");
const path = require("path");
const fs_1 = require("fs");
let LibraryService = class LibraryService {
    constructor(libraryRepository) {
        this.libraryRepository = libraryRepository;
    }
    async create(createLibraryInput) {
        const newFile = this.libraryRepository.upsert({
            ...createLibraryInput
        }, { conflictPaths: ["file_name"] });
        return (await newFile).raw;
    }
    async findAll() {
        const result = await this.libraryRepository.find();
        result.forEach((obj, index, array) => {
            array[index] = {
                ...obj,
                file_path: `${process.env.SERVER_url}/${obj.file_path}`
            };
        });
        return result;
    }
    async remove(id) {
        const find = await this.libraryRepository.findOne({ where: { id } });
        const filePath = path.join(__dirname, "../../../library/v1/" + find.file_path);
        console.log(filePath);
        if (!find)
            throw Error("library not found");
        if ((0, fs_1.existsSync)(filePath)) {
            console.log("deleting file in server ...: " + filePath);
            (0, fs_1.unlinkSync)(filePath);
        }
        await this.libraryRepository.remove(find);
        return;
    }
};
exports.LibraryService = LibraryService;
exports.LibraryService = LibraryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(library_entity_1.Library)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], LibraryService);
//# sourceMappingURL=library.service.js.map