"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mergedOptionsProvider = exports.clusterClientsProvider = exports.createAsyncOptionsProvider = exports.createAsyncOptions = exports.createAsyncProviders = exports.createOptionsProvider = void 0;
const cluster_constants_1 = require("./cluster.constants");
const common_1 = require("./common");
const default_options_1 = require("./default-options");
const createOptionsProvider = (options) => ({
    provide: cluster_constants_1.CLUSTER_OPTIONS,
    useValue: options
});
exports.createOptionsProvider = createOptionsProvider;
const createAsyncProviders = (options) => {
    if (options.useClass) {
        return [
            {
                provide: options.useClass,
                useClass: options.useClass
            },
            (0, exports.createAsyncOptionsProvider)(options)
        ];
    }
    if (options.useExisting || options.useFactory)
        return [(0, exports.createAsyncOptionsProvider)(options)];
    return [];
};
exports.createAsyncProviders = createAsyncProviders;
const createAsyncOptions = async (optionsFactory) => {
    return await optionsFactory.createClusterOptions();
};
exports.createAsyncOptions = createAsyncOptions;
const createAsyncOptionsProvider = (options) => {
    if (options.useFactory) {
        return {
            provide: cluster_constants_1.CLUSTER_OPTIONS,
            useFactory: options.useFactory,
            inject: options.inject
        };
    }
    if (options.useClass) {
        return {
            provide: cluster_constants_1.CLUSTER_OPTIONS,
            useFactory: exports.createAsyncOptions,
            inject: [options.useClass]
        };
    }
    if (options.useExisting) {
        return {
            provide: cluster_constants_1.CLUSTER_OPTIONS,
            useFactory: exports.createAsyncOptions,
            inject: [options.useExisting]
        };
    }
    return {
        provide: cluster_constants_1.CLUSTER_OPTIONS,
        useValue: {}
    };
};
exports.createAsyncOptionsProvider = createAsyncOptionsProvider;
exports.clusterClientsProvider = {
    provide: cluster_constants_1.CLUSTER_CLIENTS,
    useFactory: (options) => {
        const clients = new Map();
        if (Array.isArray(options.config)) {
            options.config.forEach(item => clients.set(item.namespace ?? cluster_constants_1.DEFAULT_CLUSTER, (0, common_1.createClient)(item, { readyLog: options.readyLog, errorLog: options.errorLog })));
        }
        else if (options.config) {
            clients.set(options.config.namespace ?? cluster_constants_1.DEFAULT_CLUSTER, (0, common_1.createClient)(options.config, { readyLog: options.readyLog, errorLog: options.errorLog }));
        }
        return clients;
    },
    inject: [cluster_constants_1.CLUSTER_MERGED_OPTIONS]
};
exports.mergedOptionsProvider = {
    provide: cluster_constants_1.CLUSTER_MERGED_OPTIONS,
    useFactory: (options) => ({ ...default_options_1.defaultClusterModuleOptions, ...options }),
    inject: [cluster_constants_1.CLUSTER_OPTIONS]
};
