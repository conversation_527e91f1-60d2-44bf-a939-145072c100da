"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OffersResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const offers_service_1 = require("./offers.service");
const offers_schema_1 = require("./schema/offers.schema");
const create_offer_dto_1 = require("./dto/create-offer.dto");
const update_offer_dto_1 = require("./dto/update-offer.dto");
const common_1 = require("@nestjs/common");
const jwt_guard_1 = require("../auth/jwt.guard");
const typeorm_1 = require("typeorm");
let OffersResolver = class OffersResolver {
    constructor(offerService) {
        this.offerService = offerService;
    }
    async createOffer(createOffersInput) {
        try {
            console.log(createOffersInput, "thisvsjkvs");
            const offerExists = await this.offerService.findOne({ status: true, code: createOffersInput.code });
            if (offerExists)
                throw Error("Sorry! your input code is already registered");
            const offer = await this.offerService.create({ ...createOffersInput, status: true });
            return offer;
        }
        catch (error) {
            console.log(error.message);
            throw Error(error);
        }
    }
    async offerView(offer_id, user_id) {
        await this.offerService.createOfferView(offer_id, user_id);
        return await this.offerService.findOne(offer_id);
    }
    async findAll(user_id) {
        return await this.offerService.findAll(user_id, {});
    }
    async findValidOffers(user_id) {
        const now = new Date();
        return await this.offerService.findAll(user_id, { expiry_date: (0, typeorm_1.MoreThan)(now) });
    }
    updateOffer(id, updateOffersInput) {
        return this.offerService.update(id, updateOffersInput);
    }
    removeOffer(id) {
        return this.offerService.remove(id);
    }
};
exports.OffersResolver = OffersResolver;
__decorate([
    (0, graphql_1.Mutation)(() => offers_schema_1.OffersSchema),
    __param(0, (0, graphql_1.Args)('createOffersInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_offer_dto_1.CreateOffersInput]),
    __metadata("design:returntype", Promise)
], OffersResolver.prototype, "createOffer", null);
__decorate([
    (0, graphql_1.Mutation)(() => offers_schema_1.OffersSchema),
    __param(0, (0, graphql_1.Args)('offer_id', { type: () => String })),
    __param(1, (0, graphql_1.Args)('user_id', { type: () => String })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], OffersResolver.prototype, "offerView", null);
__decorate([
    (0, graphql_1.Query)(() => [offers_schema_1.OffersSchema], { name: 'offers' }),
    __param(0, (0, graphql_1.Args)('user_id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OffersResolver.prototype, "findAll", null);
__decorate([
    (0, graphql_1.Query)(() => [offers_schema_1.OffersSchema], { name: 'validOffers' }),
    __param(0, (0, graphql_1.Args)('user_id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], OffersResolver.prototype, "findValidOffers", null);
__decorate([
    (0, graphql_1.Mutation)(() => offers_schema_1.OffersSchema),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __param(1, (0, graphql_1.Args)('updateOffersInput')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_offer_dto_1.UpdateOffersInput]),
    __metadata("design:returntype", void 0)
], OffersResolver.prototype, "updateOffer", null);
__decorate([
    (0, graphql_1.Mutation)(() => offers_schema_1.OffersSchema),
    __param(0, (0, graphql_1.Args)('id', { type: () => graphql_1.ID })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], OffersResolver.prototype, "removeOffer", null);
exports.OffersResolver = OffersResolver = __decorate([
    (0, common_1.UseGuards)(jwt_guard_1.JwtGuard),
    (0, graphql_1.Resolver)(() => offers_schema_1.OffersSchema),
    __metadata("design:paramtypes", [offers_service_1.OffersService])
], OffersResolver);
//# sourceMappingURL=offers.resolver.js.map