import { CreateEventInput } from './dto/create-event.input';
import { UpdateEventInput } from './dto/update-event.input';
import { Event } from './entities/event.entity';
import { Repository } from 'typeorm';
import { UUID } from 'crypto';
export declare class EventService {
    private readonly eventRepository;
    constructor(eventRepository: Repository<Event>);
    create(createEventInput: CreateEventInput): Promise<Event>;
    findAll(): Promise<Event[]>;
    findOne(id: UUID): Promise<Event>;
    findTodaysEvent(date: Date, category: string): Promise<Event[]>;
    update(id: UUID, updateEventInput: UpdateEventInput): Promise<Event>;
    remove(id: UUID): Promise<Event | null>;
}
