import Redis from 'ioredis';
export declare class SessionService {
    private readonly redis;
    private readonly logger;
    constructor(redis: Redis);
    createUserSession(userId: string, tokenPayload: any, deviceInfo?: any): Promise<string>;
    validateUserSession(userId: string, sessionId: string, token: string): Promise<boolean>;
    logoutUser(userId: string, token: string): Promise<void>;
    getUserSession(userId: string): Promise<any>;
    forceLogoutUser(userId: string): Promise<void>;
    private blacklistToken;
    private isTokenBlacklisted;
    private generateSessionId;
    getAllActiveSessions(): Promise<any[]>;
}
